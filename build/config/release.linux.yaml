# BrowserOS Linux Release Build Configuration
build:
  type: release
  architecture: x64  # Linux x64 only
  # architectures: [x64] # Single architecture for Linux
  universal: false # Linux doesn't support universal binaries

gn_flags:
  file: build/config/gn/flags.linux.release.gn

steps:
  clean: true
  git_setup: true
  apply_patches: true
  build: true
  sign: false  # Linux doesn't require code signing
  package: true

paths:
  root_dir: .
  # chromium_src: ../chromium-src

# Environment-specific settings
env:
  PYTHONPATH: scripts

# Linux-specific settings
linux:
  appimage:
    compression: gzip  # Compression type for AppImage
    architecture: x86_64  # AppImage architecture designation

# Notification settings
notifications:
  slack: true # Enable Slack notifications for release builds
