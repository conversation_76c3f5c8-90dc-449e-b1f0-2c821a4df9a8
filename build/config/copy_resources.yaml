# Copy resources configuration for Nxtscape build
# This file defines all copy operations for resources during the build process
#
# Build Type Conditional Operations:
# - Use 'build_type' field to specify when an operation should run
# - Supported build types: debug, release, dev, prod
# - Operations without build_type run for all builds
# - debug/dev = development builds, release/prod = production builds

copy_operations:
  # Extensions
  # - name: "AI Side Panel Extension"
  #   source: "resources/files/ai_side_panel"
  #   destination: "chrome/browser/resources/ai_side_panel"
  #   type: "directory"

  # - name: "Bug Reporter Extension"
  #   source: "resources/files/bug_reporter"
  #   destination: "chrome/browser/resources/bug_reporter"
  #   type: "directory"

  # Icons - General
  - name: "Product Logo Icons"
    source: "resources/icons/*.png"
    destination: "chrome/app/theme/chromium/"
    type: "files"

  - name: "Product Logo AI Files"
    source: "resources/icons/*.ai"
    destination: "chrome/app/theme/chromium/"
    type: "files"

  - name: "Product Logo SVG Files"
    source: "resources/icons/*.svg"
    destination: "chrome/app/theme/chromium/"
    type: "files"

  # Icons - Platform specific
  - name: "ChromeOS Icons"
    source: "resources/icons/chromeos"
    destination: "chrome/app/theme/chromium/chromeos"
    type: "directory"

  - name: "Linux Icons"
    source: "resources/icons/linux"
    destination: "chrome/app/theme/chromium/linux"
    type: "directory"

  - name: "Mac Icons"
    source: "resources/icons/mac"
    destination: "chrome/app/theme/chromium/mac"
    type: "directory"

  - name: "Windows Icons"
    source: "resources/icons/win"
    destination: "chrome/app/theme/chromium/win"
    type: "directory"

  # Icons - DPI specific
  - name: "Product Logo Name 22 - 100%"
    source: "resources/icons/product_logo_name_22.png"
    destination: "chrome/app/theme/default_100_percent/chromium/product_logo_name_22.png"
    type: "file"

  - name: "Product Logo Name 22 White - 100%"
    source: "resources/icons/product_logo_name_22_white.png"
    destination: "chrome/app/theme/default_100_percent/chromium/product_logo_name_22_white.png"
    type: "file"

  - name: "Product Logo Name 22 - 200%"
    source: "resources/icons/product_logo_name_22_2x.png"
    destination: "chrome/app/theme/default_200_percent/chromium/product_logo_name_22.png"
    type: "file"

  - name: "Product Logo Name 22 White - 200%"
    source: "resources/icons/product_logo_name_22_white_2x.png"
    destination: "chrome/app/theme/default_200_percent/chromium/product_logo_name_22_white.png"
    type: "file"

  - name: "Product Logo 16 - 100%"
    source: "resources/icons/product_logo_16.png"
    destination: "chrome/app/theme/default_100_percent/chromium/product_logo_16.png"
    type: "file"

  - name: "Product Logo 32 - 100%"
    source: "resources/icons/product_logo_32.png"
    destination: "chrome/app/theme/default_100_percent/chromium/product_logo_32.png"
    type: "file"

  - name: "Product Logo 16 - 200%"
    source: "resources/icons/product_logo_32.png"
    destination: "chrome/app/theme/default_200_percent/chromium/product_logo_16.png"
    type: "file"

  - name: "Product Logo 32 - 200%"
    source: "resources/icons/product_logo_64.png"
    destination: "chrome/app/theme/default_200_percent/chromium/product_logo_32.png"
    type: "file"
