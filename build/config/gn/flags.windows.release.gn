# Windows Release Build Configuration

use_siso=false
is_debug = false
is_component_build = false
is_official_build = true
symbol_level = 0
is_clang=true
chrome_pgo_phase = 0
proprietary_codecs=true

enable_widevine=true
exclude_unwind_tables=true
google_api_key=""
google_default_client_id=""
google_default_client_secret=""
treat_warnings_as_errors=false
use_official_google_api_keys=false
use_unofficial_version_number=false

target_cpu="x64"
use_sysroot=false
blink_symbol_level=0
v8_symbol_level=0
symbol_level=0

ffmpeg_branding="Chrome"

# OLD

# use_siso=false
# is_debug = false
# is_component_build = false
# is_official_build = true
# symbol_level = 0
# target_cpu = "x64"
# proprietary_codecs = true
# ffmpeg_branding = "Chrome"
# google_api_key=""
# google_default_client_id=""
# google_default_client_secret=""
# chrome_pgo_phase = 0

