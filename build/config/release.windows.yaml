# Nxtscape Windows Release Build Configuration
build:
  type: release
  architecture: x64  # Windows default architecture
  # No universal builds on Windows

gn_flags:
  file: build/config/gn/flags.windows.release.gn

steps:
  clean: true
  git_setup: true
  apply_patches: true
  build: true
  sign: false  # Windows signing requires certificate
  package: true

paths:
  root_dir: .
  # chromium_src: C:\Users\<USER>\chromium\src

# Environment-specific settings
env:
  PYTHONPATH: scripts

# Signing configuration (optional - requires code signing certificate)
signing:
  # certificate_name: "Your Company Name"  # Certificate subject name
  # Or use environment variable:
  # require_env_vars:
  #   - WINDOWS_CERTIFICATE_NAME

# Notification settings
notifications:
  slack: true  # Enable Slack notifications for release builds

# Build options
build_options:
  # Build mini_installer for creating installer package
  build_mini_installer: true
  # Create both installer and portable ZIP
  create_portable: true
  create_installer: true
