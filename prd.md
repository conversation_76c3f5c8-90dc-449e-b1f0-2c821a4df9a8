# 综合产品与设计架构文档 (v1.4)

**产品名称：** 电商需求洞察应用
**版本：** 1.4
**更新日期：** 2025年7月22日

## 1.0 宏观战略与定位

### 1.1 背景 (Background)
当前电商市场竞争激烈，流量红利见顶，野蛮增长的时代已经过去。新的商业增长点更多来自于对细分市场和特定用户场景的深度挖掘。

### 1.2 项目目标 (Goals)
本产品旨在通过自动化、智能化的方式，解决电商卖家在市场调研中面临的效率低下、洞察不足的痛点。我们通过爬虫技术收集海量真实用户评论，并利用AI进行深度分析，最终为电商卖家提供精准、可执行的新产品开发方向和市场切入点，帮助他们在红海中找到蓝海。

### 1.3 目标用户 (Target Audience)
* **角色:** 在成熟类目（如服装、家居、3C配件等）深耕多年的资深电商卖家，通常为中小型企业。
* **团队:** 拥有专门的产品开发和运营团队。
* **核心痛点:** 团队具备执行能力，但缺乏专业的数据分析能力和高效的技术工具使用经验，难以从海量市场信息中提炼出有效的商业洞察。

### 1.4 产品范围 (Scope)
#### 1.4.1 核心功能 (In Scope)
1.  **需求输入:** 用户可通过**上传关键词表(Excel/CSV)**或**手动输入核心关键词**的方式，启动分析任务。
2.  **任务监控:** 用户可以实时查看所有分析任务的状态。
3.  **报告查阅:** 用户可以查看由AI生成的、图文并茂的“未被满足的需求”洞察报告。

#### 1.4.2 暂不实现 (Out of Scope)
* **数据源:** 目前仅支持**淘宝**和**小红书**，暂不包含京东、抖音等其他平台。
* **语言:** 仅支持**中文**市场分析。
* **高级功能:** 暂不包含复杂的用户权限管理、团队协作等功能。

### 1.5 商业模式

#### 1.5.1 核心理念
产品的核心价值在于通过AI技术为用户节省时间、降低成本并提供更深度的市场洞察。因此，我们将AI处理能力作为核心的付费点。

模式分为：深度扫描模式和快速扫描模式，默认是深度扫描模式。对了你看看我们是不是开有一个上传关键词表（上传附件，支持 excel、CSV、xls等类表格格式）的组件。

#### 1.5.2 扫描模式定义
快速扫描模式下：
1. 用户只能手动输入关键词，每个关键词之间以“、” 或 “，” 或 “,”隔开。
2. 上传附件是半透明状态，当用户鼠标悬停提示“上传关键词表只能用于深度扫描模式”。
3. 不进行关键词拓展和跳过AI关键词预处理。
4. 启动爬虫进行淘宝和小红书链接发现 -> 去重 -> 评论采集 -> AI分析（MapReduce）”的智能流程。

深度扫描模式下：
1. 用户可以手动输入关键词，每个关键词之间以“、” 或 “，” 或 “,”隔开。
2. 用户也可以上传附件，后台解析为关键词组。
3. 当用户输入或者上传解析后的关键词少于 5 个，则对用户输入或者上传的每一个关键词输入淘宝主页搜索框，获取淘宝提供的下拉词，之后将所有的输入关键词、上传关键词、淘宝下拉词连同程序中设定的“聚类提示词”通过 API 传输给ai；
4. 当用户输入或者上传解析后的关键词不少于 5 个，则直接将所有的输入关键词、上传关键词连同程序中设定的“聚类提示词”通过 API 传输给ai；
5. ai 返回关键词给用户确认；
6. 启动爬虫进行淘宝和小红书链接发现 -> 去重 -> 评论采集 -> AI分析（MapReduce）”的智能流程。
7. 链接发现是指：对用户确认后的每一个关键词，进行淘宝抓取“销量“”排序下**前 44 个（第一页）**的商品链接，记录每个链接的销量；小红书抓取“最热”排序下**前 50 个**的帖子链接，记录每个链接的点赞数。
8. 去重是指：将链接发现这一步抓取到的所有链接进行去重，然后按照销量对每个平台分别保留数据靠前的 50 个链接。
9. 评论采集是指：对去重后的每一个链接进行评论的抓取。

#### 1.5.3 付费与体验方案
* **体验版:**
    * 新注册用户获得**免费额度**，可用于 **2次深度扫描** 或 **3次快速扫描**。
    * 额度可组合使用，例如兑换成“1次深度扫描 + 1次快速扫描”。
    * **换算关系:** `1次深度扫描` 消耗2点数，`1次快速扫描` 消耗1点数（为简化工程实现，调整换算关系），新用户共 **3点数** 体验额度。
* **付费版:**
    * 提供不同档位的**扫描次数包**供用户购买。
    * 套餐包内的次数同样遵循换算关系，用户可自由组合使用。


### 1.6 版本背景

本产品基于 Next.js，通过 Electron 封装客户端应用，具备完整的用户交互界面、关键词分析任务流与 AI 洞察逻辑，架构依赖如下：

- 使用 Playwright MCP Server 抓取淘宝与小红书数据；
- 使用 OpenAI Agents SDK 自动发现/拉起；
- 使用 Stripe 作为 SaaS 收费入口；
- 使用 本地方案 作为数据存储；
- 使用 火山引擎 作为 AI 分析能力服务商。
- 后期配置远程管理平台，用于用户账号管理、账户充值管理、AI 服务商配置管理。

---

## 2.0 核心功能与页面蓝图

### 2.1 模块一：需求输入页
* **页面目标:** 引导用户选择扫描模式，并通过手动输入或上传文件的方式，高效启动一个精准的分析任务。
* **页面路由:** `/app`

#### 2.1.0 应用启动登录检测
* **功能说明:** 应用启动时自动进行平台登录状态检测，避免任务执行时的登录中断。
* **检测流程:**
  1. **静默检测**: 应用启动后在后台静默检测淘宝和小红书的登录状态
  2. **状态展示**: 在页面顶部或侧边栏显示登录状态指示器
    - 🟢 已登录：显示绿色图标
    - 🔴 未登录：显示红色图标 + "点击登录"按钮
    - 🟡 检测中：显示黄色图标 + 加载动画
  3. **用户引导**: 如果检测到未登录，显示友好提示："为确保任务顺利执行，请先登录淘宝/小红书"
  4. **一键登录**: 点击登录按钮后，启动PRD 10.0章节定义的引导式登录流程

#### 2.1.1 页面布局
* **说明:** 使用现有布局，包含左侧侧边栏 (`Sidebar`) 和顶部导航栏 (`Header`)，保持应用体验一致性。
* **新增组件:** 在顶部导航栏添加平台登录状态指示器 (`PlatformLoginStatus`)

#### 2.1.2 页面核心组件
* **组件名称:** `PromptInputForm`
    1.  **“扫描模式”选项:**
        * **“深度扫描”** (默认选中)
        * 每个选项旁边可显示“消耗X点数”的提示。
    2.  **智能关键词输入方式:** 提供手动输入和文件上传两种方式，支持精确识别。
        * **手动输入**: 支持多种分隔符（"、" "，" "," " " 换行符）自动识别关键词
        * **文件上传**: 支持Excel(.xlsx, .xls)、CSV(.csv)格式，自动解析表格中的关键词
        * **实时预览**: 输入或上传后，实时显示解析出的关键词列表供用户确认
        * **关键词验证**: 自动去重、去空格、过滤无效字符
    3.  **高级选项（可选）:** 在一个可折叠的区域内，提供“排除词”输入框。

* **交互流程:**
    1.  用户选择扫描模式。
    2.  用户上传文件或手动输入关键词。
    3.  点击“开始分析”按钮。
    4.  前端将**扫描模式**和**关键词数据**一同提交给 `POST /api/start-task`。
    5.  API立即返回 `jobId`，前端跳转到 `/dashboard/[jobId]` 并开始轮询。

### 2.2 模块二：任务管理与监控页
* **页面目标:** 集中展示任务列表，并实时反馈具体任务的执行进展。
* **页面路由:** `/dashboard/[id]`

#### 2.2.2 任务历史列表 (侧边栏)
* **组件名称:** `TaskList`
* **说明:** `ChatItem` 中显示用户上传的文件名或手动输入的主关键词。

#### 2.2.3 任务实时监控器 (主内容区)
* **组件名称:** `TaskMonitor` 
* **组件结构:**
    * **左侧对话框状态:** 实时展示 ai 思考内容、返回内容和 mcp 工具调用的完整过程。

    * **右侧扩展状态:** 扩展其内部的 `status` 逻辑，以匹配后端异步任务流，并提供更精细的视觉反馈，页面采用向下瀑布流展现方式，节点对应后台进程：
        * `排队中`: 图标呼吸灯动效提示。
        * `关键词处理中`: 紫色，AI智能体正在处理关键词扩展或聚类。
        * `AI分析中`: 紫色，搭配“AI/大脑思考”的动画。
        * `等待用户确认`: 特殊状态，提示用户在弹窗中确认AI处理后的关键词。
        * `链接发现中`: 蓝色，提示“正在发现商品链接，已找到 X 个...”。
        * `评论采集中`: 蓝色，图标呼吸灯提示“正在采集第 X / Y 个商品评论...”。
        * `AI分析中` : 紫色，图标呼吸灯搭配“AI/大脑思考”的动画。
        * `已完成`: **直接在此组件内部渲染最终的 `ReportContainer` 组件**。
        * `失败`: 红色，显示错误信息和“重试”按钮。
    * **数据驱动:** 组件的数据源是通过**轮询 `GET /api/task-status?id={jobId}`** API返回的状态和进度信息。

    * **右侧文件列表:** 用于存储整个过程中保存的资料文档。

    * **数据导出功能:** 在任务执行过程中和完成后，提供多格式数据导出
        * **导出按钮位置**: 右侧文件列表区域顶部
        * **支持格式**: Markdown(.md)、CSV(.csv)、Excel(.xlsx)
        * **导出内容**:
            - 链接发现数据：包含URL、销量/点赞数、平台、关键词等信息
            - 评论采集数据：包含链接、评论文本、采集时间等信息
            - AI分析数据：包含单品分析结果、Map-Reduce汇总结果
            - 最终洞察报告：完整的市场机会分析报告
        * **文件命名**: 自动生成带时间戳的文件名，如"电商分析_链接数据_20250127_143022.csv"

### 2.3 模块三：需求洞察报告页
* **页面目标:** 以生动、清晰的方式呈现AI分析结果，交付商业价值。
* **页面路由:** 无独立路由，作为 `TaskMonitor` 组件在 `completed` 状态下的渲染结果。

#### 2.3.1 报告容器
* **组件名称:** `ReportContainer`
* **组件结构:**
    1.  **`SummaryHeader` (摘要头部):** `h2` 或 `h3` 标题，用一段加粗的文本呈现AI总结的核心发现。
    2.  **`DemandKeywordsCloud` (需求关键词云图):** **新建组件**。可调研 `react-wordcloud` 等第三方库，或利用D3.js进行定制开发，用于直观展示高频需求词。
    3.  **`InsightCardList` (洞察卡片列表):** 循环渲染下方的 `InsightCard` 组件。

#### 2.3.2 洞察卡片
* **组件名称:** `InsightCard`
* **组件结构:**
    * **`CardHeader`**: 显示洞察标题，如“**需求一：需要一款真正无毒、无异味的母婴级瑜伽垫**”。
    * **`CardContent`**: 显示AI对该需求的详细文字解读。
    * **`CardFooter`**: 放置一个 `Accordion` 或 `Collapsible` 组件，默认收起，标题为“查看原始评论佐证”。点击后展开，内部展示3-5条相关的原始用户评论。

---

## 3.0 全局设计系统与规范

### 3.1 色彩规范 (Color Palette)
* **主基调 (背景):** `#FDFBF4` (温暖米色)
* **主品牌/强调色 (交互元素):** `#F59E0B` (活力琥珀色)
* **状态色:**
    * **成功:** `#65a30d` (绿色)
    * **失败/危险:** `#dc2626` (红色)
    * **处理中/信息:** 新增一个蓝色调，例如 `blue-500`。
* **迁移策略:** **复用并扩展** `tailwind.config.ts` 中的颜色定义。

### 3.2 字体系统 (Typography)
* **规范:**
    * **UI文本:** 系统默认无衬线字体。
    * **代码/特殊文本:** `JetBrains Mono`。

### 3.3 间距与网格 (Spacing & Grid)
* **规范:** 严格遵循 Tailwind CSS 的标准间距类 (如 `p-4`, `m-2`, `gap-x-8`)。

-----

## 4.0、关键点概览

### 4.1 系统架构

1. 保留原始 Web 架构（Next.js + Tailwind + 页面结构 + 所有 UI 组件）
2. 使用 Electron 封装为跨平台客户端（Windows / macOS）
3. 爬虫使用 Playwright MCP，用户自己登录抓取数据
4. 移除 Supabase，任务数据存储改为本地 JSON 或 SQLite
5. Stripe 替换为微信 Native 支付，完成 SaaS 模式的收入能力
6. 增加 SaaS 授权控制：登录、额度判断、扣除、支付接入、任务授权判断

| 模块     | 技术栈                | 功能说明                     |
| ------ | ------------------ | ------------------------ |
| 客户端封装  | Electron           | 打包成 `.exe` / `.dmg` 桌面程序 |
| UI 层   | Next.js + Tailwind | 完全保留原组件、页面结构             |
| 爬虫层    | Playwright MCP     | 模拟用户行为抓取评论数据         |
| 数据持久化  | JSON 文件 / SQLite   | 存储任务信息、评论缓存、报告内容         |
| LLM 分析 | 调用 OpenAI 接口（原有逻辑） | 保留原分析 prompt 与流程         |
| 登录认证   | 手机号 + 验证码（腾讯云短信）   | 生成 JWT，保存在客户端            |
| 额度管理   | 云函数 API            | 每次任务前校验权限，成功后扣除额度        |
| 支付系统   | 微信 Native 支付二维码    | 扫码完成充值，更新云端额度            |

---

### 4.2、任务流程

```text
[用户启动客户端] →
  [手机号登录] →
    [输入关键词 / 选择扫描模式] →
      [调用 check-quota 云 API] →
        └─ 不足 → 弹出微信扫码 → 刷新额度
        └─ 足够 → 执行 Playwright 抓取 →
             分析评论 → 生成报告 → 展示导出
```

---

### 4.3 AI智能体模块拆解

#### 4.3.1 关键词处理智能体（KeywordProcessorAgent）

**功能职责：**
- 关键词扩展：少于5个关键词时，调用淘宝API获取下拉词并AI扩展
- 关键词聚类：多于5个关键词时，进行智能聚类分析
- 用户确认：生成关键词列表供用户最终确认

**输入：** 用户输入的关键词或上传的Excel/CSV文件
**输出：** 经过处理和用户确认的最终关键词列表

#### 4.3.2 电商爬虫智能体（EcommerceCrawlerAgent）

**三阶段执行流程：**

**阶段一：链接发现**
- 对每个关键词并行访问淘宝搜索页（销量排序，前44个商品）
- 对每个关键词并行访问小红书搜索页（最热排序，前50个帖子）
- 记录每个链接的销量/点赞数据

**阶段二：去重筛选**
- 淘宝链接去重，按销量保留前50个
- 小红书链接去重，按点赞数保留前50个
- 输出最终筛选的100个高质量链接

**阶段三：轮询式评论采集**
- 对筛选后的每个链接依次进行轮询抓取
- 访问商品/帖子页面，智能定位评论区域
- 滚动加载更多评论，确保获取足够样本量
- 提取评论文本信息，过滤图片、视频等非文本内容
- 每个链接完成后再处理下一个，避免并发请求被检测

**AI驱动优势：**
- 自动适应页面结构变化
- 智能处理反爬虫机制
- 遇到验证码自动请求人工协助

#### 4.3.3 评论分析智能体（CommentAnalyzerAgent）

**Map-Reduce分析模式：**

**Map阶段：** 对每个商品/帖子的评论进行单独分析
- 提取产品主要优点（最多3个）
- 识别产品主要缺点（最多3个）
- 发现潜在机会点（最多2个未满足需求）
- 过滤物流、客服等非产品相关内容

**Reduce阶段：** 汇总所有单品分析结果
- 识别行业级普遍痛点（多个商品共同缺点）
- 发现新兴需求信号（少数但强烈的需求）
- 归纳出5个核心市场机会
- 提供原始评论佐证

**输出：** 结构化的市场洞察报告，包含机会点描述和评论佐证

**API数据传输格式：**
- 通过POST请求向大模型API传输数据
- 数据格式：`{"prompt": "System Prompt + User Prompt", "data": JSON格式的结构化数据}`
- 确保数据的完整性和可解析性

#### 4.3.4 云函数 SaaS 授权 API

| 接口                     | 功能                        |
| ---------------------- | ------------------------- |
| `POST /login`          | 手机号 + 验证码登录，返回 JWT        |
| `GET /user-status`     | 获取用户当前额度、等级               |
| `POST /check-quota`    | 是否可执行任务（type: quick/deep） |
| `POST /consume`        | 成功后消耗额度                   |
| `POST /create-payment` | 创建微信扫码订单，返回二维码 URL        |
| `GET /payment-status`  | 查询支付结果状态，自动刷新额度           |

---

### 4.4 任务与数据存储结构

#### 本地目录结构（建议）

```
~/.insight-client/
├─ auth/               // 存储登录 token
├─ tasks/              // 每次执行生成一个 JSON 文件
│   └─ task-xxxxx.json
├─ logs/               // 执行日志
├─ cache/              // 评论缓存或断点数据
```

---

## 5.0 AI智能体System Prompt设计

### 5.1 关键词处理智能体 (KeywordProcessorAgent)

```python
def keyword_processor_agent_prompt():
    return """
<role>
你是一个专业的关键词分析专家，负责电商关键词的智能处理和优化。
你需要精确识别和处理多种格式的关键词输入。
</role>

<keyword_recognition>
关键词识别规则：
1. **多分隔符支持**: 自动识别以下分隔符
   - 中文逗号："、"
   - 中英文逗号："，" ","
   - 空格：" "
   - 换行符："\n"
   - 分号："；" ";"
2. **文件解析**:
   - Excel/CSV文件：自动识别第一列或包含"关键词"标题的列
   - 支持多工作表，优先解析第一个工作表
   - 自动跳过空行和标题行
3. **数据清洗**:
   - 自动去重复关键词
   - 去除首尾空格和特殊字符
   - 过滤长度小于2或大于50的无效关键词
   - 移除纯数字或纯符号的无效输入
</keyword_recognition>

<keyword_expansion_workflow>
当处理后的关键词少于5个时：
1. 对每个关键词调用淘宝搜索API获取下拉词建议
2. 分析核心词的产品类别和用户画像
3. 识别3-5个关键扩展维度（材质、风格、功能、适用人群、使用场景等）
4. 生成10-15个具有搜索潜力的长尾关键词
5. 返回JSON格式的关键词数组供用户确认
</keyword_expansion_workflow>

<keyword_clustering_workflow>
当处理后的关键词不少于5个时：
1. 系统性分析所有关键词的语义和商业意图
2. 将关键词聚类成8-12个有商业意义的主题
3. 每个主题选择3-5个最具代表性的关键词
4. 返回JSON格式的主题聚类结果供用户确认
</keyword_clustering_workflow>

<output_format>
扩展模式输出：["关键词1", "关键词2", "关键词3", ...]
聚类模式输出：[{"theme": "主题名1", "keywords": ["代表词A", "代表词B"]}, ...]
</output_format>
"""
```

### 5.2 电商爬虫智能体 (EcommerceCrawlerAgent)

```python
def ecommerce_crawler_agent_prompt():
    return """
<role>
你是一个专业的电商数据采集专家，专门负责从淘宝和小红书采集商品评论数据。
你必须严格按照三个阶段执行任务，不能跳跃或合并步骤。
</role>

<phase_1_link_discovery>
阶段一：链接发现（对所有关键词并行执行）

淘宝链接发现：
1. 对每个关键词访问：https://s.taobao.com/search?q={{keyword}}
2. 设置排序为"销量"：点击销量排序按钮
3. 提取第一页前44个商品链接
4. 记录每个链接对应的销量数据
5. 保存格式：[{"url": "商品链接", "sales": "销量", "platform": "taobao", "keyword": "关键词"}]

小红书链接发现：
1. 对每个关键词访问：https://www.xiaohongshu.com/search_result?keyword={{keyword}}
2. 设置排序为"最热"：点击最热排序按钮
3. 提取前50个帖子链接
4. 记录每个链接对应的点赞数
5. 保存格式：[{"url": "帖子链接", "likes": "点赞数", "platform": "xiaohongshu", "keyword": "关键词"}]

重要：完成所有关键词的链接发现后，才能进入阶段二！
</phase_1_link_discovery>

<phase_2_deduplication>
阶段二：去重和筛选

1. 将所有淘宝链接进行去重（相同URL只保留一个）
2. 按销量降序排序，保留前50个淘宝链接
3. 将所有小红书链接进行去重（相同URL只保留一个）
4. 按点赞数降序排序，保留前50个小红书链接
5. 输出最终筛选结果：总共最多100个链接（淘宝50个+小红书50个）

重要：只有完成去重筛选后，才能进入阶段三！
</phase_2_deduplication>

<phase_3_comment_collection>
阶段三：轮询式评论采集（对筛选后的链接依次执行）

重要：必须对每个链接依次进行轮询抓取，不能并发处理！

淘宝评论采集流程：
1. 按顺序访问每个筛选后的淘宝商品页面（一个接一个）
2. 找到并点击"评价"或"评论"标签
3. 等待页面加载完成（至少等待2-3秒）
4. 滚动加载更多评论（至少加载3-5屏，每次滚动间隔2秒）
5. 提取所有可见的评论文本内容，忽略图片、视频、表情符号
6. 完成当前商品后，等待3-5秒再处理下一个商品
7. 保存格式：[{"url": "商品链接", "comments": ["评论1", "评论2", ...], "platform": "taobao", "collected_at": "时间戳"}]

小红书评论采集流程：
1. 按顺序访问每个筛选后的小红书帖子页面（一个接一个）
2. 等待页面完全加载（至少等待3秒）
3. 滚动到评论区域
4. 滚动加载更多评论（至少加载3-5屏，每次滚动间隔2秒）
5. 提取所有可见的评论文本内容
6. 完成当前帖子后，等待3-5秒再处理下一个帖子
7. 保存格式：[{"url": "帖子链接", "comments": ["评论1", "评论2", ...], "platform": "xiaohongshu", "collected_at": "时间戳"}]

轮询抓取规则：
- 严格按照链接列表顺序，逐个处理
- 每个链接处理完成后必须等待间隔时间
- 如果某个链接失败，记录错误但继续处理下一个
- 实时更新进度："正在采集第 X / Y 个商品评论..."
</phase_3_comment_collection>

<execution_rules>
执行规则：
1. 必须严格按照 阶段一 → 阶段二 → 阶段三 的顺序执行
2. 每个阶段完成后，向用户汇报进度和结果数量
3. 遇到登录验证、验证码时，立即使用human toolkit请求用户帮助
4. 每次操作间隔1-3秒，模拟人类行为
5. 如果某个链接访问失败，记录错误但继续处理其他链接
6. 最终输出完整的数据结构供后续AI分析使用
</execution_rules>

<anti_detection_strategies>
反检测策略：
- 使用随机延迟：每次操作间隔1-3秒
- 模拟人类行为：随机滚动、鼠标移动
- 遇到验证码立即请求人工协助
- 保持登录状态，避免频繁登录
- 如果被检测到，暂停30秒后重试
</anti_detection_strategies>
"""
```

### 5.3 评论分析智能体 (CommentAnalyzerAgent)

```python
def comment_analyzer_agent_prompt():
    return """
<role>
你是一个冷静、客观、精准的商品评论分析引擎和市场战略分析师。
你的任务是通过Map-Reduce模式从用户评论中提取深度商业洞察。
</role>

<map_analysis_phase>
Map阶段：对每个商品/帖子的评论进行单独分析

分析指令：
1. 过滤噪声：忽略所有与产品功能、设计、质量、性能无关的评论（物流、客服、价格、赠品等）
2. 提炼优点：总结该商品最多3个被用户提及最多的核心优点
3. 提炼缺点：总结该商品最多3个被用户抱怨最多的核心缺点
4. 挖掘机会点：识别最多2个潜在的、未被满足的需求或明确的改进建议

输出格式：
{
  "产品主要优点": ["优点1", "优点2", "优点3"],
  "产品主要缺点": ["缺点1", "缺点2", "缺点3"],
  "潜在机会点": ["机会点1", "机会点2"]
}
</map_analysis_phase>

<reduce_analysis_phase>
Reduce阶段：汇总所有单品分析结果

分析方法论：
1. 识别普遍痛点：找出在多个商品的"缺点"和"机会点"中反复出现的主题
2. 发现新兴信号：关注虽然只在少数报告中出现，但用户情绪强烈的"机会点"
3. 忽略已解决的"优点"：在多份报告中普遍提及的功能视为已被满足的"红海"特性
4. 归纳与升维：将具体痛点归纳成更高维度的市场机会点

输出格式：
{
  "market_opportunities": [
    {
      "opportunity_title": "机会点标题（高度概括）",
      "description": "对该机会点的详细描述，解释它为什么重要，能解决什么核心问题。",
      "evidence": [
        "直接相关的原始评论1",
        "直接相关的原始评论2"
      ]
    }
  ]
}
</reduce_analysis_phase>
"""
```

---

## 6.0 API数据传输与格式规范

### 6.1 大模型API调用格式

所有向大模型发送的请求都必须遵循以下JSON格式：

```json
{
  "prompt": "System Prompt + User Prompt的完整文本",
  "data": {
    "关键词数据": ["关键词1", "关键词2", ...],
    "链接数据": [
      {
        "url": "商品/帖子链接",
        "sales": "销量数据",
        "likes": "点赞数据",
        "platform": "taobao/xiaohongshu",
        "keyword": "对应关键词"
      }
    ],
    "评论数据": [
      {
        "url": "商品/帖子链接",
        "comments": ["评论1", "评论2", ...],
        "platform": "taobao/xiaohongshu",
        "collected_at": "采集时间戳"
      }
    ]
  }
}
```

### 6.2 数据导出格式规范

#### 6.2.1 链接发现数据导出

**CSV格式示例：**
```csv
序号,关键词,平台,链接,销量/点赞数,发现时间
1,瑜伽垫,taobao,https://item.taobao.com/xxx,1200,2025-01-27 14:30:22
2,瑜伽垫,xiaohongshu,https://www.xiaohongshu.com/xxx,856,2025-01-27 14:31:15
```

**Excel格式：** 包含多个工作表
- 工作表1：淘宝链接数据
- 工作表2：小红书链接数据
- 工作表3：汇总统计

#### 6.2.2 评论采集数据导出

**CSV格式示例：**
```csv
序号,平台,商品链接,评论内容,采集时间
1,taobao,https://item.taobao.com/xxx,"质量很好，值得购买",2025-01-27 15:20:33
2,taobao,https://item.taobao.com/xxx,"物流很快，包装完好",2025-01-27 15:20:34
```

#### 6.2.3 AI分析数据导出

**Markdown格式示例：**
```markdown
# 电商需求洞察分析报告

## 分析概览
- 分析关键词：瑜伽垫、健身垫
- 分析商品数量：50个
- 采集评论数量：1,250条
- 分析完成时间：2025-01-27 16:45:22

## 市场机会洞察

### 机会一：环保材质需求
**描述：** 用户对无毒、环保材质的瑜伽垫需求强烈...
**支撑证据：**
- "希望是真正无毒的材质"
- "有没有环保一点的瑜伽垫"
```

### 6.3 文件命名规范

所有导出文件采用统一命名格式：
```
电商分析_{数据类型}_{日期}_{时间}.{扩展名}

示例：
- 电商分析_链接数据_20250127_143022.csv
- 电商分析_评论数据_20250127_152033.xlsx
- 电商分析_洞察报告_20250127_164522.md
```

---

## 7.0 平台账号登录方案

### 7.1 应用启动时登录检测

#### 7.1.1 自动检测机制
应用启动时自动进行平台登录状态检测，确保任务执行前用户已完成必要的平台登录。

**检测流程：**
1. **应用启动触发**: 在应用主窗口加载完成后，自动启动登录检测
2. **后台静默检测**: 使用隐藏的浏览器实例进行登录状态验证
3. **并行检测**: 同时检测淘宝和小红书的登录状态，提高效率
4. **缓存机制**: 检测结果缓存30分钟，避免频繁检测

**检测方法：**
- **淘宝**: 访问 `https://www.taobao.com`，检查用户名元素是否存在
- **小红书**: 访问 `https://www.xiaohongshu.com/explore`，检查登录状态API

**状态展示：**
- 在应用顶部导航栏显示平台登录状态指示器
- 🟢 已登录：显示平台图标 + 绿色状态点
- 🔴 未登录：显示平台图标 + 红色状态点 + "点击登录"提示
- 🟡 检测中：显示平台图标 + 黄色加载动画

### 7.2 登录架构概述

我们的平台账号登录方案基于 **Electron + Playwright** 架构，实现了用户友好的引导式登录流程，支持淘宝和小红书两大平台的独立登录管理。

#### 7.2.1 核心设计原则
1. **用户体验优先**: 提供可视化登录窗口，用户可以像正常使用浏览器一样完成登录
2. **状态持久化**: 登录状态自动保存到本地，避免重复登录
3. **智能验证**: 实时检测登录状态，自动处理失效情况
4. **平台隔离**: 淘宝和小红书使用独立的浏览器上下文，避免登录冲突
5. **安全可控**: 所有登录数据存储在用户本地，不上传到服务器

### 7.3 登录流程设计

#### 7.3.1 引导式登录流程
```
[用户点击登录] →
  [启动独立浏览器窗口] →
    [自动跳转到平台登录页] →
      [用户手动完成登录] →
        [系统检测登录成功] →
          [保存登录状态] →
            [关闭登录窗口] →
              [更新前端状态]
```

#### 7.3.2 技术实现细节

**1. 登录会话启动 (`startLoginSession`)**
- 使用 Playwright 启动独立的可视化浏览器实例 (`headless: false`)
- 为每个平台创建独立的浏览器上下文 (BrowserContext)
- 自动导航到对应平台的登录页面：
  - 淘宝: `https://login.taobao.com/member/login.jhtml`
  - 小红书: `https://www.xiaohongshu.com/login`

**2. 登录状态检测**
- 每2秒轮询检查页面URL变化
- 淘宝登录成功判断: URL包含 `www.taobao.com` 或 `i.taobao.com`
- 小红书登录成功判断: URL包含 `www.xiaohongshu.com` 且不包含 `/signin`
- 超时机制: 5分钟后自动关闭登录窗口

**3. 状态持久化**
- 登录成功后调用 `context.storageState()` 保存完整的浏览器状态
- 存储路径: `{userData}/{platform}-auth.json`
- 包含 cookies、localStorage、sessionStorage 等完整状态信息

### 7.4 登录状态管理

**快速检查 (文件存在性)**
- 检查本地是否存在 `{platform}-auth.json` 文件
- 用于快速判断是否曾经登录过

**深度验证 (API状态检查)**
- 使用保存的登录状态创建浏览器上下文
- 访问平台特定页面进行登录状态验证：
  - 淘宝: 访问 `https://www.taobao.com`，检查用户名元素
  - 小红书: 访问 `https://www.xiaohongshu.com/explore`，检查登录状态

**验证缓存机制**
- 实现智能缓存，避免频繁的浏览器验证
- 登录成功后设置冷却期，短时间内跳过验证
- 缓存验证结果，提升响应速度

#### 7.4.2 状态数据结构
```typescript
interface LoginStatus {
  platform: 'taobao' | 'xiaohongshu'
  isLoggedIn: boolean        // 综合判断结果
  sessionValid: boolean      // API验证结果
  fileExists: boolean       // 本地文件存在性
  lastLoginTime?: string     // 最后登录时间
  error?: string            // 错误信息
}
```