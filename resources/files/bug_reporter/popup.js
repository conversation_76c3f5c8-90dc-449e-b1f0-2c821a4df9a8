(()=>{"use strict";var e,t,n={20:(e,t,n)=>{var r=n(540),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:o,_owner:l.current}}t.jsx=u,t.jsxs=u},56:(e,t,n)=>{e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},72:e=>{var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var o={},i=[],l=0;l<e.length;l++){var s=e[l],u=r.base?s[0]+r.base:s[0],c=o[u]||0,d="".concat(u," ").concat(c);o[u]=c+1;var f=n(d),p={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==f)t[f].references++,t[f].updater(p);else{var m=a(p,r);r.byIndex=l,t.splice(l,0,{identifier:d,updater:m,references:1})}i.push(d)}return i}function a(e,t){var n=t.domAPI(t);n.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,a){var o=r(e=e||[],a=a||{});return function(e){e=e||[];for(var i=0;i<o.length;i++){var l=n(o[i]);t[l].references--}for(var s=r(e,a),u=0;u<o.length;u++){var c=n(o[u]);0===t[c].references&&(t[c].updater(),t.splice(c,1))}o=s}}},113:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},159:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},287:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function b(){}function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var w=y.prototype=new b;w.constructor=y,h(w,v.prototype),w.isPureReactComponent=!0;var k=Array.isArray,x=Object.prototype.hasOwnProperty,_={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var a,o={},i=null,l=null;if(null!=t)for(a in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)x.call(t,a)&&!S.hasOwnProperty(a)&&(o[a]=t[a]);var s=arguments.length-2;if(1===s)o.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===o[a]&&(o[a]=s[a]);return{$$typeof:n,type:e,key:i,ref:l,props:o,_owner:_.current}}function C(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function z(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function T(e,t,a,o,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return i=i(s=e),e=""===o?"."+z(s,0):o,k(i)?(a="",null!=e&&(a=e.replace(N,"$&/")+"/"),T(i,t,a,"",function(e){return e})):null!=i&&(C(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,a+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(N,"$&/")+"/")+e)),t.push(i)),1;if(s=0,o=""===o?".":o+":",k(e))for(var u=0;u<e.length;u++){var c=o+z(l=e[u],u);s+=T(l,t,a,c,i)}else if(c=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"==typeof c)for(e=c.call(e),u=0;!(l=e.next()).done;)s+=T(l=l.value,t,a,c=o+z(l,u++),i);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function P(e,t,n){if(null==e)return e;var r=[],a=0;return T(e,r,"","",function(e){return t.call(n,e,a++)}),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R={current:null},L={transition:null},M={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:L,ReactCurrentOwner:_};function I(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:P,forEach:function(e,t,n){P(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return P(e,function(){t++}),t},toArray:function(e){return P(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=i,t.PureComponent=y,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M,t.act=I,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=h({},e.props),o=e.key,i=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,l=_.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)x.call(t,u)&&!S.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:o,ref:i,props:a,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=L.transition;L.transition={};try{e()}finally{L.transition=t}},t.unstable_act=I,t.useCallback=function(e,t){return R.current.useCallback(e,t)},t.useContext=function(e){return R.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return R.current.useDeferredValue(e)},t.useEffect=function(e,t){return R.current.useEffect(e,t)},t.useId=function(){return R.current.useId()},t.useImperativeHandle=function(e,t,n){return R.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return R.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return R.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return R.current.useMemo(e,t)},t.useReducer=function(e,t,n){return R.current.useReducer(e,t,n)},t.useRef=function(e){return R.current.useRef(e)},t.useState=function(e){return R.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return R.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return R.current.useTransition()},t.version="18.3.1"},314:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n}).join("")},t.i=function(e,n,r,a,o){"string"==typeof e&&(e=[[null,e,void 0]]);var i={};if(r)for(var l=0;l<this.length;l++){var s=this[l][0];null!=s&&(i[s]=!0)}for(var u=0;u<e.length;u++){var c=[].concat(e[u]);r&&i[c[0]]||(void 0!==o&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=o),n&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=n):c[2]=n),a&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=a):c[4]="".concat(a)),t.push(c))}},t}},338:(e,t,n)=>{var r=n(961);t.H=r.createRoot,r.hydrateRoot},439:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(601),a=n.n(r),o=n(314),i=n.n(o)()(a());i.push([e.id,"*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n  :root {\n    --background: 222 22% 18%; /* Dark gray background */\n    --foreground: 0 0% 95%; /* White text */\n    --card: 220 18% 25%; /* Slightly lighter dark for cards */\n    --card-foreground: 0 0% 95%; /* White text on cards */\n    --popover: 220 18% 25%;\n    --popover-foreground: 0 0% 95%;\n    --primary: 206 100% 50%; /* Blue accent color */\n    --primary-foreground: 0 0% 100%;\n    --secondary: 220 18% 30%; /* Darker gray for secondary elements */\n    --secondary-foreground: 0 0% 95%;\n    --muted: 220 18% 35%; /* Muted gray */\n    --muted-foreground: 0 0% 70%; /* Light gray text */\n    --accent: 206 100% 50%; /* Blue accent */\n    --accent-foreground: 0 0% 100%;\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 0 0% 100%;\n    --border: 220 18% 30%; /* Dark border */\n    --input: 220 18% 25%; /* Dark input background */\n    --ring: 206 100% 50%; /* Blue focus ring */\n    --radius: 0.5rem;\n  }\n  * {\n  border-color: hsl(var(--border));\n}\n  body {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n    margin: 0;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n}\n.\\!container {\n  width: 100% !important;\n  margin-right: auto !important;\n  margin-left: auto !important;\n  padding-right: 2rem !important;\n  padding-left: 2rem !important;\n}\n.container {\n  width: 100%;\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 2rem;\n  padding-left: 2rem;\n}\n@media (min-width: 1400px) {\n\n  .\\!container {\n    max-width: 1400px !important;\n  }\n\n  .container {\n    max-width: 1400px;\n  }\n}\n.pointer-events-none {\n  pointer-events: none;\n}\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\n.ml-2 {\n  margin-left: 0.5rem;\n}\n.mr-1\\.5 {\n  margin-right: 0.375rem;\n}\n.mr-3 {\n  margin-right: 0.75rem;\n}\n.block {\n  display: block;\n}\n.flex {\n  display: flex;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.contents {\n  display: contents;\n}\n.h-10 {\n  height: 2.5rem;\n}\n.h-11 {\n  height: 2.75rem;\n}\n.h-2 {\n  height: 0.5rem;\n}\n.h-4 {\n  height: 1rem;\n}\n.h-5 {\n  height: 1.25rem;\n}\n.h-9 {\n  height: 2.25rem;\n}\n.h-auto {\n  height: auto;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-10 {\n  width: 2.5rem;\n}\n.w-2 {\n  width: 0.5rem;\n}\n.w-4 {\n  width: 1rem;\n}\n.w-5 {\n  width: 1.25rem;\n}\n.w-9 {\n  width: 2.25rem;\n}\n.w-full {\n  width: 100%;\n}\n.max-w-2xl {\n  max-width: 42rem;\n}\n.max-w-md {\n  max-width: 28rem;\n}\n.flex-1 {\n  flex: 1 1 0%;\n}\n.shrink-0 {\n  flex-shrink: 0;\n}\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.flex-col {\n  flex-direction: column;\n}\n.items-center {\n  align-items: center;\n}\n.justify-start {\n  justify-content: flex-start;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: var(--radius);\n}\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\n.border {\n  border-width: 1px;\n}\n.border-2 {\n  border-width: 2px;\n}\n.border-b {\n  border-bottom-width: 1px;\n}\n.border-b-2 {\n  border-bottom-width: 2px;\n}\n.border-border {\n  border-color: hsl(var(--border));\n}\n.border-green-600\\/30 {\n  border-color: rgb(22 163 74 / 0.3);\n}\n.border-input {\n  border-color: hsl(var(--input));\n}\n.border-primary {\n  border-color: hsl(var(--primary));\n}\n.border-red-600\\/30 {\n  border-color: rgb(220 38 38 / 0.3);\n}\n.border-transparent {\n  border-color: transparent;\n}\n.bg-background {\n  background-color: hsl(var(--background));\n}\n.bg-card {\n  background-color: hsl(var(--card));\n}\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\n.bg-green-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\n.bg-green-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\n.bg-green-600\\/20 {\n  background-color: rgb(22 163 74 / 0.2);\n}\n.bg-input {\n  background-color: hsl(var(--input));\n}\n.bg-muted\\/50 {\n  background-color: hsl(var(--muted) / 0.5);\n}\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\n.bg-red-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.bg-red-600\\/20 {\n  background-color: rgb(220 38 38 / 0.2);\n}\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\n.bg-white {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.p-3 {\n  padding: 0.75rem;\n}\n.p-4 {\n  padding: 1rem;\n}\n.p-6 {\n  padding: 1.5rem;\n}\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.pt-0 {\n  padding-top: 0px;\n}\n.text-left {\n  text-align: left;\n}\n.text-center {\n  text-align: center;\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-medium {\n  font-weight: 500;\n}\n.font-semibold {\n  font-weight: 600;\n}\n.leading-none {\n  line-height: 1;\n}\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\n.text-foreground {\n  color: hsl(var(--foreground));\n}\n.text-green-400 {\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\n.text-primary {\n  color: hsl(var(--primary));\n}\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\n.text-red-400 {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline {\n  outline-style: solid;\n}\n.ring-0 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.running {\n  animation-play-state: running;\n}\n\n#root {\n  height: 100vh;\n  width: 420px; /* Reduced from 560px (25% reduction) */\n  max-width: 100vw;\n  overflow-y: auto; /* Enable vertical scrolling */\n  overflow-x: hidden;\n}\n\nbody {\n  width: 420px; /* Reduced from 560px */\n  min-width: 380px; /* Reduced from 500px */\n  max-width: 420px; /* Fixed width */\n  height: 480px; /* Set fixed height, reduced from 500px */\n  overflow: hidden; /* Hide body overflow since root handles scrolling */\n}\n\n* {\n  box-sizing: border-box;\n}\n\n/* Custom scrollbar for dark theme */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: hsl(220 18% 20%);\n}\n\n::-webkit-scrollbar-thumb {\n  background: hsl(220 18% 35%);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: hsl(220 18% 40%);\n}\n\n.placeholder\\:text-muted-foreground::-moz-placeholder {\n  color: hsl(var(--muted-foreground));\n}\n\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\n\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\n\n.hover\\:bg-destructive\\/80:hover {\n  background-color: hsl(var(--destructive) / 0.8);\n}\n\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\n\n.hover\\:bg-green-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-primary\\/80:hover {\n  background-color: hsl(var(--primary) / 0.8);\n}\n\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\n\n.hover\\:bg-red-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-secondary\\/50:hover {\n  background-color: hsl(var(--secondary) / 0.5);\n}\n\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\n\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\n\n.hover\\:text-foreground:hover {\n  color: hsl(var(--foreground));\n}\n\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\n\n.focus\\:border-transparent:focus {\n  border-color: transparent;\n}\n\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:ring-offset-background:focus-visible {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\n\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\n\n.disabled\\:bg-green-600\\/50:disabled {\n  background-color: rgb(22 163 74 / 0.5);\n}\n\n.disabled\\:bg-red-600\\/50:disabled {\n  background-color: rgb(220 38 38 / 0.5);\n}\n\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\n\n.data-\\[state\\=checked\\]\\:translate-x-4[data-state=\"checked\"] {\n  --tw-translate-x: 1rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=\"unchecked\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[state\\=checked\\]\\:bg-green-600[data-state=\"checked\"] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\n\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"] {\n  background-color: hsl(var(--primary));\n}\n\n.data-\\[state\\=unchecked\\]\\:bg-input[data-state=\"unchecked\"] {\n  background-color: hsl(var(--input));\n}\n",""]);const l=i},463:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var l=2*(r+1)-1,s=e[l],u=l+1,c=e[u];if(0>o(s,n))u<a&&0>o(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[l]=n,r=l);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var u=[],c=[],d=1,f=null,p=3,m=!1,h=!1,g=!1,v="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,y="undefined"!=typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function k(e){if(g=!1,w(e),!h)if(null!==r(u))h=!0,L(x);else{var t=r(c);null!==t&&M(k,t.startTime-e)}}function x(e,n){h=!1,g&&(g=!1,b(C),C=-1),m=!0;var o=p;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!T());){var i=f.callback;if("function"==typeof i){f.callback=null,p=f.priorityLevel;var l=i(f.expirationTime<=n);n=t.unstable_now(),"function"==typeof l?f.callback=l:f===r(u)&&a(u),w(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&M(k,d.startTime-n),s=!1}return s}finally{f=null,p=o,m=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var _,S=!1,E=null,C=-1,N=5,z=-1;function T(){return!(t.unstable_now()-z<N)}function P(){if(null!==E){var e=t.unstable_now();z=e;var n=!0;try{n=E(!0,e)}finally{n?_():(S=!1,E=null)}}else S=!1}if("function"==typeof y)_=function(){y(P)};else if("undefined"!=typeof MessageChannel){var O=new MessageChannel,R=O.port2;O.port1.onmessage=P,_=function(){R.postMessage(null)}}else _=function(){v(P,0)};function L(e){E=e,S||(S=!0,_())}function M(e,n){C=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,L(x))},t.unstable_forceFrameRate=function(e){0>e||125<e||(N=0<e?Math.floor(1e3/e):5)},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var i=t.unstable_now();switch("object"==typeof o&&null!==o?o="number"==typeof(o=o.delay)&&0<o?i+o:i:o=i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:l=o+l,sortIndex:-1},o>i?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(g?(b(C),C=-1):g=!0,M(k,o-i))):(e.sortIndex=l,n(u,e),h||m||(h=!0,L(x))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},540:(e,t,n)=>{e.exports=n(287)},551:(e,t,n)=>{var r=n(540),a=n(982);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(l[e]=t,e=0;e<t.length;e++)i.add(t[e])}var c=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},m={};function h(e,t,n,r,a,o,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new h(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new h(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new h(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new h(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new h(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new h(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function b(e){return e[1].toUpperCase()}function y(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(m,e)||!d.call(p,e)&&(f.test(e)?m[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,b);g[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)});var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),x=Symbol.for("react.portal"),_=Symbol.for("react.fragment"),S=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),N=Symbol.for("react.context"),z=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),P=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),R=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var L=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var M=Symbol.iterator;function I(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=M&&e[M]||e["@@iterator"])?e:null}var j,F=Object.assign;function A(e){if(void 0===j)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);j=t&&t[1]||""}return"\n"+j+e}var D=!1;function $(e,t){if(!e||D)return"";D=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}else{try{throw Error()}catch(e){r=e}e()}}catch(t){if(t&&r&&"string"==typeof t.stack){for(var a=t.stack.split("\n"),o=r.stack.split("\n"),i=a.length-1,l=o.length-1;1<=i&&0<=l&&a[i]!==o[l];)l--;for(;1<=i&&0<=l;i--,l--)if(a[i]!==o[l]){if(1!==i||1!==l)do{if(i--,0>--l||a[i]!==o[l]){var s="\n"+a[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=l);break}}}finally{D=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?A(e):""}function U(e){switch(e.tag){case 5:return A(e.type);case 16:return A("Lazy");case 13:return A("Suspense");case 19:return A("SuspenseList");case 0:case 2:case 15:return e=$(e.type,!1);case 11:return e=$(e.type.render,!1);case 1:return e=$(e.type,!0);default:return""}}function Z(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case _:return"Fragment";case x:return"Portal";case E:return"Profiler";case S:return"StrictMode";case T:return"Suspense";case P:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case N:return(e.displayName||"Context")+".Consumer";case C:return(e._context.displayName||"Context")+".Provider";case z:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case O:return null!==(t=e.displayName||null)?t:Z(e.type)||"Memo";case R:t=e._payload,e=e._init;try{return Z(e(t))}catch(e){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Z(t);case 8:return t===S?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function B(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function W(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function H(e){e._valueTracker||(e._valueTracker=function(e){var t=W(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=W(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function q(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=B(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&y(e,"checked",t,!1)}function X(e,t){Y(e,t);var n=B(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,B(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+B(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:B(n)}}function oe(e,t){var n=B(t.value),r=B(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){me.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ve=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function be(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(o(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function ke(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var xe=null,_e=null,Se=null;function Ee(e){if(e=ya(e)){if("function"!=typeof xe)throw Error(o(280));var t=e.stateNode;t&&(t=ka(t),xe(e.stateNode,e.type,t))}}function Ce(e){_e?Se?Se.push(e):Se=[e]:_e=e}function Ne(){if(_e){var e=_e,t=Se;if(Se=_e=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function ze(e,t){return e(t)}function Te(){}var Pe=!1;function Oe(e,t,n){if(Pe)return e(t,n);Pe=!0;try{return ze(e,t,n)}finally{Pe=!1,(null!==_e||null!==Se)&&(Te(),Ne())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=ka(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(o(231,t,typeof n));return n}var Le=!1;if(c)try{var Me={};Object.defineProperty(Me,"passive",{get:function(){Le=!0}}),window.addEventListener("test",Me,Me),window.removeEventListener("test",Me,Me)}catch(ce){Le=!1}function Ie(e,t,n,r,a,o,i,l,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(e){this.onError(e)}}var je=!1,Fe=null,Ae=!1,De=null,$e={onError:function(e){je=!0,Fe=e}};function Ue(e,t,n,r,a,o,i,l,s){je=!1,Fe=null,Ie.apply($e,arguments)}function Ze(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ve(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Be(e){if(Ze(e)!==e)throw Error(o(188))}function We(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ze(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var i=a.alternate;if(null===i){if(null!==(r=a.return)){n=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===n)return Be(a),e;if(i===r)return Be(a),t;i=i.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=i;else{for(var l=!1,s=a.child;s;){if(s===n){l=!0,n=a,r=i;break}if(s===r){l=!0,r=a,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=a;break}if(s===r){l=!0,r=i,n=a;break}s=s.sibling}if(!l)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?He(e):null}function He(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=He(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,qe=a.unstable_shouldYield,Ge=a.unstable_requestPaint,Ye=a.unstable_now,Xe=a.unstable_getCurrentPriorityLevel,Je=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,ot=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/st|0)|0},lt=Math.log,st=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,i=268435455&n;if(0!==i){var l=i&~a;0!==l?r=dt(l):0!==(o&=i)&&(r=dt(o))}else 0!==(i=n&~a)?r=dt(i):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&4194240&o))return t;if(4&r&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-it(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ut;return!(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function bt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var yt=0;function wt(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var kt,xt,_t,St,Et,Ct=!1,Nt=[],zt=null,Tt=null,Pt=null,Ot=new Map,Rt=new Map,Lt=[],Mt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function It(e,t){switch(e){case"focusin":case"focusout":zt=null;break;case"dragenter":case"dragleave":Tt=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":Ot.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function jt(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=ya(t))&&xt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Ft(e){var t=ba(e.target);if(null!==t){var n=Ze(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ve(n)))return e.blockedOn=t,void Et(e.priority,function(){_t(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function At(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=qt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ya(n))&&xt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function Dt(e,t,n){At(e)&&n.delete(t)}function $t(){Ct=!1,null!==zt&&At(zt)&&(zt=null),null!==Tt&&At(Tt)&&(Tt=null),null!==Pt&&At(Pt)&&(Pt=null),Ot.forEach(Dt),Rt.forEach(Dt)}function Ut(e,t){e.blockedOn===t&&(e.blockedOn=null,Ct||(Ct=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,$t)))}function Zt(e){function t(t){return Ut(t,e)}if(0<Nt.length){Ut(Nt[0],e);for(var n=1;n<Nt.length;n++){var r=Nt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==zt&&Ut(zt,e),null!==Tt&&Ut(Tt,e),null!==Pt&&Ut(Pt,e),Ot.forEach(t),Rt.forEach(t),n=0;n<Lt.length;n++)(r=Lt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&null===(n=Lt[0]).blockedOn;)Ft(n),null===n.blockedOn&&Lt.shift()}var Vt=w.ReactCurrentBatchConfig,Bt=!0;function Wt(e,t,n,r){var a=yt,o=Vt.transition;Vt.transition=null;try{yt=1,Qt(e,t,n,r)}finally{yt=a,Vt.transition=o}}function Ht(e,t,n,r){var a=yt,o=Vt.transition;Vt.transition=null;try{yt=4,Qt(e,t,n,r)}finally{yt=a,Vt.transition=o}}function Qt(e,t,n,r){if(Bt){var a=qt(e,t,n,r);if(null===a)Br(e,t,r,Kt,n),It(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return zt=jt(zt,e,t,n,r,a),!0;case"dragenter":return Tt=jt(Tt,e,t,n,r,a),!0;case"mouseover":return Pt=jt(Pt,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return Ot.set(o,jt(Ot.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,Rt.set(o,jt(Rt.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(It(e,r),4&t&&-1<Mt.indexOf(e)){for(;null!==a;){var o=ya(a);if(null!==o&&kt(o),null===(o=qt(e,t,n,r))&&Br(e,t,r,Kt,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Br(e,t,r,null,n)}}var Kt=null;function qt(e,t,n,r){if(Kt=null,null!==(e=ba(e=ke(r))))if(null===(t=Ze(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ve(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xe()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Xt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Xt,r=n.length,a="value"in Yt?Yt.value:Yt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[o-t];t++);return Jt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,o){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,ln,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=F({},un,{view:0,detail:0}),fn=an(dn),pn=F({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:En,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(on=e.screenX-sn.screenX,ln=e.screenY-sn.screenY):ln=on=0,sn=e),on)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),mn=an(pn),hn=an(F({},pn,{dataTransfer:0})),gn=an(F({},dn,{relatedTarget:0})),vn=an(F({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),bn=F({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),yn=an(bn),wn=an(F({},un,{data:0})),kn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},xn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},_n={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=_n[e])&&!!t[e]}function En(){return Sn}var Cn=F({},dn,{key:function(e){if(e.key){var t=kn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?xn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:En,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Nn=an(Cn),zn=an(F({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Tn=an(F({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:En})),Pn=an(F({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),On=F({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rn=an(On),Ln=[9,13,27,32],Mn=c&&"CompositionEvent"in window,In=null;c&&"documentMode"in document&&(In=document.documentMode);var jn=c&&"TextEvent"in window&&!In,Fn=c&&(!Mn||In&&8<In&&11>=In),An=String.fromCharCode(32),Dn=!1;function $n(e,t){switch(e){case"keyup":return-1!==Ln.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Un(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Zn=!1;var Vn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vn[e.type]:"textarea"===t}function Wn(e,t,n,r){Ce(r),0<(t=Hr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Hn=null,Qn=null;function Kn(e){Ar(e,0)}function qn(e){if(Q(wa(e)))return e}function Gn(e,t){if("change"===e)return t}var Yn=!1;if(c){var Xn;if(c){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"==typeof er.oninput}Xn=Jn}else Xn=!1;Yn=Xn&&(!document.documentMode||9<document.documentMode)}function tr(){Hn&&(Hn.detachEvent("onpropertychange",nr),Qn=Hn=null)}function nr(e){if("value"===e.propertyName&&qn(Qn)){var t=[];Wn(t,Qn,e,ke(e)),Oe(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(Hn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return qn(Qn)}function or(e,t){if("click"===e)return qn(t)}function ir(e,t){if("input"===e||"change"===e)return qn(t)}var lr="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function sr(e,t){if(lr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!lr(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=cr(n,o);var i=cr(n,r);a&&i&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var hr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,br=null,yr=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;yr||null==gr||gr!==K(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},br&&sr(br,r)||(br=r,0<(r=Hr(vr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function kr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var xr={animationend:kr("Animation","AnimationEnd"),animationiteration:kr("Animation","AnimationIteration"),animationstart:kr("Animation","AnimationStart"),transitionend:kr("Transition","TransitionEnd")},_r={},Sr={};function Er(e){if(_r[e])return _r[e];if(!xr[e])return e;var t,n=xr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Sr)return _r[e]=n[t];return e}c&&(Sr=document.createElement("div").style,"AnimationEvent"in window||(delete xr.animationend.animation,delete xr.animationiteration.animation,delete xr.animationstart.animation),"TransitionEvent"in window||delete xr.transitionend.transition);var Cr=Er("animationend"),Nr=Er("animationiteration"),zr=Er("animationstart"),Tr=Er("transitionend"),Pr=new Map,Or="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){Pr.set(e,t),s(t,[e])}for(var Lr=0;Lr<Or.length;Lr++){var Mr=Or[Lr];Rr(Mr.toLowerCase(),"on"+(Mr[0].toUpperCase()+Mr.slice(1)))}Rr(Cr,"onAnimationEnd"),Rr(Nr,"onAnimationIteration"),Rr(zr,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Tr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ir="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),jr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ir));function Fr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,i,l,s,u){if(Ue.apply(this,arguments),je){if(!je)throw Error(o(198));var c=Fe;je=!1,Fe=null,Ae||(Ae=!0,De=c)}}(r,t,void 0,e),e.currentTarget=null}function Ar(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,u=l.currentTarget;if(l=l.listener,s!==o&&a.isPropagationStopped())break e;Fr(a,l,u),o=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,u=l.currentTarget,l=l.listener,s!==o&&a.isPropagationStopped())break e;Fr(a,l,u),o=s}}}if(Ae)throw e=De,Ae=!1,De=null,e}function Dr(e,t){var n=t[ha];void 0===n&&(n=t[ha]=new Set);var r=e+"__bubble";n.has(r)||(Vr(t,e,2,!1),n.add(r))}function $r(e,t,n){var r=0;t&&(r|=4),Vr(n,e,r,t)}var Ur="_reactListening"+Math.random().toString(36).slice(2);function Zr(e){if(!e[Ur]){e[Ur]=!0,i.forEach(function(t){"selectionchange"!==t&&(jr.has(t)||$r(t,!1,e),$r(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ur]||(t[Ur]=!0,$r("selectionchange",!1,t))}}function Vr(e,t,n,r){switch(Gt(t)){case 1:var a=Wt;break;case 4:a=Ht;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Le||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Br(e,t,n,r,a){var o=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===a||8===l.nodeType&&l.parentNode===a)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;i=i.return}for(;null!==l;){if(null===(i=ba(l)))return;if(5===(s=i.tag)||6===s){r=o=i;continue e}l=l.parentNode}}r=r.return}Oe(function(){var r=o,a=ke(n),i=[];e:{var l=Pr.get(e);if(void 0!==l){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=Nn;break;case"focusin":u="focus",s=gn;break;case"focusout":u="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Tn;break;case Cr:case Nr:case zr:s=vn;break;case Tr:s=Pn;break;case"scroll":s=fn;break;case"wheel":s=Rn;break;case"copy":case"cut":case"paste":s=yn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=zn}var c=!!(4&t),d=!c&&"scroll"===e,f=c?null!==l?l+"Capture":null:l;c=[];for(var p,m=r;null!==m;){var h=(p=m).stateNode;if(5===p.tag&&null!==h&&(p=h,null!==f&&(null!=(h=Re(m,f))&&c.push(Wr(m,h,p)))),d)break;m=m.return}0<c.length&&(l=new s(l,u,null,n,a),i.push({event:l,listeners:c}))}}if(!(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!ba(u)&&!u[ma])&&(s||l)&&(l=a.window===a?a:(l=a.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?ba(u):null)&&(u!==(d=Ze(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=mn,h="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(c=zn,h="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==s?l:wa(s),p=null==u?l:wa(u),(l=new c(h,m+"leave",s,n,a)).target=d,l.relatedTarget=p,h=null,ba(a)===r&&((c=new c(f,m+"enter",u,n,a)).target=p,c.relatedTarget=d,h=c),d=h,s&&u)e:{for(f=u,m=0,p=c=s;p;p=Qr(p))m++;for(p=0,h=f;h;h=Qr(h))p++;for(;0<m-p;)c=Qr(c),m--;for(;0<p-m;)f=Qr(f),p--;for(;m--;){if(c===f||null!==f&&c===f.alternate)break e;c=Qr(c),f=Qr(f)}c=null}else c=null;null!==s&&Kr(i,l,s,c,!1),null!==u&&null!==d&&Kr(i,d,u,c,!0)}if("select"===(s=(l=r?wa(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var g=Gn;else if(Bn(l))if(Yn)g=ir;else{g=ar;var v=rr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(g=or);switch(g&&(g=g(e,r))?Wn(i,g,n,a):(v&&v(e,l,r),"focusout"===e&&(v=l._wrapperState)&&v.controlled&&"number"===l.type&&ee(l,"number",l.value)),v=r?wa(r):window,e){case"focusin":(Bn(v)||"true"===v.contentEditable)&&(gr=v,vr=r,br=null);break;case"focusout":br=vr=gr=null;break;case"mousedown":yr=!0;break;case"contextmenu":case"mouseup":case"dragend":yr=!1,wr(i,n,a);break;case"selectionchange":if(hr)break;case"keydown":case"keyup":wr(i,n,a)}var b;if(Mn)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else Zn?$n(e,n)&&(y="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(y="onCompositionStart");y&&(Fn&&"ko"!==n.locale&&(Zn||"onCompositionStart"!==y?"onCompositionEnd"===y&&Zn&&(b=en()):(Xt="value"in(Yt=a)?Yt.value:Yt.textContent,Zn=!0)),0<(v=Hr(r,y)).length&&(y=new wn(y,e,null,n,a),i.push({event:y,listeners:v}),b?y.data=b:null!==(b=Un(n))&&(y.data=b))),(b=jn?function(e,t){switch(e){case"compositionend":return Un(t);case"keypress":return 32!==t.which?null:(Dn=!0,An);case"textInput":return(e=t.data)===An&&Dn?null:e;default:return null}}(e,n):function(e,t){if(Zn)return"compositionend"===e||!Mn&&$n(e,t)?(e=en(),Jt=Xt=Yt=null,Zn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Hr(r,"onBeforeInput")).length&&(a=new wn("onBeforeInput","beforeinput",null,n,a),i.push({event:a,listeners:r}),a.data=b))}Ar(i,t)})}function Wr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Hr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=Re(e,n))&&r.unshift(Wr(e,o,a)),null!=(o=Re(e,t))&&r.push(Wr(e,o,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var o=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,u=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==u&&(l=u,a?null!=(s=Re(n,o))&&i.unshift(Wr(n,s,l)):a||null!=(s=Re(n,o))&&i.push(Wr(n,s,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var qr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Yr(e){return("string"==typeof e?e:""+e).replace(qr,"\n").replace(Gr,"")}function Xr(e,t,n){if(t=Yr(t),Yr(e)!==t&&n)throw Error(o(425))}function Jr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"==typeof setTimeout?setTimeout:void 0,aa="function"==typeof clearTimeout?clearTimeout:void 0,oa="function"==typeof Promise?Promise:void 0,ia="function"==typeof queueMicrotask?queueMicrotask:void 0!==oa?function(e){return oa.resolve(null).then(e).catch(la)}:ra;function la(e){setTimeout(function(){throw e})}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Zt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Zt(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ma="__reactContainer$"+da,ha="__reactEvents$"+da,ga="__reactListeners$"+da,va="__reactHandles$"+da;function ba(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ma]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ya(e){return!(e=e[fa]||e[ma])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function ka(e){return e[pa]||null}var xa=[],_a=-1;function Sa(e){return{current:e}}function Ea(e){0>_a||(e.current=xa[_a],xa[_a]=null,_a--)}function Ca(e,t){_a++,xa[_a]=e.current,e.current=t}var Na={},za=Sa(Na),Ta=Sa(!1),Pa=Na;function Oa(e,t){var n=e.type.contextTypes;if(!n)return Na;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ra(e){return null!=(e=e.childContextTypes)}function La(){Ea(Ta),Ea(za)}function Ma(e,t,n){if(za.current!==Na)throw Error(o(168));Ca(za,t),Ca(Ta,n)}function Ia(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(o(108,V(e)||"Unknown",a));return F({},n,r)}function ja(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Na,Pa=za.current,Ca(za,e),Ca(Ta,Ta.current),!0}function Fa(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=Ia(e,t,Pa),r.__reactInternalMemoizedMergedChildContext=e,Ea(Ta),Ea(za),Ca(za,e)):Ea(Ta),Ca(Ta,n)}var Aa=null,Da=!1,$a=!1;function Ua(e){null===Aa?Aa=[e]:Aa.push(e)}function Za(){if(!$a&&null!==Aa){$a=!0;var e=0,t=yt;try{var n=Aa;for(yt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Aa=null,Da=!1}catch(t){throw null!==Aa&&(Aa=Aa.slice(e+1)),Qe(Je,Za),t}finally{yt=t,$a=!1}}return null}var Va=[],Ba=0,Wa=null,Ha=0,Qa=[],Ka=0,qa=null,Ga=1,Ya="";function Xa(e,t){Va[Ba++]=Ha,Va[Ba++]=Wa,Wa=e,Ha=t}function Ja(e,t,n){Qa[Ka++]=Ga,Qa[Ka++]=Ya,Qa[Ka++]=qa,qa=e;var r=Ga;e=Ya;var a=32-it(r)-1;r&=~(1<<a),n+=1;var o=32-it(t)+a;if(30<o){var i=a-a%5;o=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Ga=1<<32-it(t)+a|n<<a|r,Ya=o+e}else Ga=1<<o|n<<a|r,Ya=e}function eo(e){null!==e.return&&(Xa(e,1),Ja(e,1,0))}function to(e){for(;e===Wa;)Wa=Va[--Ba],Va[Ba]=null,Ha=Va[--Ba],Va[Ba]=null;for(;e===qa;)qa=Qa[--Ka],Qa[Ka]=null,Ya=Qa[--Ka],Qa[Ka]=null,Ga=Qa[--Ka],Qa[Ka]=null}var no=null,ro=null,ao=!1,oo=null;function io(e,t){var n=Pu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function lo(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==qa?{id:Ga,overflow:Ya}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Pu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function so(e){return!(!(1&e.mode)||128&e.flags)}function uo(e){if(ao){var t=ro;if(t){var n=t;if(!lo(e,t)){if(so(e))throw Error(o(418));t=ua(n.nextSibling);var r=no;t&&lo(e,t)?io(r,n):(e.flags=-4097&e.flags|2,ao=!1,no=e)}}else{if(so(e))throw Error(o(418));e.flags=-4097&e.flags|2,ao=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!ao)return co(e),ao=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ro)){if(so(e))throw po(),Error(o(418));for(;t;)io(e,t),t=ua(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ua(e.stateNode.nextSibling):null;return!0}function po(){for(var e=ro;e;)e=ua(e.nextSibling)}function mo(){ro=no=null,ao=!1}function ho(e){null===oo?oo=[e]:oo.push(e)}var go=w.ReactCurrentBatchConfig;function vo(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var a=r,i=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=a.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!=typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function bo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yo(e){return(0,e._init)(e._payload)}function wo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Ru(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=ju(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===_?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"==typeof o&&null!==o&&o.$$typeof===R&&yo(o)===t.type)?((r=a(t,n.props)).ref=vo(e,t,n),r.return=e,r):((r=Lu(n.type,n.key,n.props,null,e.mode,r)).ref=vo(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Fu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Mu(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=ju(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case k:return(n=Lu(t.type,t.key,t.props,null,e.mode,n)).ref=vo(e,null,t),n.return=e,n;case x:return(t=Fu(t,e.mode,n)).return=e,t;case R:return f(e,(0,t._init)(t._payload),n)}if(te(t)||I(t))return(t=Mu(t,e.mode,n,null)).return=e,t;bo(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==a?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case k:return n.key===a?u(e,t,n,r):null;case x:return n.key===a?c(e,t,n,r):null;case R:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||I(n))return null!==a?null:d(e,t,n,r,null);bo(e,n)}return null}function m(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case k:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case x:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case R:return m(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||I(r))return d(t,e=e.get(n)||null,r,a,null);bo(t,r)}return null}function h(a,o,l,s){for(var u=null,c=null,d=o,h=o=0,g=null;null!==d&&h<l.length;h++){d.index>h?(g=d,d=null):g=d.sibling;var v=p(a,d,l[h],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),o=i(v,o,h),null===c?u=v:c.sibling=v,c=v,d=g}if(h===l.length)return n(a,d),ao&&Xa(a,h),u;if(null===d){for(;h<l.length;h++)null!==(d=f(a,l[h],s))&&(o=i(d,o,h),null===c?u=d:c.sibling=d,c=d);return ao&&Xa(a,h),u}for(d=r(a,d);h<l.length;h++)null!==(g=m(d,a,h,l[h],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?h:g.key),o=i(g,o,h),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),ao&&Xa(a,h),u}function g(a,l,s,u){var c=I(s);if("function"!=typeof c)throw Error(o(150));if(null==(s=c.call(s)))throw Error(o(151));for(var d=c=null,h=l,g=l=0,v=null,b=s.next();null!==h&&!b.done;g++,b=s.next()){h.index>g?(v=h,h=null):v=h.sibling;var y=p(a,h,b.value,u);if(null===y){null===h&&(h=v);break}e&&h&&null===y.alternate&&t(a,h),l=i(y,l,g),null===d?c=y:d.sibling=y,d=y,h=v}if(b.done)return n(a,h),ao&&Xa(a,g),c;if(null===h){for(;!b.done;g++,b=s.next())null!==(b=f(a,b.value,u))&&(l=i(b,l,g),null===d?c=b:d.sibling=b,d=b);return ao&&Xa(a,g),c}for(h=r(a,h);!b.done;g++,b=s.next())null!==(b=m(h,a,g,b.value,u))&&(e&&null!==b.alternate&&h.delete(null===b.key?g:b.key),l=i(b,l,g),null===d?c=b:d.sibling=b,d=b);return e&&h.forEach(function(e){return t(a,e)}),ao&&Xa(a,g),c}return function e(r,o,i,s){if("object"==typeof i&&null!==i&&i.type===_&&null===i.key&&(i=i.props.children),"object"==typeof i&&null!==i){switch(i.$$typeof){case k:e:{for(var u=i.key,c=o;null!==c;){if(c.key===u){if((u=i.type)===_){if(7===c.tag){n(r,c.sibling),(o=a(c,i.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"==typeof u&&null!==u&&u.$$typeof===R&&yo(u)===c.type){n(r,c.sibling),(o=a(c,i.props)).ref=vo(r,c,i),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}i.type===_?((o=Mu(i.props.children,r.mode,s,i.key)).return=r,r=o):((s=Lu(i.type,i.key,i.props,null,r.mode,s)).ref=vo(r,o,i),s.return=r,r=s)}return l(r);case x:e:{for(c=i.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===i.containerInfo&&o.stateNode.implementation===i.implementation){n(r,o.sibling),(o=a(o,i.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Fu(i,r.mode,s)).return=r,r=o}return l(r);case R:return e(r,o,(c=i._init)(i._payload),s)}if(te(i))return h(r,o,i,s);if(I(i))return g(r,o,i,s);bo(r,i)}return"string"==typeof i&&""!==i||"number"==typeof i?(i=""+i,null!==o&&6===o.tag?(n(r,o.sibling),(o=a(o,i)).return=r,r=o):(n(r,o),(o=ju(i,r.mode,s)).return=r,r=o),l(r)):n(r,o)}}var ko=wo(!0),xo=wo(!1),_o=Sa(null),So=null,Eo=null,Co=null;function No(){Co=Eo=So=null}function zo(e){var t=_o.current;Ea(_o),e._currentValue=t}function To(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Po(e,t){So=e,Co=Eo=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bl=!0),e.firstContext=null)}function Oo(e){var t=e._currentValue;if(Co!==e)if(e={context:e,memoizedValue:t,next:null},null===Eo){if(null===So)throw Error(o(308));Eo=e,So.dependencies={lanes:0,firstContext:e}}else Eo=Eo.next=e;return t}var Ro=null;function Lo(e){null===Ro?Ro=[e]:Ro.push(e)}function Mo(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Lo(t)):(n.next=a.next,a.next=n),t.interleaved=n,Io(e,r)}function Io(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var jo=!1;function Fo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ao(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Do(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function $o(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&Ns){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Io(e,n)}return null===(a=r.interleaved)?(t.next=t,Lo(r)):(t.next=a.next,a.next=t),r.interleaved=t,Io(e,n)}function Uo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}function Zo(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=i:o=o.next=i,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Vo(e,t,n,r){var a=e.updateQueue;jo=!1;var o=a.firstBaseUpdate,i=a.lastBaseUpdate,l=a.shared.pending;if(null!==l){a.shared.pending=null;var s=l,u=s.next;s.next=null,null===i?o=u:i.next=u,i=s;var c=e.alternate;null!==c&&((l=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===l?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=s))}if(null!==o){var d=a.baseState;for(i=0,c=u=s=null,l=o;;){var f=l.lane,p=l.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var m=e,h=l;switch(f=t,p=n,h.tag){case 1:if("function"==typeof(m=h.payload)){d=m.call(p,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(f="function"==typeof(m=h.payload)?m.call(p,d,f):m))break e;d=F({},d,f);break e;case 2:jo=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,i|=f;if(null===(l=l.next)){if(null===(l=a.shared.pending))break;l=(f=l).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{i|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);Is|=i,e.lanes=i,e.memoizedState=d}}function Bo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!=typeof a)throw Error(o(191,a));a.call(r)}}}var Wo={},Ho=Sa(Wo),Qo=Sa(Wo),Ko=Sa(Wo);function qo(e){if(e===Wo)throw Error(o(174));return e}function Go(e,t){switch(Ca(Ko,t),Ca(Qo,e),Ca(Ho,Wo),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ea(Ho),Ca(Ho,t)}function Yo(){Ea(Ho),Ea(Qo),Ea(Ko)}function Xo(e){qo(Ko.current);var t=qo(Ho.current),n=se(t,e.type);t!==n&&(Ca(Qo,e),Ca(Ho,n))}function Jo(e){Qo.current===e&&(Ea(Ho),Ea(Qo))}var ei=Sa(0);function ti(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ni=[];function ri(){for(var e=0;e<ni.length;e++)ni[e]._workInProgressVersionPrimary=null;ni.length=0}var ai=w.ReactCurrentDispatcher,oi=w.ReactCurrentBatchConfig,ii=0,li=null,si=null,ui=null,ci=!1,di=!1,fi=0,pi=0;function mi(){throw Error(o(321))}function hi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function gi(e,t,n,r,a,i){if(ii=i,li=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ai.current=null===e||null===e.memoizedState?Ji:el,e=n(r,a),di){i=0;do{if(di=!1,fi=0,25<=i)throw Error(o(301));i+=1,ui=si=null,t.updateQueue=null,ai.current=tl,e=n(r,a)}while(di)}if(ai.current=Xi,t=null!==si&&null!==si.next,ii=0,ui=si=li=null,ci=!1,t)throw Error(o(300));return e}function vi(){var e=0!==fi;return fi=0,e}function bi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ui?li.memoizedState=ui=e:ui=ui.next=e,ui}function yi(){if(null===si){var e=li.alternate;e=null!==e?e.memoizedState:null}else e=si.next;var t=null===ui?li.memoizedState:ui.next;if(null!==t)ui=t,si=e;else{if(null===e)throw Error(o(310));e={memoizedState:(si=e).memoizedState,baseState:si.baseState,baseQueue:si.baseQueue,queue:si.queue,next:null},null===ui?li.memoizedState=ui=e:ui=ui.next=e}return ui}function wi(e,t){return"function"==typeof t?t(e):t}function ki(e){var t=yi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=si,a=r.baseQueue,i=n.pending;if(null!==i){if(null!==a){var l=a.next;a.next=i.next,i.next=l}r.baseQueue=a=i,n.pending=null}if(null!==a){i=a.next,r=r.baseState;var s=l=null,u=null,c=i;do{var d=c.lane;if((ii&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,l=r):u=u.next=f,li.lanes|=d,Is|=d}c=c.next}while(null!==c&&c!==i);null===u?l=r:u.next=s,lr(r,t.memoizedState)||(bl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{i=a.lane,li.lanes|=i,Is|=i,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function xi(e){var t=yi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,i=t.memoizedState;if(null!==a){n.pending=null;var l=a=a.next;do{i=e(i,l.action),l=l.next}while(l!==a);lr(i,t.memoizedState)||(bl=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function _i(){}function Si(e,t){var n=li,r=yi(),a=t(),i=!lr(r.memoizedState,a);if(i&&(r.memoizedState=a,bl=!0),r=r.queue,ji(Ni.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==ui&&1&ui.memoizedState.tag){if(n.flags|=2048,Oi(9,Ci.bind(null,n,r,a,t),void 0,null),null===zs)throw Error(o(349));30&ii||Ei(n,t,a)}return a}function Ei(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=li.updateQueue)?(t={lastEffect:null,stores:null},li.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ci(e,t,n,r){t.value=n,t.getSnapshot=r,zi(t)&&Ti(e)}function Ni(e,t,n){return n(function(){zi(t)&&Ti(e)})}function zi(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lr(e,n)}catch(e){return!0}}function Ti(e){var t=Io(e,1);null!==t&&tu(t,e,1,-1)}function Pi(e){var t=bi();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wi,lastRenderedState:e},t.queue=e,e=e.dispatch=Ki.bind(null,li,e),[t.memoizedState,e]}function Oi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=li.updateQueue)?(t={lastEffect:null,stores:null},li.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ri(){return yi().memoizedState}function Li(e,t,n,r){var a=bi();li.flags|=e,a.memoizedState=Oi(1|t,n,void 0,void 0===r?null:r)}function Mi(e,t,n,r){var a=yi();r=void 0===r?null:r;var o=void 0;if(null!==si){var i=si.memoizedState;if(o=i.destroy,null!==r&&hi(r,i.deps))return void(a.memoizedState=Oi(t,n,o,r))}li.flags|=e,a.memoizedState=Oi(1|t,n,o,r)}function Ii(e,t){return Li(8390656,8,e,t)}function ji(e,t){return Mi(2048,8,e,t)}function Fi(e,t){return Mi(4,2,e,t)}function Ai(e,t){return Mi(4,4,e,t)}function Di(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function $i(e,t,n){return n=null!=n?n.concat([e]):null,Mi(4,4,Di.bind(null,t,e),n)}function Ui(){}function Zi(e,t){var n=yi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&hi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vi(e,t){var n=yi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&hi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Bi(e,t,n){return 21&ii?(lr(n,t)||(n=ht(),li.lanes|=n,Is|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,bl=!0),e.memoizedState=n)}function Wi(e,t){var n=yt;yt=0!==n&&4>n?n:4,e(!0);var r=oi.transition;oi.transition={};try{e(!1),t()}finally{yt=n,oi.transition=r}}function Hi(){return yi().memoizedState}function Qi(e,t,n){var r=eu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},qi(e))Gi(t,n);else if(null!==(n=Mo(e,t,n,r))){tu(n,e,r,Js()),Yi(n,t,r)}}function Ki(e,t,n){var r=eu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(qi(e))Gi(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=o(i,n);if(a.hasEagerState=!0,a.eagerState=l,lr(l,i)){var s=t.interleaved;return null===s?(a.next=a,Lo(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(e){}null!==(n=Mo(e,t,a,r))&&(tu(n,e,r,a=Js()),Yi(n,t,r))}}function qi(e){var t=e.alternate;return e===li||null!==t&&t===li}function Gi(e,t){di=ci=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yi(e,t,n){if(4194240&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}var Xi={readContext:Oo,useCallback:mi,useContext:mi,useEffect:mi,useImperativeHandle:mi,useInsertionEffect:mi,useLayoutEffect:mi,useMemo:mi,useReducer:mi,useRef:mi,useState:mi,useDebugValue:mi,useDeferredValue:mi,useTransition:mi,useMutableSource:mi,useSyncExternalStore:mi,useId:mi,unstable_isNewReconciler:!1},Ji={readContext:Oo,useCallback:function(e,t){return bi().memoizedState=[e,void 0===t?null:t],e},useContext:Oo,useEffect:Ii,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Li(4194308,4,Di.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Li(4194308,4,e,t)},useInsertionEffect:function(e,t){return Li(4,2,e,t)},useMemo:function(e,t){var n=bi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=bi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qi.bind(null,li,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},bi().memoizedState=e},useState:Pi,useDebugValue:Ui,useDeferredValue:function(e){return bi().memoizedState=e},useTransition:function(){var e=Pi(!1),t=e[0];return e=Wi.bind(null,e[1]),bi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=li,a=bi();if(ao){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===zs)throw Error(o(349));30&ii||Ei(r,t,n)}a.memoizedState=n;var i={value:n,getSnapshot:t};return a.queue=i,Ii(Ni.bind(null,r,i,e),[e]),r.flags|=2048,Oi(9,Ci.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=bi(),t=zs.identifierPrefix;if(ao){var n=Ya;t=":"+t+"R"+(n=(Ga&~(1<<32-it(Ga)-1)).toString(32)+n),0<(n=fi++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},el={readContext:Oo,useCallback:Zi,useContext:Oo,useEffect:ji,useImperativeHandle:$i,useInsertionEffect:Fi,useLayoutEffect:Ai,useMemo:Vi,useReducer:ki,useRef:Ri,useState:function(){return ki(wi)},useDebugValue:Ui,useDeferredValue:function(e){return Bi(yi(),si.memoizedState,e)},useTransition:function(){return[ki(wi)[0],yi().memoizedState]},useMutableSource:_i,useSyncExternalStore:Si,useId:Hi,unstable_isNewReconciler:!1},tl={readContext:Oo,useCallback:Zi,useContext:Oo,useEffect:ji,useImperativeHandle:$i,useInsertionEffect:Fi,useLayoutEffect:Ai,useMemo:Vi,useReducer:xi,useRef:Ri,useState:function(){return xi(wi)},useDebugValue:Ui,useDeferredValue:function(e){var t=yi();return null===si?t.memoizedState=e:Bi(t,si.memoizedState,e)},useTransition:function(){return[xi(wi)[0],yi().memoizedState]},useMutableSource:_i,useSyncExternalStore:Si,useId:Hi,unstable_isNewReconciler:!1};function nl(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rl(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var al={isMounted:function(e){return!!(e=e._reactInternals)&&Ze(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Js(),a=eu(e),o=Do(r,a);o.payload=t,null!=n&&(o.callback=n),null!==(t=$o(e,o,a))&&(tu(t,e,a,r),Uo(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Js(),a=eu(e),o=Do(r,a);o.tag=1,o.payload=t,null!=n&&(o.callback=n),null!==(t=$o(e,o,a))&&(tu(t,e,a,r),Uo(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Js(),r=eu(e),a=Do(n,r);a.tag=2,null!=t&&(a.callback=t),null!==(t=$o(e,a,r))&&(tu(t,e,r,n),Uo(t,e,r))}};function ol(e,t,n,r,a,o,i){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,i):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,o))}function il(e,t,n){var r=!1,a=Na,o=t.contextType;return"object"==typeof o&&null!==o?o=Oo(o):(a=Ra(t)?Pa:za.current,o=(r=null!=(r=t.contextTypes))?Oa(e,a):Na),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=al,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function ll(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&al.enqueueReplaceState(t,t.state,null)}function sl(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Fo(e);var o=t.contextType;"object"==typeof o&&null!==o?a.context=Oo(o):(o=Ra(t)?Pa:za.current,a.context=Oa(e,o)),a.state=e.memoizedState,"function"==typeof(o=t.getDerivedStateFromProps)&&(rl(e,t,o,n),a.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(t=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&al.enqueueReplaceState(a,a.state,null),Vo(e,n,a,r),a.state=e.memoizedState),"function"==typeof a.componentDidMount&&(e.flags|=4194308)}function ul(e,t){try{var n="",r=t;do{n+=U(r),r=r.return}while(r);var a=n}catch(e){a="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:a,digest:null}}function cl(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}var dl="function"==typeof WeakMap?WeakMap:Map;function fl(e,t,n){(n=Do(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vs||(Vs=!0,Bs=r)},n}function pl(e,t,n){(n=Do(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){}}var o=e.stateNode;return null!==o&&"function"==typeof o.componentDidCatch&&(n.callback=function(){"function"!=typeof r&&(null===Ws?Ws=new Set([this]):Ws.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ml(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new dl;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Su.bind(null,e,t,n),t.then(e,e))}function hl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function gl(e,t,n,r,a){return 1&e.mode?(e.flags|=65536,e.lanes=a,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Do(-1,1)).tag=2,$o(n,t,1))),n.lanes|=1),e)}var vl=w.ReactCurrentOwner,bl=!1;function yl(e,t,n,r){t.child=null===e?xo(t,null,n,r):ko(t,e.child,n,r)}function wl(e,t,n,r,a){n=n.render;var o=t.ref;return Po(t,a),r=gi(e,t,n,r,o,a),n=vi(),null===e||bl?(ao&&n&&eo(t),t.flags|=1,yl(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vl(e,t,a))}function kl(e,t,n,r,a){if(null===e){var o=n.type;return"function"!=typeof o||Ou(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Lu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,xl(e,t,o,r,a))}if(o=e.child,0===(e.lanes&a)){var i=o.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(i,r)&&e.ref===t.ref)return Vl(e,t,a)}return t.flags|=1,(e=Ru(o,r)).ref=t.ref,e.return=t,t.child=e}function xl(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(sr(o,r)&&e.ref===t.ref){if(bl=!1,t.pendingProps=r=o,0===(e.lanes&a))return t.lanes=e.lanes,Vl(e,t,a);131072&e.flags&&(bl=!0)}}return El(e,t,n,r,a)}function _l(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ca(Rs,Os),Os|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Ca(Rs,Os),Os|=r}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ca(Rs,Os),Os|=n;else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ca(Rs,Os),Os|=r;return yl(e,t,a,n),t.child}function Sl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function El(e,t,n,r,a){var o=Ra(n)?Pa:za.current;return o=Oa(t,o),Po(t,a),n=gi(e,t,n,r,o,a),r=vi(),null===e||bl?(ao&&r&&eo(t),t.flags|=1,yl(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vl(e,t,a))}function Cl(e,t,n,r,a){if(Ra(n)){var o=!0;ja(t)}else o=!1;if(Po(t,a),null===t.stateNode)Zl(e,t),il(t,n,r),sl(t,n,r,a),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var s=i.context,u=n.contextType;"object"==typeof u&&null!==u?u=Oo(u):u=Oa(t,u=Ra(n)?Pa:za.current);var c=n.getDerivedStateFromProps,d="function"==typeof c||"function"==typeof i.getSnapshotBeforeUpdate;d||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l!==r||s!==u)&&ll(t,i,r,u),jo=!1;var f=t.memoizedState;i.state=f,Vo(t,r,i,a),s=t.memoizedState,l!==r||f!==s||Ta.current||jo?("function"==typeof c&&(rl(t,n,c,r),s=t.memoizedState),(l=jo||ol(t,n,l,r,f,s,u))?(d||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=u,r=l):("function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Ao(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:nl(t.type,l),i.props=u,d=t.pendingProps,f=i.context,"object"==typeof(s=n.contextType)&&null!==s?s=Oo(s):s=Oa(t,s=Ra(n)?Pa:za.current);var p=n.getDerivedStateFromProps;(c="function"==typeof p||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l!==d||f!==s)&&ll(t,i,r,s),jo=!1,f=t.memoizedState,i.state=f,Vo(t,r,i,a);var m=t.memoizedState;l!==d||f!==m||Ta.current||jo?("function"==typeof p&&(rl(t,n,p,r),m=t.memoizedState),(u=jo||ol(t,n,u,r,f,m,s)||!1)?(c||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,m,s),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,m,s)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),i.props=r,i.state=m,i.context=s,r=u):("function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Nl(e,t,n,r,o,a)}function Nl(e,t,n,r,a,o){Sl(e,t);var i=!!(128&t.flags);if(!r&&!i)return a&&Fa(t,n,!1),Vl(e,t,o);r=t.stateNode,vl.current=t;var l=i&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=ko(t,e.child,null,o),t.child=ko(t,null,l,o)):yl(e,t,l,o),t.memoizedState=r.state,a&&Fa(t,n,!0),t.child}function zl(e){var t=e.stateNode;t.pendingContext?Ma(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ma(0,t.context,!1),Go(e,t.containerInfo)}function Tl(e,t,n,r,a){return mo(),ho(a),t.flags|=256,yl(e,t,n,r),t.child}var Pl,Ol,Rl,Ll,Ml={dehydrated:null,treeContext:null,retryLane:0};function Il(e){return{baseLanes:e,cachePool:null,transitions:null}}function jl(e,t,n){var r,a=t.pendingProps,i=ei.current,l=!1,s=!!(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&!!(2&i)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Ca(ei,1&i),null===e)return uo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=a.children,e=a.fallback,l?(a=t.mode,l=t.child,s={mode:"hidden",children:s},1&a||null===l?l=Iu(s,a,0,null):(l.childLanes=0,l.pendingProps=s),e=Mu(e,a,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=Il(n),t.memoizedState=Ml,e):Fl(t,s));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,a,i,l){if(n)return 256&t.flags?(t.flags&=-257,Al(e,t,l,r=cl(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,a=t.mode,r=Iu({mode:"visible",children:r.children},a,0,null),(i=Mu(i,a,l,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,1&t.mode&&ko(t,e.child,null,l),t.child.memoizedState=Il(l),t.memoizedState=Ml,i);if(!(1&t.mode))return Al(e,t,l,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Al(e,t,l,r=cl(i=Error(o(419)),r,void 0))}if(s=0!==(l&e.childLanes),bl||s){if(null!==(r=zs)){switch(l&-l){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|l))?0:a)&&a!==i.retryLane&&(i.retryLane=a,Io(e,a),tu(r,e,a,-1))}return mu(),Al(e,t,l,r=cl(Error(o(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Cu.bind(null,e),a._reactRetry=t,null):(e=i.treeContext,ro=ua(a.nextSibling),no=t,ao=!0,oo=null,null!==e&&(Qa[Ka++]=Ga,Qa[Ka++]=Ya,Qa[Ka++]=qa,Ga=e.id,Ya=e.overflow,qa=t),t=Fl(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,i,n);if(l){l=a.fallback,s=t.mode,r=(i=e.child).sibling;var u={mode:"hidden",children:a.children};return 1&s||t.child===i?(a=Ru(i,u)).subtreeFlags=14680064&i.subtreeFlags:((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null),null!==r?l=Ru(r,l):(l=Mu(l,s,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,s=null===(s=e.child.memoizedState)?Il(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=Ml,a}return e=(l=e.child).sibling,a=Ru(l,{mode:"visible",children:a.children}),!(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Fl(e,t){return(t=Iu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Al(e,t,n,r){return null!==r&&ho(r),ko(t,e.child,null,n),(e=Fl(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Dl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),To(e.return,t,n)}function $l(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Ul(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(yl(e,t,r.children,n),2&(r=ei.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Dl(e,n,t);else if(19===e.tag)Dl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ca(ei,r),1&t.mode)switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ti(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),$l(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ti(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}$l(t,!0,n,null,o);break;case"together":$l(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Zl(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Is|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Ru(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ru(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Bl(e,t){if(!ao)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Wl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Hl(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Wl(t),null;case 1:case 17:return Ra(t.type)&&La(),Wl(t),null;case 3:return r=t.stateNode,Yo(),Ea(Ta),Ea(za),ri(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==oo&&(ou(oo),oo=null))),Ol(e,t),Wl(t),null;case 5:Jo(t);var a=qo(Ko.current);if(n=t.type,null!==e&&null!=t.stateNode)Rl(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return Wl(t),null}if(e=qo(Ho.current),fo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[fa]=t,r[pa]=i,e=!!(1&t.mode),n){case"dialog":Dr("cancel",r),Dr("close",r);break;case"iframe":case"object":case"embed":Dr("load",r);break;case"video":case"audio":for(a=0;a<Ir.length;a++)Dr(Ir[a],r);break;case"source":Dr("error",r);break;case"img":case"image":case"link":Dr("error",r),Dr("load",r);break;case"details":Dr("toggle",r);break;case"input":G(r,i),Dr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Dr("invalid",r);break;case"textarea":ae(r,i),Dr("invalid",r)}for(var s in be(n,i),a=null,i)if(i.hasOwnProperty(s)){var u=i[s];"children"===s?"string"==typeof u?r.textContent!==u&&(!0!==i.suppressHydrationWarning&&Xr(r.textContent,u,e),a=["children",u]):"number"==typeof u&&r.textContent!==""+u&&(!0!==i.suppressHydrationWarning&&Xr(r.textContent,u,e),a=["children",""+u]):l.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Dr("scroll",r)}switch(n){case"input":H(r),J(r,i,!0);break;case"textarea":H(r),ie(r);break;case"select":case"option":break;default:"function"==typeof i.onClick&&(r.onclick=Jr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[pa]=r,Pl(e,t,!1,!1),t.stateNode=e;e:{switch(s=ye(n,r),n){case"dialog":Dr("cancel",e),Dr("close",e),a=r;break;case"iframe":case"object":case"embed":Dr("load",e),a=r;break;case"video":case"audio":for(a=0;a<Ir.length;a++)Dr(Ir[a],e);a=r;break;case"source":Dr("error",e),a=r;break;case"img":case"image":case"link":Dr("error",e),Dr("load",e),a=r;break;case"details":Dr("toggle",e),a=r;break;case"input":G(e,r),a=q(e,r),Dr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=F({},r,{value:void 0}),Dr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Dr("invalid",e)}for(i in be(n,a),u=a)if(u.hasOwnProperty(i)){var c=u[i];"style"===i?ge(e,c):"dangerouslySetInnerHTML"===i?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===i?"string"==typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"==typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(l.hasOwnProperty(i)?null!=c&&"onScroll"===i&&Dr("scroll",e):null!=c&&y(e,i,c,s))}switch(n){case"input":H(e),J(e,r,!1);break;case"textarea":H(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+B(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof a.onClick&&(e.onclick=Jr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Wl(t),null;case 6:if(e&&null!=t.stateNode)Ll(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(o(166));if(n=qo(Ko.current),qo(Ho.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(i=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Xr(r.nodeValue,n,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Xr(r.nodeValue,n,!!(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Wl(t),null;case 13:if(Ea(ei),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ao&&null!==ro&&1&t.mode&&!(128&t.flags))po(),mo(),t.flags|=98560,i=!1;else if(i=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(o(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(o(317));i[fa]=t}else mo(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Wl(t),i=!1}else null!==oo&&(ou(oo),oo=null),i=!0;if(!i)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,1&t.mode&&(null===e||1&ei.current?0===Ls&&(Ls=3):mu())),null!==t.updateQueue&&(t.flags|=4),Wl(t),null);case 4:return Yo(),Ol(e,t),null===e&&Zr(t.stateNode.containerInfo),Wl(t),null;case 10:return zo(t.type._context),Wl(t),null;case 19:if(Ea(ei),null===(i=t.memoizedState))return Wl(t),null;if(r=!!(128&t.flags),null===(s=i.rendering))if(r)Bl(i,!1);else{if(0!==Ls||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(s=ti(e))){for(t.flags|=128,Bl(i,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(s=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ca(ei,1&ei.current|2),t.child}e=e.sibling}null!==i.tail&&Ye()>Us&&(t.flags|=128,r=!0,Bl(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ti(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Bl(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!ao)return Wl(t),null}else 2*Ye()-i.renderingStartTime>Us&&1073741824!==n&&(t.flags|=128,r=!0,Bl(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=i.last)?n.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Ye(),t.sibling=null,n=ei.current,Ca(ei,r?1&n|2:1&n),t):(Wl(t),null);case 22:case 23:return cu(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&1&t.mode?!!(1073741824&Os)&&(Wl(t),6&t.subtreeFlags&&(t.flags|=8192)):Wl(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function Ql(e,t){switch(to(t),t.tag){case 1:return Ra(t.type)&&La(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Yo(),Ea(Ta),Ea(za),ri(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Jo(t),null;case 13:if(Ea(ei),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));mo()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ea(ei),null;case 4:return Yo(),null;case 10:return zo(t.type._context),null;case 22:case 23:return cu(),null;default:return null}}Pl=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ol=function(){},Rl=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,qo(Ho.current);var o,i=null;switch(n){case"input":a=q(e,a),r=q(e,r),i=[];break;case"select":a=F({},a,{value:void 0}),r=F({},r,{value:void 0}),i=[];break;case"textarea":a=re(e,a),r=re(e,r),i=[];break;default:"function"!=typeof a.onClick&&"function"==typeof r.onClick&&(e.onclick=Jr)}for(c in be(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(l.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(o in s)!s.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&s[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(i=i||[]).push(c,u)):"children"===c?"string"!=typeof u&&"number"!=typeof u||(i=i||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(l.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Dr("scroll",e),i||s===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}},Ll=function(e,t,n,r){n!==r&&(t.flags|=4)};var Kl=!1,ql=!1,Gl="function"==typeof WeakSet?WeakSet:Set,Yl=null;function Xl(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(n){_u(e,t,n)}else n.current=null}function Jl(e,t,n){try{n()}catch(n){_u(e,t,n)}}var es=!1;function ts(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&Jl(t,n,o)}a=a.next}while(a!==r)}}function ns(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function rs(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function as(e){var t=e.alternate;null!==t&&(e.alternate=null,as(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ha],delete t[ga],delete t[va])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function os(e){return 5===e.tag||3===e.tag||4===e.tag}function is(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||os(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ls(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Jr));else if(4!==r&&null!==(e=e.child))for(ls(e,t,n),e=e.sibling;null!==e;)ls(e,t,n),e=e.sibling}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}var us=null,cs=!1;function ds(e,t,n){for(n=n.child;null!==n;)fs(e,t,n),n=n.sibling}function fs(e,t,n){if(ot&&"function"==typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(at,n)}catch(e){}switch(n.tag){case 5:ql||Xl(n,t);case 6:var r=us,a=cs;us=null,ds(e,t,n),cs=a,null!==(us=r)&&(cs?(e=us,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):us.removeChild(n.stateNode));break;case 18:null!==us&&(cs?(e=us,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Zt(e)):sa(us,n.stateNode));break;case 4:r=us,a=cs,us=n.stateNode.containerInfo,cs=!0,ds(e,t,n),us=r,cs=a;break;case 0:case 11:case 14:case 15:if(!ql&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,i=o.destroy;o=o.tag,void 0!==i&&(2&o||4&o)&&Jl(n,t,i),a=a.next}while(a!==r)}ds(e,t,n);break;case 1:if(!ql&&(Xl(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(e){_u(n,t,e)}ds(e,t,n);break;case 21:ds(e,t,n);break;case 22:1&n.mode?(ql=(r=ql)||null!==n.memoizedState,ds(e,t,n),ql=r):ds(e,t,n);break;default:ds(e,t,n)}}function ps(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Gl),t.forEach(function(t){var r=Nu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ms(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var i=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:us=s.stateNode,cs=!1;break e;case 3:case 4:us=s.stateNode.containerInfo,cs=!0;break e}s=s.return}if(null===us)throw Error(o(160));fs(i,l,a),us=null,cs=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(e){_u(a,t,e)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)hs(t,e),t=t.sibling}function hs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ms(t,e),gs(e),4&r){try{ts(3,e,e.return),ns(3,e)}catch(t){_u(e,e.return,t)}try{ts(5,e,e.return)}catch(t){_u(e,e.return,t)}}break;case 1:ms(t,e),gs(e),512&r&&null!==n&&Xl(n,n.return);break;case 5:if(ms(t,e),gs(e),512&r&&null!==n&&Xl(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(t){_u(e,e.return,t)}}if(4&r&&null!=(a=e.stateNode)){var i=e.memoizedProps,l=null!==n?n.memoizedProps:i,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===i.type&&null!=i.name&&Y(a,i),ye(s,l);var c=ye(s,i);for(l=0;l<u.length;l+=2){var d=u[l],f=u[l+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):y(a,d,f,c)}switch(s){case"input":X(a,i);break;case"textarea":oe(a,i);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!i.multiple;var m=i.value;null!=m?ne(a,!!i.multiple,m,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(a,!!i.multiple,i.defaultValue,!0):ne(a,!!i.multiple,i.multiple?[]:"",!1))}a[pa]=i}catch(t){_u(e,e.return,t)}}break;case 6:if(ms(t,e),gs(e),4&r){if(null===e.stateNode)throw Error(o(162));a=e.stateNode,i=e.memoizedProps;try{a.nodeValue=i}catch(t){_u(e,e.return,t)}}break;case 3:if(ms(t,e),gs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Zt(t.containerInfo)}catch(t){_u(e,e.return,t)}break;case 4:default:ms(t,e),gs(e);break;case 13:ms(t,e),gs(e),8192&(a=e.child).flags&&(i=null!==a.memoizedState,a.stateNode.isHidden=i,!i||null!==a.alternate&&null!==a.alternate.memoizedState||($s=Ye())),4&r&&ps(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(ql=(c=ql)||d,ms(t,e),ql=c):ms(t,e),gs(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&1&e.mode)for(Yl=e,d=e.child;null!==d;){for(f=Yl=d;null!==Yl;){switch(m=(p=Yl).child,p.tag){case 0:case 11:case 14:case 15:ts(4,p,p.return);break;case 1:Xl(p,p.return);var h=p.stateNode;if("function"==typeof h.componentWillUnmount){r=p,n=p.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(e){_u(r,n,e)}}break;case 5:Xl(p,p.return);break;case 22:if(null!==p.memoizedState){ws(f);continue}}null!==m?(m.return=p,Yl=m):ws(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"==typeof(i=a.style).setProperty?i.setProperty("display","none","important"):i.display="none":(s=f.stateNode,l=null!=(u=f.memoizedProps.style)&&u.hasOwnProperty("display")?u.display:null,s.style.display=he("display",l))}catch(t){_u(e,e.return,t)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(t){_u(e,e.return,t)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ms(t,e),gs(e),4&r&&ps(e);case 21:}}function gs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(os(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),ss(e,is(e),a);break;case 3:case 4:var i=r.stateNode.containerInfo;ls(e,is(e),i);break;default:throw Error(o(161))}}catch(t){_u(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vs(e,t,n){Yl=e,bs(e,t,n)}function bs(e,t,n){for(var r=!!(1&e.mode);null!==Yl;){var a=Yl,o=a.child;if(22===a.tag&&r){var i=null!==a.memoizedState||Kl;if(!i){var l=a.alternate,s=null!==l&&null!==l.memoizedState||ql;l=Kl;var u=ql;if(Kl=i,(ql=s)&&!u)for(Yl=a;null!==Yl;)s=(i=Yl).child,22===i.tag&&null!==i.memoizedState?ks(a):null!==s?(s.return=i,Yl=s):ks(a);for(;null!==o;)Yl=o,bs(o,t,n),o=o.sibling;Yl=a,Kl=l,ql=u}ys(e)}else 8772&a.subtreeFlags&&null!==o?(o.return=a,Yl=o):ys(e)}}function ys(e){for(;null!==Yl;){var t=Yl;if(8772&t.flags){var n=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:ql||ns(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!ql)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:nl(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Bo(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Bo(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Zt(f)}}}break;default:throw Error(o(163))}ql||512&t.flags&&rs(t)}catch(e){_u(t,t.return,e)}}if(t===e){Yl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Yl=n;break}Yl=t.return}}function ws(e){for(;null!==Yl;){var t=Yl;if(t===e){Yl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Yl=n;break}Yl=t.return}}function ks(e){for(;null!==Yl;){var t=Yl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ns(4,t)}catch(e){_u(t,n,e)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(e){_u(t,a,e)}}var o=t.return;try{rs(t)}catch(e){_u(t,o,e)}break;case 5:var i=t.return;try{rs(t)}catch(e){_u(t,i,e)}}}catch(e){_u(t,t.return,e)}if(t===e){Yl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Yl=l;break}Yl=t.return}}var xs,_s=Math.ceil,Ss=w.ReactCurrentDispatcher,Es=w.ReactCurrentOwner,Cs=w.ReactCurrentBatchConfig,Ns=0,zs=null,Ts=null,Ps=0,Os=0,Rs=Sa(0),Ls=0,Ms=null,Is=0,js=0,Fs=0,As=null,Ds=null,$s=0,Us=1/0,Zs=null,Vs=!1,Bs=null,Ws=null,Hs=!1,Qs=null,Ks=0,qs=0,Gs=null,Ys=-1,Xs=0;function Js(){return 6&Ns?Ye():-1!==Ys?Ys:Ys=Ye()}function eu(e){return 1&e.mode?2&Ns&&0!==Ps?Ps&-Ps:null!==go.transition?(0===Xs&&(Xs=ht()),Xs):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Gt(e.type):1}function tu(e,t,n,r){if(50<qs)throw qs=0,Gs=null,Error(o(185));vt(e,n,r),2&Ns&&e===zs||(e===zs&&(!(2&Ns)&&(js|=n),4===Ls&&iu(e,Ps)),nu(e,r),1===n&&0===Ns&&!(1&t.mode)&&(Us=Ye()+500,Da&&Za()))}function nu(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-it(o),l=1<<i,s=a[i];-1===s?0!==(l&n)&&0===(l&r)||(a[i]=pt(l,t)):s<=t&&(e.expiredLanes|=l),o&=~l}}(e,t);var r=ft(e,e===zs?Ps:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Da=!0,Ua(e)}(lu.bind(null,e)):Ua(lu.bind(null,e)),ia(function(){!(6&Ns)&&Za()}),n=null;else{switch(wt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=zu(n,ru.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ru(e,t){if(Ys=-1,Xs=0,6&Ns)throw Error(o(327));var n=e.callbackNode;if(ku()&&e.callbackNode!==n)return null;var r=ft(e,e===zs?Ps:0);if(0===r)return null;if(30&r||0!==(r&e.expiredLanes)||t)t=hu(e,r);else{t=r;var a=Ns;Ns|=2;var i=pu();for(zs===e&&Ps===t||(Zs=null,Us=Ye()+500,du(e,t));;)try{vu();break}catch(t){fu(e,t)}No(),Ss.current=i,Ns=a,null!==Ts?t=0:(zs=null,Ps=0,t=Ls)}if(0!==t){if(2===t&&(0!==(a=mt(e))&&(r=a,t=au(e,a))),1===t)throw n=Ms,du(e,0),iu(e,r),nu(e,Ye()),n;if(6===t)iu(e,r);else{if(a=e.current.alternate,!(30&r||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!lr(o(),a))return!1}catch(e){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)||(t=hu(e,r),2===t&&(i=mt(e),0!==i&&(r=i,t=au(e,i))),1!==t)))throw n=Ms,du(e,0),iu(e,r),nu(e,Ye()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:wu(e,Ds,Zs);break;case 3:if(iu(e,r),(130023424&r)===r&&10<(t=$s+500-Ye())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){Js(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wu.bind(null,e,Ds,Zs),t);break}wu(e,Ds,Zs);break;case 4:if(iu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var l=31-it(r);i=1<<l,(l=t[l])>a&&(a=l),r&=~i}if(r=a,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*_s(r/1960))-r)){e.timeoutHandle=ra(wu.bind(null,e,Ds,Zs),r);break}wu(e,Ds,Zs);break;default:throw Error(o(329))}}}return nu(e,Ye()),e.callbackNode===n?ru.bind(null,e):null}function au(e,t){var n=As;return e.current.memoizedState.isDehydrated&&(du(e,t).flags|=256),2!==(e=hu(e,t))&&(t=Ds,Ds=n,null!==t&&ou(t)),e}function ou(e){null===Ds?Ds=e:Ds.push.apply(Ds,e)}function iu(e,t){for(t&=~Fs,t&=~js,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(6&Ns)throw Error(o(327));ku();var t=ft(e,0);if(!(1&t))return nu(e,Ye()),null;var n=hu(e,t);if(0!==e.tag&&2===n){var r=mt(e);0!==r&&(t=r,n=au(e,r))}if(1===n)throw n=Ms,du(e,0),iu(e,t),nu(e,Ye()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,Ds,Zs),nu(e,Ye()),null}function su(e,t){var n=Ns;Ns|=1;try{return e(t)}finally{0===(Ns=n)&&(Us=Ye()+500,Da&&Za())}}function uu(e){null!==Qs&&0===Qs.tag&&!(6&Ns)&&ku();var t=Ns;Ns|=1;var n=Cs.transition,r=yt;try{if(Cs.transition=null,yt=1,e)return e()}finally{yt=r,Cs.transition=n,!(6&(Ns=t))&&Za()}}function cu(){Os=Rs.current,Ea(Rs)}function du(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Ts)for(n=Ts.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&La();break;case 3:Yo(),Ea(Ta),Ea(za),ri();break;case 5:Jo(r);break;case 4:Yo();break;case 13:case 19:Ea(ei);break;case 10:zo(r.type._context);break;case 22:case 23:cu()}n=n.return}if(zs=e,Ts=e=Ru(e.current,null),Ps=Os=t,Ls=0,Ms=null,Fs=js=Is=0,Ds=As=null,null!==Ro){for(t=0;t<Ro.length;t++)if(null!==(r=(n=Ro[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var i=o.next;o.next=a,r.next=i}n.pending=r}Ro=null}return e}function fu(e,t){for(;;){var n=Ts;try{if(No(),ai.current=Xi,ci){for(var r=li.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}ci=!1}if(ii=0,ui=si=li=null,di=!1,fi=0,Es.current=null,null===n||null===n.return){Ls=1,Ms=t,Ts=null;break}e:{var i=e,l=n.return,s=n,u=t;if(t=Ps,s.flags|=32768,null!==u&&"object"==typeof u&&"function"==typeof u.then){var c=u,d=s,f=d.tag;if(!(1&d.mode||0!==f&&11!==f&&15!==f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=hl(l);if(null!==m){m.flags&=-257,gl(m,l,s,0,t),1&m.mode&&ml(i,c,t),u=c;var h=(t=m).updateQueue;if(null===h){var g=new Set;g.add(u),t.updateQueue=g}else h.add(u);break e}if(!(1&t)){ml(i,c,t),mu();break e}u=Error(o(426))}else if(ao&&1&s.mode){var v=hl(l);if(null!==v){!(65536&v.flags)&&(v.flags|=256),gl(v,l,s,0,t),ho(ul(u,s));break e}}i=u=ul(u,s),4!==Ls&&(Ls=2),null===As?As=[i]:As.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Zo(i,fl(0,u,t));break e;case 1:s=u;var b=i.type,y=i.stateNode;if(!(128&i.flags||"function"!=typeof b.getDerivedStateFromError&&(null===y||"function"!=typeof y.componentDidCatch||null!==Ws&&Ws.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t,Zo(i,pl(i,s,t));break e}}i=i.return}while(null!==i)}yu(n)}catch(e){t=e,Ts===n&&null!==n&&(Ts=n=n.return);continue}break}}function pu(){var e=Ss.current;return Ss.current=Xi,null===e?Xi:e}function mu(){0!==Ls&&3!==Ls&&2!==Ls||(Ls=4),null===zs||!(268435455&Is)&&!(268435455&js)||iu(zs,Ps)}function hu(e,t){var n=Ns;Ns|=2;var r=pu();for(zs===e&&Ps===t||(Zs=null,du(e,t));;)try{gu();break}catch(t){fu(e,t)}if(No(),Ns=n,Ss.current=r,null!==Ts)throw Error(o(261));return zs=null,Ps=0,Ls}function gu(){for(;null!==Ts;)bu(Ts)}function vu(){for(;null!==Ts&&!qe();)bu(Ts)}function bu(e){var t=xs(e.alternate,e,Os);e.memoizedProps=e.pendingProps,null===t?yu(e):Ts=t,Es.current=null}function yu(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=Ql(n,t)))return n.flags&=32767,void(Ts=n);if(null===e)return Ls=6,void(Ts=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=Hl(n,t,Os)))return void(Ts=n);if(null!==(t=t.sibling))return void(Ts=t);Ts=t=e}while(null!==t);0===Ls&&(Ls=5)}function wu(e,t,n){var r=yt,a=Cs.transition;try{Cs.transition=null,yt=1,function(e,t,n,r){do{ku()}while(null!==Qs);if(6&Ns)throw Error(o(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-it(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,i),e===zs&&(Ts=zs=null,Ps=0),!(2064&n.subtreeFlags)&&!(2064&n.flags)||Hs||(Hs=!0,zu(tt,function(){return ku(),null})),i=!!(15990&n.flags),!!(15990&n.subtreeFlags)||i){i=Cs.transition,Cs.transition=null;var l=yt;yt=1;var s=Ns;Ns|=4,Es.current=null,function(e,t){if(ea=Bt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(e){n=null;break e}var l=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==a&&3!==f.nodeType||(s=l+a),f!==i||0!==r&&3!==f.nodeType||(u=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=l),p===i&&++d===r&&(u=l),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Bt=!1,Yl=t;null!==Yl;)if(e=(t=Yl).child,1028&t.subtreeFlags&&null!==e)e.return=t,Yl=e;else for(;null!==Yl;){t=Yl;try{var h=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var g=h.memoizedProps,v=h.memoizedState,b=t.stateNode,y=b.getSnapshotBeforeUpdate(t.elementType===t.type?g:nl(t.type,g),v);b.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(o(163))}}catch(e){_u(t,t.return,e)}if(null!==(e=t.sibling)){e.return=t.return,Yl=e;break}Yl=t.return}h=es,es=!1}(e,n),hs(n,e),mr(ta),Bt=!!ea,ta=ea=null,e.current=n,vs(n,e,a),Ge(),Ns=s,yt=l,Cs.transition=i}else e.current=n;if(Hs&&(Hs=!1,Qs=e,Ks=a),i=e.pendingLanes,0===i&&(Ws=null),function(e){if(ot&&"function"==typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(at,e,void 0,!(128&~e.current.flags))}catch(e){}}(n.stateNode),nu(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Vs)throw Vs=!1,e=Bs,Bs=null,e;!!(1&Ks)&&0!==e.tag&&ku(),i=e.pendingLanes,1&i?e===Gs?qs++:(qs=0,Gs=e):qs=0,Za()}(e,t,n,r)}finally{Cs.transition=a,yt=r}return null}function ku(){if(null!==Qs){var e=wt(Ks),t=Cs.transition,n=yt;try{if(Cs.transition=null,yt=16>e?16:e,null===Qs)var r=!1;else{if(e=Qs,Qs=null,Ks=0,6&Ns)throw Error(o(331));var a=Ns;for(Ns|=4,Yl=e.current;null!==Yl;){var i=Yl,l=i.child;if(16&Yl.flags){var s=i.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Yl=c;null!==Yl;){var d=Yl;switch(d.tag){case 0:case 11:case 15:ts(8,d,i)}var f=d.child;if(null!==f)f.return=d,Yl=f;else for(;null!==Yl;){var p=(d=Yl).sibling,m=d.return;if(as(d),d===c){Yl=null;break}if(null!==p){p.return=m,Yl=p;break}Yl=m}}}var h=i.alternate;if(null!==h){var g=h.child;if(null!==g){h.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Yl=i}}if(2064&i.subtreeFlags&&null!==l)l.return=i,Yl=l;else e:for(;null!==Yl;){if(2048&(i=Yl).flags)switch(i.tag){case 0:case 11:case 15:ts(9,i,i.return)}var b=i.sibling;if(null!==b){b.return=i.return,Yl=b;break e}Yl=i.return}}var y=e.current;for(Yl=y;null!==Yl;){var w=(l=Yl).child;if(2064&l.subtreeFlags&&null!==w)w.return=l,Yl=w;else e:for(l=y;null!==Yl;){if(2048&(s=Yl).flags)try{switch(s.tag){case 0:case 11:case 15:ns(9,s)}}catch(e){_u(s,s.return,e)}if(s===l){Yl=null;break e}var k=s.sibling;if(null!==k){k.return=s.return,Yl=k;break e}Yl=s.return}}if(Ns=a,Za(),ot&&"function"==typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(at,e)}catch(e){}r=!0}return r}finally{yt=n,Cs.transition=t}}return!1}function xu(e,t,n){e=$o(e,t=fl(0,t=ul(n,t),1),1),t=Js(),null!==e&&(vt(e,1,t),nu(e,t))}function _u(e,t,n){if(3===e.tag)xu(e,e,n);else for(;null!==t;){if(3===t.tag){xu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Ws||!Ws.has(r))){t=$o(t,e=pl(t,e=ul(n,e),1),1),e=Js(),null!==t&&(vt(t,1,e),nu(t,e));break}}t=t.return}}function Su(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=Js(),e.pingedLanes|=e.suspendedLanes&n,zs===e&&(Ps&n)===n&&(4===Ls||3===Ls&&(130023424&Ps)===Ps&&500>Ye()-$s?du(e,0):Fs|=n),nu(e,t)}function Eu(e,t){0===t&&(1&e.mode?(t=ct,!(130023424&(ct<<=1))&&(ct=4194304)):t=1);var n=Js();null!==(e=Io(e,t))&&(vt(e,t,n),nu(e,n))}function Cu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Eu(e,n)}function Nu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Eu(e,n)}function zu(e,t){return Qe(e,t)}function Tu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Pu(e,t,n,r){return new Tu(e,t,n,r)}function Ou(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ru(e,t){var n=e.alternate;return null===n?((n=Pu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Lu(e,t,n,r,a,i){var l=2;if(r=e,"function"==typeof e)Ou(e)&&(l=1);else if("string"==typeof e)l=5;else e:switch(e){case _:return Mu(n.children,a,i,t);case S:l=8,a|=8;break;case E:return(e=Pu(12,n,t,2|a)).elementType=E,e.lanes=i,e;case T:return(e=Pu(13,n,t,a)).elementType=T,e.lanes=i,e;case P:return(e=Pu(19,n,t,a)).elementType=P,e.lanes=i,e;case L:return Iu(n,a,i,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case C:l=10;break e;case N:l=9;break e;case z:l=11;break e;case O:l=14;break e;case R:l=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Pu(l,n,t,a)).elementType=e,t.type=r,t.lanes=i,t}function Mu(e,t,n,r){return(e=Pu(7,e,r,t)).lanes=n,e}function Iu(e,t,n,r){return(e=Pu(22,e,r,t)).elementType=L,e.lanes=n,e.stateNode={isHidden:!1},e}function ju(e,t,n){return(e=Pu(6,e,null,t)).lanes=n,e}function Fu(e,t,n){return(t=Pu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Au(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Du(e,t,n,r,a,o,i,l,s){return e=new Au(e,t,n,l,s),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Pu(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fo(o),e}function $u(e){if(!e)return Na;e:{if(Ze(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ra(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Ra(n))return Ia(e,n,t)}return t}function Uu(e,t,n,r,a,o,i,l,s){return(e=Du(n,r,!0,e,0,o,0,l,s)).context=$u(null),n=e.current,(o=Do(r=Js(),a=eu(n))).callback=null!=t?t:null,$o(n,o,a),e.current.lanes=a,vt(e,a,r),nu(e,r),e}function Zu(e,t,n,r){var a=t.current,o=Js(),i=eu(a);return n=$u(n),null===t.context?t.context=n:t.pendingContext=n,(t=Do(o,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=$o(a,t,i))&&(tu(e,a,i,o),Uo(e,a,i)),i}function Vu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Bu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Wu(e,t){Bu(e,t),(e=e.alternate)&&Bu(e,t)}xs=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ta.current)bl=!0;else{if(0===(e.lanes&n)&&!(128&t.flags))return bl=!1,function(e,t,n){switch(t.tag){case 3:zl(t),mo();break;case 5:Xo(t);break;case 1:Ra(t.type)&&ja(t);break;case 4:Go(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ca(_o,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ca(ei,1&ei.current),t.flags|=128,null):0!==(n&t.child.childLanes)?jl(e,t,n):(Ca(ei,1&ei.current),null!==(e=Vl(e,t,n))?e.sibling:null);Ca(ei,1&ei.current);break;case 19:if(r=0!==(n&t.childLanes),128&e.flags){if(r)return Ul(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ca(ei,ei.current),r)break;return null;case 22:case 23:return t.lanes=0,_l(e,t,n)}return Vl(e,t,n)}(e,t,n);bl=!!(131072&e.flags)}else bl=!1,ao&&1048576&t.flags&&Ja(t,Ha,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Zl(e,t),e=t.pendingProps;var a=Oa(t,za.current);Po(t,n),a=gi(null,t,r,e,a,n);var i=vi();return t.flags|=1,"object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ra(r)?(i=!0,ja(t)):i=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Fo(t),a.updater=al,t.stateNode=a,a._reactInternals=t,sl(t,r,e,n),t=Nl(null,t,r,!0,i,n)):(t.tag=0,ao&&i&&eo(t),yl(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Zl(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"==typeof e)return Ou(e)?1:0;if(null!=e){if((e=e.$$typeof)===z)return 11;if(e===O)return 14}return 2}(r),e=nl(r,e),a){case 0:t=El(null,t,r,e,n);break e;case 1:t=Cl(null,t,r,e,n);break e;case 11:t=wl(null,t,r,e,n);break e;case 14:t=kl(null,t,r,nl(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,El(e,t,r,a=t.elementType===r?a:nl(r,a),n);case 1:return r=t.type,a=t.pendingProps,Cl(e,t,r,a=t.elementType===r?a:nl(r,a),n);case 3:e:{if(zl(t),null===e)throw Error(o(387));r=t.pendingProps,a=(i=t.memoizedState).element,Ao(e,t),Vo(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Tl(e,t,r,n,a=ul(Error(o(423)),t));break e}if(r!==a){t=Tl(e,t,r,n,a=ul(Error(o(424)),t));break e}for(ro=ua(t.stateNode.containerInfo.firstChild),no=t,ao=!0,oo=null,n=xo(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(mo(),r===a){t=Vl(e,t,n);break e}yl(e,t,r,n)}t=t.child}return t;case 5:return Xo(t),null===e&&uo(t),r=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,l=a.children,na(r,a)?l=null:null!==i&&na(r,i)&&(t.flags|=32),Sl(e,t),yl(e,t,l,n),t.child;case 6:return null===e&&uo(t),null;case 13:return jl(e,t,n);case 4:return Go(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ko(t,null,r,n):yl(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,wl(e,t,r,a=t.elementType===r?a:nl(r,a),n);case 7:return yl(e,t,t.pendingProps,n),t.child;case 8:case 12:return yl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,i=t.memoizedProps,l=a.value,Ca(_o,r._currentValue),r._currentValue=l,null!==i)if(lr(i.value,l)){if(i.children===a.children&&!Ta.current){t=Vl(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var s=i.dependencies;if(null!==s){l=i.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===i.tag){(u=Do(-1,n&-n)).tag=2;var c=i.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),To(i.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(o(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),To(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}yl(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Po(t,n),r=r(a=Oo(a)),t.flags|=1,yl(e,t,r,n),t.child;case 14:return a=nl(r=t.type,t.pendingProps),kl(e,t,r,a=nl(r.type,a),n);case 15:return xl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:nl(r,a),Zl(e,t),t.tag=1,Ra(r)?(e=!0,ja(t)):e=!1,Po(t,n),il(t,r,a),sl(t,r,a,n),Nl(null,t,r,!0,e,n);case 19:return Ul(e,t,n);case 22:return _l(e,t,n)}throw Error(o(156,t.tag))};var Hu="function"==typeof reportError?reportError:function(e){};function Qu(e){this._internalRoot=e}function Ku(e){this._internalRoot=e}function qu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Yu(){}function Xu(e,t,n,r,a){var o=n._reactRootContainer;if(o){var i=o;if("function"==typeof a){var l=a;a=function(){var e=Vu(i);l.call(e)}}Zu(t,i,e,a)}else i=function(e,t,n,r,a){if(a){if("function"==typeof r){var o=r;r=function(){var e=Vu(i);o.call(e)}}var i=Uu(t,r,e,0,null,!1,0,"",Yu);return e._reactRootContainer=i,e[ma]=i.current,Zr(8===e.nodeType?e.parentNode:e),uu(),i}for(;a=e.lastChild;)e.removeChild(a);if("function"==typeof r){var l=r;r=function(){var e=Vu(s);l.call(e)}}var s=Du(e,0,!1,null,0,!1,0,"",Yu);return e._reactRootContainer=s,e[ma]=s.current,Zr(8===e.nodeType?e.parentNode:e),uu(function(){Zu(t,s,n,r)}),s}(n,t,e,a,r);return Vu(i)}Ku.prototype.render=Qu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Zu(e,t,null,null)},Ku.prototype.unmount=Qu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uu(function(){Zu(null,e,null,null)}),t[ma]=null}},Ku.prototype.unstable_scheduleHydration=function(e){if(e){var t=St();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&0!==t&&t<Lt[n].priority;n++);Lt.splice(n,0,e),0===n&&Ft(e)}},kt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(bt(t,1|n),nu(t,Ye()),!(6&Ns)&&(Us=Ye()+500,Za()))}break;case 13:uu(function(){var t=Io(e,1);if(null!==t){var n=Js();tu(t,e,1,n)}}),Wu(e,1)}},xt=function(e){if(13===e.tag){var t=Io(e,134217728);if(null!==t)tu(t,e,134217728,Js());Wu(e,134217728)}},_t=function(e){if(13===e.tag){var t=eu(e),n=Io(e,t);if(null!==n)tu(n,e,t,Js());Wu(e,t)}},St=function(){return yt},Et=function(e,t){var n=yt;try{return yt=e,t()}finally{yt=n}},xe=function(e,t,n){switch(t){case"input":if(X(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=ka(r);if(!a)throw Error(o(90));Q(r),X(r,a)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},ze=su,Te=uu;var Ju={usingClientEntryPoint:!1,Events:[ya,wa,ka,Ce,Ne,su]},ec={findFiberByHostInstance:ba,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},tc={bundleType:ec.bundleType,version:ec.version,rendererPackageName:ec.rendererPackageName,rendererConfig:ec.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=We(e))?null:e.stateNode},findFiberByHostInstance:ec.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var nc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!nc.isDisabled&&nc.supportsFiber)try{at=nc.inject(tc),ot=nc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ju,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!qu(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:x,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!qu(e))throw Error(o(299));var n=!1,r="",a=Hu;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Du(e,1,!1,null,0,n,0,r,a),e[ma]=t.current,Zr(8===e.nodeType?e.parentNode:e),new Qu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=We(t))?null:e.stateNode},t.flushSync=function(e){return uu(e)},t.hydrate=function(e,t,n){if(!Gu(t))throw Error(o(200));return Xu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!qu(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,a=!1,i="",l=Hu;if(null!=n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Uu(t,null,e,1,null!=n?n:null,a,0,i,l),e[ma]=t.current,Zr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Ku(t)},t.render=function(e,t,n){if(!Gu(t))throw Error(o(200));return Xu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Gu(e))throw Error(o(40));return!!e._reactRootContainer&&(uu(function(){Xu(null,null,e,!1,function(){e._reactRootContainer=null,e[ma]=null})}),!0)},t.unstable_batchedUpdates=su,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Gu(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return Xu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},601:e=>{e.exports=function(e){return e[1]}},659:e=>{var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},825:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var a=void 0!==n.layer;a&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,a&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var o=n.sourceMap;o&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},848:(e,t,n)=>{e.exports=n(20)},961:(e,t,n)=>{!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){}}(),e.exports=n(551)},982:(e,t,n)=>{e.exports=n(463)}},r={};function a(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={id:e,exports:{}};return n[e](o,o.exports,a),o.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var o=Object.create(null);a.r(o);var i={};e=e||[null,t({}),t([]),t(t)];for(var l=2&r&&n;"object"==typeof l&&!~e.indexOf(l);l=t(l))Object.getOwnPropertyNames(l).forEach(e=>i[e]=()=>n[e]);return i.default=()=>n,a.d(o,i),o},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.nc=void 0;var o,i,l=a(540),s=a.t(l,2),u=a(338);!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},e.getValidEnumValues=t=>{const n=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),r={};for(const e of n)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},e.find=(e,t)=>{for(const n of e)if(t(n))return n},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(o||(o={})),function(e){e.mergeShapes=(e,t)=>({...e,...t})}(i||(i={}));const c=o.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),d=e=>{switch(typeof e){case"undefined":return c.undefined;case"string":return c.string;case"number":return Number.isNaN(e)?c.nan:c.number;case"boolean":return c.boolean;case"function":return c.function;case"bigint":return c.bigint;case"symbol":return c.symbol;case"object":return Array.isArray(e)?c.array:null===e?c.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?c.promise:"undefined"!=typeof Map&&e instanceof Map?c.map:"undefined"!=typeof Set&&e instanceof Set?c.set:"undefined"!=typeof Date&&e instanceof Date?c.date:c.object;default:return c.unknown}},f=o.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class p extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(e){return e.message},n={_errors:[]},r=e=>{for(const a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(r);else if("invalid_return_type"===a.code)r(a.returnTypeError);else if("invalid_arguments"===a.code)r(a.argumentsError);else if(0===a.path.length)n._errors.push(t(a));else{let e=n,r=0;for(;r<a.path.length;){const n=a.path[r];r===a.path.length-1?(e[n]=e[n]||{_errors:[]},e[n]._errors.push(t(a))):e[n]=e[n]||{_errors:[]},e=e[n],r++}}};return r(this),n}static assert(e){if(!(e instanceof p))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,o.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}p.create=e=>new p(e);const m=(e,t)=>{let n;switch(e.code){case f.invalid_type:n=e.received===c.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case f.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,o.jsonStringifyReplacer)}`;break;case f.unrecognized_keys:n=`Unrecognized key(s) in object: ${o.joinValues(e.keys,", ")}`;break;case f.invalid_union:n="Invalid input";break;case f.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${o.joinValues(e.options)}`;break;case f.invalid_enum_value:n=`Invalid enum value. Expected ${o.joinValues(e.options)}, received '${e.received}'`;break;case f.invalid_arguments:n="Invalid function arguments";break;case f.invalid_return_type:n="Invalid function return type";break;case f.invalid_date:n="Invalid date";break;case f.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:o.assertNever(e.validation):n="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case f.too_small:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case f.too_big:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case f.custom:n="Invalid input";break;case f.invalid_intersection_types:n="Intersection results could not be merged";break;case f.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case f.not_finite:n="Number must be finite";break;default:n=t.defaultError,o.assertNever(e)}return{message:n}};let h=m;function g(){return h}const v=e=>{const{data:t,path:n,errorMaps:r,issueData:a}=e,o=[...n,...a.path||[]],i={...a,path:o};if(void 0!==a.message)return{...a,path:o,message:a.message};let l="";const s=r.filter(e=>!!e).slice().reverse();for(const e of s)l=e(i,{data:t,defaultError:l}).message;return{...a,path:o,message:l}};function b(e,t){const n=g(),r=v({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===m?void 0:m].filter(e=>!!e)});e.common.issues.push(r)}class y{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if("aborted"===r.status)return w;"dirty"===r.status&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const e of t){const t=await e.key,r=await e.value;n.push({key:t,value:r})}return y.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:t,value:a}=r;if("aborted"===t.status)return w;if("aborted"===a.status)return w;"dirty"===t.status&&e.dirty(),"dirty"===a.status&&e.dirty(),"__proto__"===t.value||void 0===a.value&&!r.alwaysSet||(n[t.value]=a.value)}return{status:e.value,value:n}}}const w=Object.freeze({status:"aborted"}),k=e=>({status:"dirty",value:e}),x=e=>({status:"valid",value:e}),_=e=>"aborted"===e.status,S=e=>"dirty"===e.status,E=e=>"valid"===e.status,C=e=>"undefined"!=typeof Promise&&e instanceof Promise;var N;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(N||(N={}));class z{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const T=(e,t)=>{if(E(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new p(e.common.issues);return this._error=t,this._error}}};function P(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:a}=e;if(t&&(n||r))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:a};return{errorMap:(t,a)=>{const{message:o}=e;return"invalid_enum_value"===t.code?{message:o??a.defaultError}:void 0===a.data?{message:o??r??a.defaultError}:"invalid_type"!==t.code?{message:a.defaultError}:{message:o??n??a.defaultError}},description:a}}class O{get description(){return this._def.description}_getType(e){return d(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:d(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new y,ctx:{common:e.parent.common,data:e.data,parsedType:d(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(C(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){const n={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:d(e)},r=this._parseSync({data:e,path:n.path,parent:n});return T(n,r)}"~validate"(e){const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:d(e)};if(!this["~standard"].async)try{const n=this._parseSync({data:e,path:[],parent:t});return E(n)?{value:n.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>E(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:d(e)},r=this._parse({data:e,path:n.path,parent:n}),a=await(C(r)?r:Promise.resolve(r));return T(n,a)}refine(e,t){const n=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{const a=e(t),o=()=>r.addIssue({code:f.custom,...n(t)});return"undefined"!=typeof Promise&&a instanceof Promise?a.then(e=>!!e||(o(),!1)):!!a||(o(),!1)})}refinement(e,t){return this._refinement((n,r)=>!!e(n)||(r.addIssue("function"==typeof t?t(n,r):t),!1))}_refinement(e){return new Te({schema:this,typeName:Ae.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return Pe.create(this,this._def)}nullable(){return Oe.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return me.create(this)}promise(){return ze.create(this,this._def)}or(e){return ve.create([this,e],this._def)}and(e){return ye.create(this,e,this._def)}transform(e){return new Te({...P(this._def),schema:this,typeName:Ae.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new Re({...P(this._def),innerType:this,defaultValue:t,typeName:Ae.ZodDefault})}brand(){return new Ie({typeName:Ae.ZodBranded,type:this,...P(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new Le({...P(this._def),innerType:this,catchValue:t,typeName:Ae.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return je.create(this,e)}readonly(){return Fe.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const R=/^c[^\s-]{8,}$/i,L=/^[0-9a-z]+$/,M=/^[0-9A-HJKMNP-TV-Z]{26}$/i,I=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,j=/^[a-z0-9_-]{21}$/i,F=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,A=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,D=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let $;const U=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Z=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,V=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,B=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,W=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,H=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Q="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",K=new RegExp(`^${Q}$`);function q(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${e.precision?"+":"?"}`}function G(e){return new RegExp(`^${q(e)}$`)}function Y(e){let t=`${Q}T${q(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function X(e,t){return!("v4"!==t&&t||!U.test(e))||!("v6"!==t&&t||!V.test(e))}function J(e,t){if(!F.test(e))return!1;try{const[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),a=JSON.parse(atob(r));return"object"==typeof a&&null!==a&&((!("typ"in a)||"JWT"===a?.typ)&&(!!a.alg&&(!t||a.alg===t)))}catch{return!1}}function ee(e,t){return!("v4"!==t&&t||!Z.test(e))||!("v6"!==t&&t||!B.test(e))}class te extends O{_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==c.string){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.string,received:t.parsedType}),w}const t=new y;let n;for(const r of this._def.checks)if("min"===r.kind)e.data.length<r.value&&(n=this._getOrReturnCtx(e,n),b(n,{code:f.too_small,minimum:r.value,type:"string",inclusive:!0,exact:!1,message:r.message}),t.dirty());else if("max"===r.kind)e.data.length>r.value&&(n=this._getOrReturnCtx(e,n),b(n,{code:f.too_big,maximum:r.value,type:"string",inclusive:!0,exact:!1,message:r.message}),t.dirty());else if("length"===r.kind){const a=e.data.length>r.value,o=e.data.length<r.value;(a||o)&&(n=this._getOrReturnCtx(e,n),a?b(n,{code:f.too_big,maximum:r.value,type:"string",inclusive:!0,exact:!0,message:r.message}):o&&b(n,{code:f.too_small,minimum:r.value,type:"string",inclusive:!0,exact:!0,message:r.message}),t.dirty())}else if("email"===r.kind)D.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"email",code:f.invalid_string,message:r.message}),t.dirty());else if("emoji"===r.kind)$||($=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),$.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"emoji",code:f.invalid_string,message:r.message}),t.dirty());else if("uuid"===r.kind)I.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"uuid",code:f.invalid_string,message:r.message}),t.dirty());else if("nanoid"===r.kind)j.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"nanoid",code:f.invalid_string,message:r.message}),t.dirty());else if("cuid"===r.kind)R.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"cuid",code:f.invalid_string,message:r.message}),t.dirty());else if("cuid2"===r.kind)L.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"cuid2",code:f.invalid_string,message:r.message}),t.dirty());else if("ulid"===r.kind)M.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"ulid",code:f.invalid_string,message:r.message}),t.dirty());else if("url"===r.kind)try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),b(n,{validation:"url",code:f.invalid_string,message:r.message}),t.dirty()}else if("regex"===r.kind){r.regex.lastIndex=0;r.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"regex",code:f.invalid_string,message:r.message}),t.dirty())}else if("trim"===r.kind)e.data=e.data.trim();else if("includes"===r.kind)e.data.includes(r.value,r.position)||(n=this._getOrReturnCtx(e,n),b(n,{code:f.invalid_string,validation:{includes:r.value,position:r.position},message:r.message}),t.dirty());else if("toLowerCase"===r.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===r.kind)e.data=e.data.toUpperCase();else if("startsWith"===r.kind)e.data.startsWith(r.value)||(n=this._getOrReturnCtx(e,n),b(n,{code:f.invalid_string,validation:{startsWith:r.value},message:r.message}),t.dirty());else if("endsWith"===r.kind)e.data.endsWith(r.value)||(n=this._getOrReturnCtx(e,n),b(n,{code:f.invalid_string,validation:{endsWith:r.value},message:r.message}),t.dirty());else if("datetime"===r.kind){Y(r).test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{code:f.invalid_string,validation:"datetime",message:r.message}),t.dirty())}else if("date"===r.kind){K.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{code:f.invalid_string,validation:"date",message:r.message}),t.dirty())}else if("time"===r.kind){G(r).test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{code:f.invalid_string,validation:"time",message:r.message}),t.dirty())}else"duration"===r.kind?A.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"duration",code:f.invalid_string,message:r.message}),t.dirty()):"ip"===r.kind?X(e.data,r.version)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"ip",code:f.invalid_string,message:r.message}),t.dirty()):"jwt"===r.kind?J(e.data,r.alg)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"jwt",code:f.invalid_string,message:r.message}),t.dirty()):"cidr"===r.kind?ee(e.data,r.version)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"cidr",code:f.invalid_string,message:r.message}),t.dirty()):"base64"===r.kind?W.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"base64",code:f.invalid_string,message:r.message}),t.dirty()):"base64url"===r.kind?H.test(e.data)||(n=this._getOrReturnCtx(e,n),b(n,{validation:"base64url",code:f.invalid_string,message:r.message}),t.dirty()):o.assertNever(r);return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(t=>e.test(t),{validation:t,code:f.invalid_string,...N.errToObj(n)})}_addCheck(e){return new te({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...N.errToObj(e)})}url(e){return this._addCheck({kind:"url",...N.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...N.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...N.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...N.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...N.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...N.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...N.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...N.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...N.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...N.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...N.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...N.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...N.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...N.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...N.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...N.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...N.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...N.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...N.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...N.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...N.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...N.errToObj(t)})}nonempty(e){return this.min(1,N.errToObj(e))}trim(){return new te({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new te({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new te({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function ne(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,a=n>r?n:r;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}te.create=e=>new te({checks:[],typeName:Ae.ZodString,coerce:e?.coerce??!1,...P(e)});class re extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==c.number){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.number,received:t.parsedType}),w}let t;const n=new y;for(const r of this._def.checks)if("int"===r.kind)o.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),b(t,{code:f.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty());else if("min"===r.kind){(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),b(t,{code:f.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty())}else if("max"===r.kind){(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),b(t,{code:f.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty())}else"multipleOf"===r.kind?0!==ne(e.data,r.value)&&(t=this._getOrReturnCtx(e,t),b(t,{code:f.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):"finite"===r.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),b(t,{code:f.not_finite,message:r.message}),n.dirty()):o.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,N.toString(t))}gt(e,t){return this.setLimit("min",e,!1,N.toString(t))}lte(e,t){return this.setLimit("max",e,!0,N.toString(t))}lt(e,t){return this.setLimit("max",e,!1,N.toString(t))}setLimit(e,t,n,r){return new re({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:N.toString(r)}]})}_addCheck(e){return new re({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:N.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:N.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:N.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:N.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:N.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:N.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:N.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:N.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:N.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&o.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if("finite"===n.kind||"int"===n.kind||"multipleOf"===n.kind)return!0;"min"===n.kind?(null===t||n.value>t)&&(t=n.value):"max"===n.kind&&(null===e||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}re.create=e=>new re({checks:[],typeName:Ae.ZodNumber,coerce:e?.coerce||!1,...P(e)});class ae extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==c.bigint)return this._getInvalidInput(e);let t;const n=new y;for(const r of this._def.checks)if("min"===r.kind){(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),b(t,{code:f.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty())}else if("max"===r.kind){(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),b(t,{code:f.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty())}else"multipleOf"===r.kind?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),b(t,{code:f.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):o.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.bigint,received:t.parsedType}),w}gte(e,t){return this.setLimit("min",e,!0,N.toString(t))}gt(e,t){return this.setLimit("min",e,!1,N.toString(t))}lte(e,t){return this.setLimit("max",e,!0,N.toString(t))}lt(e,t){return this.setLimit("max",e,!1,N.toString(t))}setLimit(e,t,n,r){return new ae({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:N.toString(r)}]})}_addCheck(e){return new ae({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:N.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:N.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:N.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:N.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:N.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ae.create=e=>new ae({checks:[],typeName:Ae.ZodBigInt,coerce:e?.coerce??!1,...P(e)});class oe extends O{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==c.boolean){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.boolean,received:t.parsedType}),w}return x(e.data)}}oe.create=e=>new oe({typeName:Ae.ZodBoolean,coerce:e?.coerce||!1,...P(e)});class ie extends O{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==c.date){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.date,received:t.parsedType}),w}if(Number.isNaN(e.data.getTime())){return b(this._getOrReturnCtx(e),{code:f.invalid_date}),w}const t=new y;let n;for(const r of this._def.checks)"min"===r.kind?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),b(n,{code:f.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):"max"===r.kind?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),b(n,{code:f.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):o.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ie({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:N.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:N.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ie.create=e=>new ie({checks:[],coerce:e?.coerce||!1,typeName:Ae.ZodDate,...P(e)});class le extends O{_parse(e){if(this._getType(e)!==c.symbol){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.symbol,received:t.parsedType}),w}return x(e.data)}}le.create=e=>new le({typeName:Ae.ZodSymbol,...P(e)});class se extends O{_parse(e){if(this._getType(e)!==c.undefined){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.undefined,received:t.parsedType}),w}return x(e.data)}}se.create=e=>new se({typeName:Ae.ZodUndefined,...P(e)});class ue extends O{_parse(e){if(this._getType(e)!==c.null){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.null,received:t.parsedType}),w}return x(e.data)}}ue.create=e=>new ue({typeName:Ae.ZodNull,...P(e)});class ce extends O{constructor(){super(...arguments),this._any=!0}_parse(e){return x(e.data)}}ce.create=e=>new ce({typeName:Ae.ZodAny,...P(e)});class de extends O{constructor(){super(...arguments),this._unknown=!0}_parse(e){return x(e.data)}}de.create=e=>new de({typeName:Ae.ZodUnknown,...P(e)});class fe extends O{_parse(e){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.never,received:t.parsedType}),w}}fe.create=e=>new fe({typeName:Ae.ZodNever,...P(e)});class pe extends O{_parse(e){if(this._getType(e)!==c.undefined){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.void,received:t.parsedType}),w}return x(e.data)}}pe.create=e=>new pe({typeName:Ae.ZodVoid,...P(e)});class me extends O{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==c.array)return b(t,{code:f.invalid_type,expected:c.array,received:t.parsedType}),w;if(null!==r.exactLength){const e=t.data.length>r.exactLength.value,a=t.data.length<r.exactLength.value;(e||a)&&(b(t,{code:e?f.too_big:f.too_small,minimum:a?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(b(t,{code:f.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(b(t,{code:f.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((e,n)=>r.type._parseAsync(new z(t,e,t.path,n)))).then(e=>y.mergeArray(n,e));const a=[...t.data].map((e,n)=>r.type._parseSync(new z(t,e,t.path,n)));return y.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new me({...this._def,minLength:{value:e,message:N.toString(t)}})}max(e,t){return new me({...this._def,maxLength:{value:e,message:N.toString(t)}})}length(e,t){return new me({...this._def,exactLength:{value:e,message:N.toString(t)}})}nonempty(e){return this.min(1,e)}}function he(e){if(e instanceof ge){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=Pe.create(he(r))}return new ge({...e._def,shape:()=>t})}return e instanceof me?new me({...e._def,type:he(e.element)}):e instanceof Pe?Pe.create(he(e.unwrap())):e instanceof Oe?Oe.create(he(e.unwrap())):e instanceof we?we.create(e.items.map(e=>he(e))):e}me.create=(e,t)=>new me({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Ae.ZodArray,...P(t)});class ge extends O{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=o.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==c.object){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.object,received:t.parsedType}),w}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),o=[];if(!(this._def.catchall instanceof fe&&"strip"===this._def.unknownKeys))for(const e in n.data)a.includes(e)||o.push(e);const i=[];for(const e of a){const t=r[e],a=n.data[e];i.push({key:{status:"valid",value:e},value:t._parse(new z(n,a,n.path,e)),alwaysSet:e in n.data})}if(this._def.catchall instanceof fe){const e=this._def.unknownKeys;if("passthrough"===e)for(const e of o)i.push({key:{status:"valid",value:e},value:{status:"valid",value:n.data[e]}});else if("strict"===e)o.length>0&&(b(n,{code:f.unrecognized_keys,keys:o}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of o){const r=n.data[t];i.push({key:{status:"valid",value:t},value:e._parse(new z(n,r,n.path,t)),alwaysSet:t in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const e=[];for(const t of i){const n=await t.key,r=await t.value;e.push({key:n,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>y.mergeObjectSync(t,e)):y.mergeObjectSync(t,i)}get shape(){return this._def.shape()}strict(e){return N.errToObj,new ge({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,n)=>{const r=this._def.errorMap?.(t,n).message??n.defaultError;return"unrecognized_keys"===t.code?{message:N.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new ge({...this._def,unknownKeys:"strip"})}passthrough(){return new ge({...this._def,unknownKeys:"passthrough"})}extend(e){return new ge({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new ge({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Ae.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new ge({...this._def,catchall:e})}pick(e){const t={};for(const n of o.objectKeys(e))e[n]&&this.shape[n]&&(t[n]=this.shape[n]);return new ge({...this._def,shape:()=>t})}omit(e){const t={};for(const n of o.objectKeys(this.shape))e[n]||(t[n]=this.shape[n]);return new ge({...this._def,shape:()=>t})}deepPartial(){return he(this)}partial(e){const t={};for(const n of o.objectKeys(this.shape)){const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}return new ge({...this._def,shape:()=>t})}required(e){const t={};for(const n of o.objectKeys(this.shape))if(e&&!e[n])t[n]=this.shape[n];else{let e=this.shape[n];for(;e instanceof Pe;)e=e._def.innerType;t[n]=e}return new ge({...this._def,shape:()=>t})}keyof(){return Ee(o.objectKeys(this.shape))}}ge.create=(e,t)=>new ge({shape:()=>e,unknownKeys:"strip",catchall:fe.create(),typeName:Ae.ZodObject,...P(t)}),ge.strictCreate=(e,t)=>new ge({shape:()=>e,unknownKeys:"strict",catchall:fe.create(),typeName:Ae.ZodObject,...P(t)}),ge.lazycreate=(e,t)=>new ge({shape:e,unknownKeys:"strip",catchall:fe.create(),typeName:Ae.ZodObject,...P(t)});class ve extends O{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async e=>{const n={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:n}),ctx:n}})).then(function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const n of e)if("dirty"===n.result.status)return t.common.issues.push(...n.ctx.common.issues),n.result;const n=e.map(e=>new p(e.ctx.common.issues));return b(t,{code:f.invalid_union,unionErrors:n}),w});{let e;const r=[];for(const a of n){const n={...t,common:{...t.common,issues:[]},parent:null},o=a._parseSync({data:t.data,path:t.path,parent:n});if("valid"===o.status)return o;"dirty"!==o.status||e||(e={result:o,ctx:n}),n.common.issues.length&&r.push(n.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const a=r.map(e=>new p(e));return b(t,{code:f.invalid_union,unionErrors:a}),w}}get options(){return this._def.options}}ve.create=(e,t)=>new ve({options:e,typeName:Ae.ZodUnion,...P(t)});function be(e,t){const n=d(e),r=d(t);if(e===t)return{valid:!0,data:e};if(n===c.object&&r===c.object){const n=o.objectKeys(t),r=o.objectKeys(e).filter(e=>-1!==n.indexOf(e)),a={...e,...t};for(const n of r){const r=be(e[n],t[n]);if(!r.valid)return{valid:!1};a[n]=r.data}return{valid:!0,data:a}}if(n===c.array&&r===c.array){if(e.length!==t.length)return{valid:!1};const n=[];for(let r=0;r<e.length;r++){const a=be(e[r],t[r]);if(!a.valid)return{valid:!1};n.push(a.data)}return{valid:!0,data:n}}return n===c.date&&r===c.date&&+e===+t?{valid:!0,data:e}:{valid:!1}}class ye extends O{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(e,r)=>{if(_(e)||_(r))return w;const a=be(e.value,r.value);return a.valid?((S(e)||S(r))&&t.dirty(),{status:t.value,value:a.data}):(b(n,{code:f.invalid_intersection_types}),w)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([e,t])=>r(e,t)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}ye.create=(e,t,n)=>new ye({left:e,right:t,typeName:Ae.ZodIntersection,...P(n)});class we extends O{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==c.array)return b(n,{code:f.invalid_type,expected:c.array,received:n.parsedType}),w;if(n.data.length<this._def.items.length)return b(n,{code:f.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),w;!this._def.rest&&n.data.length>this._def.items.length&&(b(n,{code:f.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((e,t)=>{const r=this._def.items[t]||this._def.rest;return r?r._parse(new z(n,e,n.path,t)):null}).filter(e=>!!e);return n.common.async?Promise.all(r).then(e=>y.mergeArray(t,e)):y.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new we({...this._def,rest:e})}}we.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new we({items:e,typeName:Ae.ZodTuple,rest:null,...P(t)})};class ke extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==c.map)return b(n,{code:f.invalid_type,expected:c.map,received:n.parsedType}),w;const r=this._def.keyType,a=this._def.valueType,o=[...n.data.entries()].map(([e,t],o)=>({key:r._parse(new z(n,e,n.path,[o,"key"])),value:a._parse(new z(n,t,n.path,[o,"value"]))}));if(n.common.async){const e=new Map;return Promise.resolve().then(async()=>{for(const n of o){const r=await n.key,a=await n.value;if("aborted"===r.status||"aborted"===a.status)return w;"dirty"!==r.status&&"dirty"!==a.status||t.dirty(),e.set(r.value,a.value)}return{status:t.value,value:e}})}{const e=new Map;for(const n of o){const r=n.key,a=n.value;if("aborted"===r.status||"aborted"===a.status)return w;"dirty"!==r.status&&"dirty"!==a.status||t.dirty(),e.set(r.value,a.value)}return{status:t.value,value:e}}}}ke.create=(e,t,n)=>new ke({valueType:t,keyType:e,typeName:Ae.ZodMap,...P(n)});class xe extends O{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==c.set)return b(n,{code:f.invalid_type,expected:c.set,received:n.parsedType}),w;const r=this._def;null!==r.minSize&&n.data.size<r.minSize.value&&(b(n,{code:f.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&n.data.size>r.maxSize.value&&(b(n,{code:f.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function o(e){const n=new Set;for(const r of e){if("aborted"===r.status)return w;"dirty"===r.status&&t.dirty(),n.add(r.value)}return{status:t.value,value:n}}const i=[...n.data.values()].map((e,t)=>a._parse(new z(n,e,n.path,t)));return n.common.async?Promise.all(i).then(e=>o(e)):o(i)}min(e,t){return new xe({...this._def,minSize:{value:e,message:N.toString(t)}})}max(e,t){return new xe({...this._def,maxSize:{value:e,message:N.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}xe.create=(e,t)=>new xe({valueType:e,minSize:null,maxSize:null,typeName:Ae.ZodSet,...P(t)});class _e extends O{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}_e.create=(e,t)=>new _e({getter:e,typeName:Ae.ZodLazy,...P(t)});class Se extends O{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return b(t,{received:t.data,code:f.invalid_literal,expected:this._def.value}),w}return{status:"valid",value:e.data}}get value(){return this._def.value}}function Ee(e,t){return new Ce({values:e,typeName:Ae.ZodEnum,...P(t)})}Se.create=(e,t)=>new Se({value:e,typeName:Ae.ZodLiteral,...P(t)});class Ce extends O{_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),n=this._def.values;return b(t,{expected:o.joinValues(n),received:t.parsedType,code:f.invalid_type}),w}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return b(t,{received:t.data,code:f.invalid_enum_value,options:n}),w}return x(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Ce.create(e,{...this._def,...t})}exclude(e,t=this._def){return Ce.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}Ce.create=Ee;class Ne extends O{_parse(e){const t=o.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==c.string&&n.parsedType!==c.number){const e=o.objectValues(t);return b(n,{expected:o.joinValues(e),received:n.parsedType,code:f.invalid_type}),w}if(this._cache||(this._cache=new Set(o.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const e=o.objectValues(t);return b(n,{received:n.data,code:f.invalid_enum_value,options:e}),w}return x(e.data)}get enum(){return this._def.values}}Ne.create=(e,t)=>new Ne({values:e,typeName:Ae.ZodNativeEnum,...P(t)});class ze extends O{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==c.promise&&!1===t.common.async)return b(t,{code:f.invalid_type,expected:c.promise,received:t.parsedType}),w;const n=t.parsedType===c.promise?t.data:Promise.resolve(t.data);return x(n.then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}ze.create=(e,t)=>new ze({type:e,typeName:Ae.ZodPromise,...P(t)});class Te extends O{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Ae.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:e=>{b(n,e),e.fatal?t.abort():t.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),"preprocess"===r.type){const e=r.transform(n.data,a);if(n.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return w;const r=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return"aborted"===r.status?w:"dirty"===r.status||"dirty"===t.value?k(r.value):r});{if("aborted"===t.value)return w;const r=this._def.schema._parseSync({data:e,path:n.path,parent:n});return"aborted"===r.status?w:"dirty"===r.status||"dirty"===t.value?k(r.value):r}}if("refinement"===r.type){const e=e=>{const t=r.refinement(e,a);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===n.common.async){const r=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===r.status?w:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(n=>"aborted"===n.status?w:("dirty"===n.status&&t.dirty(),e(n.value).then(()=>({status:t.value,value:n.value}))))}if("transform"===r.type){if(!1===n.common.async){const e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!E(e))return w;const o=r.transform(e.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(e=>E(e)?Promise.resolve(r.transform(e.value,a)).then(e=>({status:t.value,value:e})):w)}o.assertNever(r)}}Te.create=(e,t,n)=>new Te({schema:e,typeName:Ae.ZodEffects,effect:t,...P(n)}),Te.createWithPreprocess=(e,t,n)=>new Te({schema:t,effect:{type:"preprocess",transform:e},typeName:Ae.ZodEffects,...P(n)});class Pe extends O{_parse(e){return this._getType(e)===c.undefined?x(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Pe.create=(e,t)=>new Pe({innerType:e,typeName:Ae.ZodOptional,...P(t)});class Oe extends O{_parse(e){return this._getType(e)===c.null?x(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Oe.create=(e,t)=>new Oe({innerType:e,typeName:Ae.ZodNullable,...P(t)});class Re extends O{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===c.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Re.create=(e,t)=>new Re({innerType:e,typeName:Ae.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...P(t)});class Le extends O{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return C(r)?r.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new p(n.common.issues)},input:n.data})})):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new p(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Le.create=(e,t)=>new Le({innerType:e,typeName:Ae.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...P(t)});class Me extends O{_parse(e){if(this._getType(e)!==c.nan){const t=this._getOrReturnCtx(e);return b(t,{code:f.invalid_type,expected:c.nan,received:t.parsedType}),w}return{status:"valid",value:e.data}}}Me.create=e=>new Me({typeName:Ae.ZodNaN,...P(e)});Symbol("zod_brand");class Ie extends O{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class je extends O{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?w:"dirty"===e.status?(t.dirty(),k(e.value)):this._def.out._parseAsync({data:e.value,path:n.path,parent:n})})()}{const e=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?w:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:n.path,parent:n})}}static create(e,t){return new je({in:e,out:t,typeName:Ae.ZodPipeline})}}class Fe extends O{_parse(e){const t=this._def.innerType._parse(e),n=e=>(E(e)&&(e.value=Object.freeze(e.value)),e);return C(t)?t.then(e=>n(e)):n(t)}unwrap(){return this._def.innerType}}Fe.create=(e,t)=>new Fe({innerType:e,typeName:Ae.ZodReadonly,...P(t)});ge.lazycreate;var Ae;!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(Ae||(Ae={}));const De=te.create,$e=re.create,Ue=(Me.create,ae.create,oe.create),Ze=ie.create,Ve=(le.create,se.create,ue.create,ce.create,de.create),Be=(fe.create,pe.create,me.create,ge.create),We=(ge.strictCreate,ve.create),He=(ye.create,we.create,ke.create,xe.create,_e.create,Se.create,Ce.create),Qe=(Ne.create,ze.create,Te.create,Pe.create,Oe.create,Te.createWithPreprocess,je.create,Be({type:He(["bug","feature"]),title:De().min(1).max(200),description:De().min(10).max(5e3),email:De().email().optional(),timestamp:Ze().optional(),url:De().url().optional(),currentUrl:De().optional(),screenshot:De().optional()})),Ke=(He(["checking","available","current","error"]),Be({currentVersion:De(),latestVersion:De(),updateUrl:De().url(),releaseNotes:De().optional(),checkDate:Ze()}),Be({updateCheckEnabled:Ue().default(!0),updateCheckInterval:$e().int().min(1).max(1440).default(10),updateUrl:De().url().optional(),lastUpdateCheck:Ze().optional(),notifications:Be({showUpdateNotifications:Ue().default(!0),showSubmitConfirmations:Ue().default(!0)})}),Be({type:He(["SUBMIT_REPORT","CHECK_UPDATE","UPDATE_SETTINGS","GET_SETTINGS"]),data:Ve().optional(),tabId:$e().int().optional(),timestamp:We([Ze(),De()]).optional()}));Qe.extend({id:De().uuid(),status:He(["pending","sent","failed"]),attempts:$e().int().min(0).default(0),lastAttempt:Ze().optional()});function qe(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function Ge(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function Ye(...e){return t=>{let n=!1;const r=e.map(e=>{const r=Ge(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){const n=r[t];"function"==typeof n?n():Ge(e[t],null)}}}}function Xe(...e){return l.useCallback(Ye(...e),e)}var Je=a(848);function et(...e){const t=e[0];if(1===e.length)return t;const n=()=>{const n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){const r=n.reduce((t,{useScope:n,scopeName:r})=>({...t,...n(e)[`__scope${r}`]}),{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}var tt=globalThis?.document?l.useLayoutEffect:()=>{},nt=s[" useInsertionEffect ".trim().toString()]||tt;function rt({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[a,o,i]=function({defaultProp:e,onChange:t}){const[n,r]=l.useState(e),a=l.useRef(n),o=l.useRef(t);return nt(()=>{o.current=t},[t]),l.useEffect(()=>{a.current!==n&&(o.current?.(n),a.current=n)},[n,a]),[n,r,o]}({defaultProp:t,onChange:n}),s=void 0!==e,u=s?e:a;{const t=l.useRef(void 0!==e);l.useEffect(()=>{const e=t.current;if(e!==s){}t.current=s},[s,r])}const c=l.useCallback(t=>{if(s){const n=function(e){return"function"==typeof e}(t)?t(e):t;n!==e&&i.current?.(n)}else o(t)},[s,e,o,i]);return[u,c]}Symbol("RADIX:SYNC_STATE");a(961);function at(e){const t=ot(e),n=l.forwardRef((e,n)=>{const{children:r,...a}=e,o=l.Children.toArray(r),i=o.find(lt);if(i){const e=i.props.children,r=o.map(t=>t===i?l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null:t);return(0,Je.jsx)(t,{...a,ref:n,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,Je.jsx)(t,{...a,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}function ot(e){const t=l.forwardRef((e,t)=>{const{children:n,...r}=e;if(l.isValidElement(n)){const e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(n),a=function(e,t){const n={...t};for(const r in t){const a=e[r],o=t[r];/^on[A-Z]/.test(r)?a&&o?n[r]=(...e)=>{const t=o(...e);return a(...e),t}:a&&(n[r]=a):"style"===r?n[r]={...a,...o}:"className"===r&&(n[r]=[a,o].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==l.Fragment&&(a.ref=t?Ye(t,e):e),l.cloneElement(n,a)}return l.Children.count(n)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var it=Symbol("radix.slottable");function lt(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===it}var st=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{const n=at(`Primitive.${t}`),r=l.forwardRef((e,r)=>{const{asChild:a,...o}=e,i=a?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,Je.jsx)(i,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});var ut="Switch",[ct,dt]=function(e,t=[]){let n=[];const r=()=>{const t=n.map(e=>l.createContext(e));return function(n){const r=n?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){const a=l.createContext(r),o=n.length;n=[...n,r];const i=t=>{const{scope:n,children:r,...i}=t,s=n?.[e]?.[o]||a,u=l.useMemo(()=>i,Object.values(i));return(0,Je.jsx)(s.Provider,{value:u,children:r})};return i.displayName=t+"Provider",[i,function(n,i){const s=i?.[e]?.[o]||a,u=l.useContext(s);if(u)return u;if(void 0!==r)return r;throw new Error(`\`${n}\` must be used within \`${t}\``)}]},et(r,...t)]}(ut),[ft,pt]=ct(ut),mt=l.forwardRef((e,t)=>{const{__scopeSwitch:n,name:r,checked:a,defaultChecked:o,required:i,disabled:s,value:u="on",onCheckedChange:c,form:d,...f}=e,[p,m]=l.useState(null),h=Xe(t,e=>m(e)),g=l.useRef(!1),v=!p||(d||!!p.closest("form")),[b,y]=rt({prop:a,defaultProp:o??!1,onChange:c,caller:ut});return(0,Je.jsxs)(ft,{scope:n,checked:b,disabled:s,children:[(0,Je.jsx)(st.button,{type:"button",role:"switch","aria-checked":b,"aria-required":i,"data-state":bt(b),"data-disabled":s?"":void 0,disabled:s,value:u,...f,ref:h,onClick:qe(e.onClick,e=>{y(e=>!e),v&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),v&&(0,Je.jsx)(vt,{control:p,bubbles:!g.current,name:r,value:u,checked:b,required:i,disabled:s,form:d,style:{transform:"translateX(-100%)"}})]})});mt.displayName=ut;var ht="SwitchThumb",gt=l.forwardRef((e,t)=>{const{__scopeSwitch:n,...r}=e,a=pt(ht,n);return(0,Je.jsx)(st.span,{"data-state":bt(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:t})});gt.displayName=ht;var vt=l.forwardRef(({__scopeSwitch:e,control:t,checked:n,bubbles:r=!0,...a},o)=>{const i=l.useRef(null),s=Xe(i,o),u=function(e){const t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(n),c=function(e){const[t,n]=l.useState(void 0);return tt(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver(t=>{if(!Array.isArray(t))return;if(!t.length)return;const r=t[0];let a,o;if("borderBoxSize"in r){const e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;a=t.inlineSize,o=t.blockSize}else a=e.offsetWidth,o=e.offsetHeight;n({width:a,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(t);return l.useEffect(()=>{const e=i.current;if(!e)return;const t=window.HTMLInputElement.prototype,a=Object.getOwnPropertyDescriptor(t,"checked").set;if(u!==n&&a){const t=new Event("click",{bubbles:r});a.call(e,n),e.dispatchEvent(t)}},[u,n,r]),(0,Je.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...a,tabIndex:-1,ref:s,style:{...a.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function bt(e){return e?"checked":"unchecked"}vt.displayName="SwitchBubbleInput";var yt=mt,wt=gt;function kt(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=kt(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function xt(){for(var e,t,n=0,r="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=kt(e))&&(r&&(r+=" "),r+=t);return r}const _t=e=>{const t=Nt(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{const n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),St(n,t)||Ct(e)},getConflictingClassGroupIds:(e,t)=>{const a=n[e]||[];return t&&r[e]?[...a,...r[e]]:a}}},St=(e,t)=>{if(0===e.length)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),a=r?St(e.slice(1),r):void 0;if(a)return a;if(0===t.validators.length)return;const o=e.join("-");return t.validators.find(({validator:e})=>e(o))?.classGroupId},Et=/^\[(.+)\]$/,Ct=e=>{if(Et.test(e)){const t=Et.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Nt=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const e in n)zt(n[e],r,e,t);return r},zt=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){return void((""===e?t:Tt(t,e)).classGroupId=n)}if("function"==typeof e)return Pt(e)?void zt(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,a])=>{zt(a,Tt(t,e),n,r)})})},Tt=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},Pt=e=>e.isThemeGetter,Ot=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const a=(a,o)=>{n.set(a,o),t++,t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(a(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):a(e,t)}}},Rt=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=e=>{const t=[];let n,r=0,a=0,o=0;for(let i=0;i<e.length;i++){let l=e[i];if(0===r&&0===a){if(":"===l){t.push(e.slice(o,i)),o=i+1;continue}if("/"===l){n=i;continue}}"["===l?r++:"]"===l?r--:"("===l?a++:")"===l&&a--}const i=0===t.length?e:e.substring(o),l=Lt(i);return{modifiers:t,hasImportantModifier:l!==i,baseClassName:l,maybePostfixModifierPosition:n&&n>o?n-o:void 0}};if(t){const e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){const e=r;r=t=>n({className:t,parseClassName:e})}return r},Lt=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,Mt=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;const n=[];let r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},It=/\s+/;function jt(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=Ft(e))&&(r&&(r+=" "),r+=t);return r}const Ft=e=>{if("string"==typeof e)return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Ft(e[r]))&&(n&&(n+=" "),n+=t);return n};function At(e,...t){let n,r,a,o=function(l){const s=t.reduce((e,t)=>t(e),e());return n=(e=>({cache:Ot(e.cacheSize),parseClassName:Rt(e),sortModifiers:Mt(e),..._t(e)}))(s),r=n.cache.get,a=n.cache.set,o=i,i(l)};function i(e){const t=r(e);if(t)return t;const o=((e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:a,sortModifiers:o}=t,i=[],l=e.trim().split(It);let s="";for(let e=l.length-1;e>=0;e-=1){const t=l[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=n(t);if(u){s=t+(s.length>0?" "+s:s);continue}let m=!!p,h=r(m?f.substring(0,p):f);if(!h){if(!m){s=t+(s.length>0?" "+s:s);continue}if(h=r(f),!h){s=t+(s.length>0?" "+s:s);continue}m=!1}const g=o(c).join(":"),v=d?g+"!":g,b=v+h;if(i.includes(b))continue;i.push(b);const y=a(h,m);for(let e=0;e<y.length;++e){const t=y[e];i.push(v+t)}s=t+(s.length>0?" "+s:s)}return s})(e,n);return a(e,o),o}return function(){return o(jt.apply(null,arguments))}}const Dt=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},$t=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ut=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Zt=/^\d+\/\d+$/,Vt=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Bt=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Wt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Ht=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Qt=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Kt=e=>Zt.test(e),qt=e=>!!e&&!Number.isNaN(Number(e)),Gt=e=>!!e&&Number.isInteger(Number(e)),Yt=e=>e.endsWith("%")&&qt(e.slice(0,-1)),Xt=e=>Vt.test(e),Jt=()=>!0,en=e=>Bt.test(e)&&!Wt.test(e),tn=()=>!1,nn=e=>Ht.test(e),rn=e=>Qt.test(e),an=e=>!ln(e)&&!pn(e),on=e=>wn(e,Sn,tn),ln=e=>$t.test(e),sn=e=>wn(e,En,en),un=e=>wn(e,Cn,qt),cn=e=>wn(e,xn,tn),dn=e=>wn(e,_n,rn),fn=e=>wn(e,zn,nn),pn=e=>Ut.test(e),mn=e=>kn(e,En),hn=e=>kn(e,Nn),gn=e=>kn(e,xn),vn=e=>kn(e,Sn),bn=e=>kn(e,_n),yn=e=>kn(e,zn,!0),wn=(e,t,n)=>{const r=$t.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},kn=(e,t,n=!1)=>{const r=Ut.exec(e);return!!r&&(r[1]?t(r[1]):n)},xn=e=>"position"===e||"percentage"===e,_n=e=>"image"===e||"url"===e,Sn=e=>"length"===e||"size"===e||"bg-size"===e,En=e=>"length"===e,Cn=e=>"number"===e,Nn=e=>"family-name"===e,zn=e=>"shadow"===e,Tn=(Symbol.toStringTag,()=>{const e=Dt("color"),t=Dt("font"),n=Dt("text"),r=Dt("font-weight"),a=Dt("tracking"),o=Dt("leading"),i=Dt("breakpoint"),l=Dt("container"),s=Dt("spacing"),u=Dt("radius"),c=Dt("shadow"),d=Dt("inset-shadow"),f=Dt("text-shadow"),p=Dt("drop-shadow"),m=Dt("blur"),h=Dt("perspective"),g=Dt("aspect"),v=Dt("ease"),b=Dt("animate"),y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",pn,ln],w=()=>[pn,ln,s],k=()=>[Kt,"full","auto",...w()],x=()=>[Gt,"none","subgrid",pn,ln],_=()=>["auto",{span:["full",Gt,pn,ln]},Gt,pn,ln],S=()=>[Gt,"auto",pn,ln],E=()=>["auto","min","max","fr",pn,ln],C=()=>["auto",...w()],N=()=>[Kt,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...w()],z=()=>[e,pn,ln],T=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",gn,cn,{position:[pn,ln]}],P=()=>["auto","cover","contain",vn,on,{size:[pn,ln]}],O=()=>[Yt,mn,sn],R=()=>["","none","full",u,pn,ln],L=()=>["",qt,mn,sn],M=()=>[qt,Yt,gn,cn],I=()=>["","none",m,pn,ln],j=()=>["none",qt,pn,ln],F=()=>["none",qt,pn,ln],A=()=>[qt,pn,ln],D=()=>[Kt,"full",...w()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Xt],breakpoint:[Xt],color:[Jt],container:[Xt],"drop-shadow":[Xt],ease:["in","out","in-out"],font:[an],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Xt],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Xt],shadow:[Xt],spacing:["px",qt],text:[Xt],"text-shadow":[Xt],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Kt,ln,pn,g]}],container:["container"],columns:[{columns:[qt,ln,pn,l]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:y()}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:k()}],"inset-x":[{"inset-x":k()}],"inset-y":[{"inset-y":k()}],start:[{start:k()}],end:[{end:k()}],top:[{top:k()}],right:[{right:k()}],bottom:[{bottom:k()}],left:[{left:k()}],visibility:["visible","invisible","collapse"],z:[{z:[Gt,"auto",pn,ln]}],basis:[{basis:[Kt,"full","auto",l,...w()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[qt,Kt,"auto","initial","none",ln]}],grow:[{grow:["",qt,pn,ln]}],shrink:[{shrink:["",qt,pn,ln]}],order:[{order:[Gt,"first","last","none",pn,ln]}],"grid-cols":[{"grid-cols":x()}],"col-start-end":[{col:_()}],"col-start":[{"col-start":S()}],"col-end":[{"col-end":S()}],"grid-rows":[{"grid-rows":x()}],"row-start-end":[{row:_()}],"row-start":[{"row-start":S()}],"row-end":[{"row-end":S()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":E()}],"auto-rows":[{"auto-rows":E()}],gap:[{gap:w()}],"gap-x":[{"gap-x":w()}],"gap-y":[{"gap-y":w()}],"justify-content":[{justify:["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe","normal"]}],"justify-items":[{"justify-items":["start","end","center","stretch","center-safe","end-safe","normal"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"align-items":[{items:["start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"align-self":[{self:["auto","start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"place-items":[{"place-items":["start","end","center","stretch","center-safe","end-safe","baseline"]}],"place-self":[{"place-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],p:[{p:w()}],px:[{px:w()}],py:[{py:w()}],ps:[{ps:w()}],pe:[{pe:w()}],pt:[{pt:w()}],pr:[{pr:w()}],pb:[{pb:w()}],pl:[{pl:w()}],m:[{m:C()}],mx:[{mx:C()}],my:[{my:C()}],ms:[{ms:C()}],me:[{me:C()}],mt:[{mt:C()}],mr:[{mr:C()}],mb:[{mb:C()}],ml:[{ml:C()}],"space-x":[{"space-x":w()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":w()}],"space-y-reverse":["space-y-reverse"],size:[{size:N()}],w:[{w:[l,"screen",...N()]}],"min-w":[{"min-w":[l,"screen","none",...N()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[i]},...N()]}],h:[{h:["screen","lh",...N()]}],"min-h":[{"min-h":["screen","lh","none",...N()]}],"max-h":[{"max-h":["screen","lh",...N()]}],"font-size":[{text:["base",n,mn,sn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,pn,un]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Yt,ln]}],"font-family":[{font:[hn,ln,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,pn,ln]}],"line-clamp":[{"line-clamp":[qt,"none",pn,un]}],leading:[{leading:[o,...w()]}],"list-image":[{"list-image":["none",pn,ln]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",pn,ln]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:z()}],"text-color":[{text:z()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","wavy"]}],"text-decoration-thickness":[{decoration:[qt,"from-font","auto",pn,sn]}],"text-decoration-color":[{decoration:z()}],"underline-offset":[{"underline-offset":[qt,"auto",pn,ln]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:w()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",pn,ln]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",pn,ln]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:T()}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:P()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Gt,pn,ln],radial:["",pn,ln],conic:[Gt,pn,ln]},bn,dn]}],"bg-color":[{bg:z()}],"gradient-from-pos":[{from:O()}],"gradient-via-pos":[{via:O()}],"gradient-to-pos":[{to:O()}],"gradient-from":[{from:z()}],"gradient-via":[{via:z()}],"gradient-to":[{to:z()}],rounded:[{rounded:R()}],"rounded-s":[{"rounded-s":R()}],"rounded-e":[{"rounded-e":R()}],"rounded-t":[{"rounded-t":R()}],"rounded-r":[{"rounded-r":R()}],"rounded-b":[{"rounded-b":R()}],"rounded-l":[{"rounded-l":R()}],"rounded-ss":[{"rounded-ss":R()}],"rounded-se":[{"rounded-se":R()}],"rounded-ee":[{"rounded-ee":R()}],"rounded-es":[{"rounded-es":R()}],"rounded-tl":[{"rounded-tl":R()}],"rounded-tr":[{"rounded-tr":R()}],"rounded-br":[{"rounded-br":R()}],"rounded-bl":[{"rounded-bl":R()}],"border-w":[{border:L()}],"border-w-x":[{"border-x":L()}],"border-w-y":[{"border-y":L()}],"border-w-s":[{"border-s":L()}],"border-w-e":[{"border-e":L()}],"border-w-t":[{"border-t":L()}],"border-w-r":[{"border-r":L()}],"border-w-b":[{"border-b":L()}],"border-w-l":[{"border-l":L()}],"divide-x":[{"divide-x":L()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":L()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:["solid","dashed","dotted","double","hidden","none"]}],"divide-style":[{divide:["solid","dashed","dotted","double","hidden","none"]}],"border-color":[{border:z()}],"border-color-x":[{"border-x":z()}],"border-color-y":[{"border-y":z()}],"border-color-s":[{"border-s":z()}],"border-color-e":[{"border-e":z()}],"border-color-t":[{"border-t":z()}],"border-color-r":[{"border-r":z()}],"border-color-b":[{"border-b":z()}],"border-color-l":[{"border-l":z()}],"divide-color":[{divide:z()}],"outline-style":[{outline:["solid","dashed","dotted","double","none","hidden"]}],"outline-offset":[{"outline-offset":[qt,pn,ln]}],"outline-w":[{outline:["",qt,mn,sn]}],"outline-color":[{outline:z()}],shadow:[{shadow:["","none",c,yn,fn]}],"shadow-color":[{shadow:z()}],"inset-shadow":[{"inset-shadow":["none",d,yn,fn]}],"inset-shadow-color":[{"inset-shadow":z()}],"ring-w":[{ring:L()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:z()}],"ring-offset-w":[{"ring-offset":[qt,sn]}],"ring-offset-color":[{"ring-offset":z()}],"inset-ring-w":[{"inset-ring":L()}],"inset-ring-color":[{"inset-ring":z()}],"text-shadow":[{"text-shadow":["none",f,yn,fn]}],"text-shadow-color":[{"text-shadow":z()}],opacity:[{opacity:[qt,pn,ln]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[qt]}],"mask-image-linear-from-pos":[{"mask-linear-from":M()}],"mask-image-linear-to-pos":[{"mask-linear-to":M()}],"mask-image-linear-from-color":[{"mask-linear-from":z()}],"mask-image-linear-to-color":[{"mask-linear-to":z()}],"mask-image-t-from-pos":[{"mask-t-from":M()}],"mask-image-t-to-pos":[{"mask-t-to":M()}],"mask-image-t-from-color":[{"mask-t-from":z()}],"mask-image-t-to-color":[{"mask-t-to":z()}],"mask-image-r-from-pos":[{"mask-r-from":M()}],"mask-image-r-to-pos":[{"mask-r-to":M()}],"mask-image-r-from-color":[{"mask-r-from":z()}],"mask-image-r-to-color":[{"mask-r-to":z()}],"mask-image-b-from-pos":[{"mask-b-from":M()}],"mask-image-b-to-pos":[{"mask-b-to":M()}],"mask-image-b-from-color":[{"mask-b-from":z()}],"mask-image-b-to-color":[{"mask-b-to":z()}],"mask-image-l-from-pos":[{"mask-l-from":M()}],"mask-image-l-to-pos":[{"mask-l-to":M()}],"mask-image-l-from-color":[{"mask-l-from":z()}],"mask-image-l-to-color":[{"mask-l-to":z()}],"mask-image-x-from-pos":[{"mask-x-from":M()}],"mask-image-x-to-pos":[{"mask-x-to":M()}],"mask-image-x-from-color":[{"mask-x-from":z()}],"mask-image-x-to-color":[{"mask-x-to":z()}],"mask-image-y-from-pos":[{"mask-y-from":M()}],"mask-image-y-to-pos":[{"mask-y-to":M()}],"mask-image-y-from-color":[{"mask-y-from":z()}],"mask-image-y-to-color":[{"mask-y-to":z()}],"mask-image-radial":[{"mask-radial":[pn,ln]}],"mask-image-radial-from-pos":[{"mask-radial-from":M()}],"mask-image-radial-to-pos":[{"mask-radial-to":M()}],"mask-image-radial-from-color":[{"mask-radial-from":z()}],"mask-image-radial-to-color":[{"mask-radial-to":z()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"]}],"mask-image-conic-pos":[{"mask-conic":[qt]}],"mask-image-conic-from-pos":[{"mask-conic-from":M()}],"mask-image-conic-to-pos":[{"mask-conic-to":M()}],"mask-image-conic-from-color":[{"mask-conic-from":z()}],"mask-image-conic-to-color":[{"mask-conic-to":z()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:T()}],"mask-repeat":[{mask:["no-repeat",{repeat:["","x","y","space","round"]}]}],"mask-size":[{mask:P()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",pn,ln]}],filter:[{filter:["","none",pn,ln]}],blur:[{blur:I()}],brightness:[{brightness:[qt,pn,ln]}],contrast:[{contrast:[qt,pn,ln]}],"drop-shadow":[{"drop-shadow":["","none",p,yn,fn]}],"drop-shadow-color":[{"drop-shadow":z()}],grayscale:[{grayscale:["",qt,pn,ln]}],"hue-rotate":[{"hue-rotate":[qt,pn,ln]}],invert:[{invert:["",qt,pn,ln]}],saturate:[{saturate:[qt,pn,ln]}],sepia:[{sepia:["",qt,pn,ln]}],"backdrop-filter":[{"backdrop-filter":["","none",pn,ln]}],"backdrop-blur":[{"backdrop-blur":I()}],"backdrop-brightness":[{"backdrop-brightness":[qt,pn,ln]}],"backdrop-contrast":[{"backdrop-contrast":[qt,pn,ln]}],"backdrop-grayscale":[{"backdrop-grayscale":["",qt,pn,ln]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[qt,pn,ln]}],"backdrop-invert":[{"backdrop-invert":["",qt,pn,ln]}],"backdrop-opacity":[{"backdrop-opacity":[qt,pn,ln]}],"backdrop-saturate":[{"backdrop-saturate":[qt,pn,ln]}],"backdrop-sepia":[{"backdrop-sepia":["",qt,pn,ln]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":w()}],"border-spacing-x":[{"border-spacing-x":w()}],"border-spacing-y":[{"border-spacing-y":w()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",pn,ln]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[qt,"initial",pn,ln]}],ease:[{ease:["linear","initial",v,pn,ln]}],delay:[{delay:[qt,pn,ln]}],animate:[{animate:["none",b,pn,ln]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,pn,ln]}],"perspective-origin":[{"perspective-origin":y()}],rotate:[{rotate:j()}],"rotate-x":[{"rotate-x":j()}],"rotate-y":[{"rotate-y":j()}],"rotate-z":[{"rotate-z":j()}],scale:[{scale:F()}],"scale-x":[{"scale-x":F()}],"scale-y":[{"scale-y":F()}],"scale-z":[{"scale-z":F()}],"scale-3d":["scale-3d"],skew:[{skew:A()}],"skew-x":[{"skew-x":A()}],"skew-y":[{"skew-y":A()}],transform:[{transform:[pn,ln,"","none","gpu","cpu"]}],"transform-origin":[{origin:y()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:D()}],"translate-x":[{"translate-x":D()}],"translate-y":[{"translate-y":D()}],"translate-z":[{"translate-z":D()}],"translate-none":["translate-none"],accent:[{accent:z()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:z()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",pn,ln]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":w()}],"scroll-mx":[{"scroll-mx":w()}],"scroll-my":[{"scroll-my":w()}],"scroll-ms":[{"scroll-ms":w()}],"scroll-me":[{"scroll-me":w()}],"scroll-mt":[{"scroll-mt":w()}],"scroll-mr":[{"scroll-mr":w()}],"scroll-mb":[{"scroll-mb":w()}],"scroll-ml":[{"scroll-ml":w()}],"scroll-p":[{"scroll-p":w()}],"scroll-px":[{"scroll-px":w()}],"scroll-py":[{"scroll-py":w()}],"scroll-ps":[{"scroll-ps":w()}],"scroll-pe":[{"scroll-pe":w()}],"scroll-pt":[{"scroll-pt":w()}],"scroll-pr":[{"scroll-pr":w()}],"scroll-pb":[{"scroll-pb":w()}],"scroll-pl":[{"scroll-pl":w()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",pn,ln]}],fill:[{fill:["none",...z()]}],"stroke-w":[{stroke:[qt,mn,sn,un]}],stroke:[{stroke:["none",...z()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}}),Pn=At(Tn);function On(...e){return Pn(xt(e))}const Rn=l.forwardRef(({className:e,...t},n)=>l.createElement(yt,{className:On("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:n},l.createElement(wt,{className:On("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})));Rn.displayName=yt.displayName;const Ln=()=>{const[e,t]=(0,l.useState)("bug"),[n,r]=(0,l.useState)(""),[a,o]=(0,l.useState)(""),[i,s]=(0,l.useState)(!1),[u,c]=(0,l.useState)(null);return l.createElement("div",{className:"max-w-2xl mx-auto px-4"},l.createElement("form",{onSubmit:async t=>{if(t.preventDefault(),n.trim()){s(!0),c(null);try{const t={type:e,title:`${"bug"===e?"Bug Report":"Feature Request"} - ${(new Date).toLocaleDateString()}`,description:n.trim(),email:a.trim()||void 0,url:window.location.href},i=Ke.parse({type:"SUBMIT_REPORT",data:t});await chrome.runtime.sendMessage(i),c({type:"success",message:("bug"===e?"Bug report":"Feature request")+" submitted successfully!"}),r(""),o("")}catch(e){c({type:"error",message:"Failed to submit report. Please try again."})}finally{s(!1)}}else c({type:"error",message:"Please fill in the description."})},className:"space-y-5"},l.createElement("div",null,l.createElement("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground mb-1"},"Email"),l.createElement("input",{type:"email",id:"email",value:a,onChange:e=>o(e.target.value),placeholder:"<EMAIL>",className:"w-full px-3 py-2 border border-input bg-input text-foreground rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent placeholder:text-muted-foreground",disabled:i})),l.createElement("div",null,l.createElement("label",{htmlFor:"description",className:"block text-sm font-medium text-foreground mb-1"},"bug"===e?"What bug do you want to report?":"What feature you want to build?"),l.createElement("textarea",{id:"description",value:n,onChange:e=>r(e.target.value),rows:6,placeholder:""+("bug"===e?"Describe the bug and steps to reproduce it...":"Describe the feature and why it would be useful..."),className:"w-full px-3 py-2 border border-input bg-input text-foreground rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-vertical placeholder:text-muted-foreground",disabled:i})),l.createElement("div",{className:"space-y-3"},l.createElement("div",{className:"flex items-center justify-between px-2"},l.createElement("label",{htmlFor:"report-type",className:"text-sm font-medium text-foreground"},"Report Type"),l.createElement("div",{className:"flex items-center space-x-2"},l.createElement("span",{className:On("text-sm font-medium","bug"===e?"text-foreground font-semibold":"text-muted-foreground")},"Bug"),l.createElement(Rn,{id:"report-type",checked:"feature"===e,onCheckedChange:e=>t(e?"feature":"bug"),className:On("feature"===e&&"data-[state=checked]:bg-green-600")}),l.createElement("span",{className:On("text-sm font-medium","feature"===e?"text-foreground font-semibold":"text-muted-foreground")},"Feature"))),l.createElement("button",{type:"submit",disabled:i,className:`w-full py-2 px-4 rounded-md font-medium text-white transition-colors ${"bug"===e?"bg-red-600 hover:bg-red-700 disabled:bg-red-600/50":"bg-green-600 hover:bg-green-700 disabled:bg-green-600/50"} disabled:cursor-not-allowed`},i?"Submitting...":"Submit "+("bug"===e?"Bug Report":"Feature Request"))),u&&l.createElement("div",{className:"p-3 rounded-md text-sm "+("success"===u.type?"bg-green-600/20 text-green-400 border border-green-600/30":"bg-red-600/20 text-red-400 border border-red-600/30")},u.message)))},Mn=l.forwardRef(({className:e,...t},n)=>l.createElement("div",{ref:n,className:`rounded-lg border bg-card text-card-foreground shadow-sm ${e||""}`,...t}));Mn.displayName="Card";const In=l.forwardRef(({className:e,...t},n)=>l.createElement("div",{ref:n,className:`flex flex-col space-y-1.5 p-6 ${e||""}`,...t}));In.displayName="CardHeader";const jn=l.forwardRef(({className:e,...t},n)=>l.createElement("h3",{ref:n,className:`text-2xl font-semibold leading-none tracking-tight ${e||""}`,...t}));jn.displayName="CardTitle";const Fn=l.forwardRef(({className:e,...t},n)=>l.createElement("p",{ref:n,className:`text-sm text-muted-foreground ${e||""}`,...t}));Fn.displayName="CardDescription";const An=l.forwardRef(({className:e,...t},n)=>l.createElement("div",{ref:n,className:`p-6 pt-0 ${e||""}`,...t}));An.displayName="CardContent";l.forwardRef(({className:e,...t},n)=>l.createElement("div",{ref:n,className:`flex items-center p-6 pt-0 ${e||""}`,...t})).displayName="CardFooter";const Dn=l.forwardRef(({className:e,variant:t="default",size:n="default",...r},a)=>l.createElement("button",{className:`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[t]} ${{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[n]} ${e||""}`,ref:a,...r}));function $n({className:e,variant:t="default",...n}){return l.createElement("div",{className:`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${{default:"bg-primary text-primary-foreground hover:bg-primary/80",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground border-border",success:"bg-green-600/20 text-green-400 border-green-600/30"}[t]} ${e||""}`,...n})}Dn.displayName="Button";const Un=()=>{const[e,t]=(0,l.useState)("current"),[n,r]=(0,l.useState)(!1),[a,o]=(0,l.useState)(""),[i,s]=(0,l.useState)(""),[u,c]=(0,l.useState)([]),[d,f]=(0,l.useState)(""),[p,m]=(0,l.useState)(""),h=chrome.runtime.getManifest();h.browser_version||h.version;(0,l.useEffect)(()=>{g();const e=chrome.runtime.getManifest().browser_version||"";m(e)},[]);const g=async()=>{try{const e=await chrome.storage.local.get(["updateAvailable","latestVersion","downloadUrl","releaseNotes","lastUpdateCheck"]);if(e.updateAvailable?(t("available"),o(e.latestVersion||""),s(e.downloadUrl||""),c(e.releaseNotes||[]),chrome.action.setBadgeText({text:""})):t("current"),e.lastUpdateCheck){const t=new Date(e.lastUpdateCheck),n=(new Date).getTime()-t.getTime(),r=Math.floor(n/6e4),a=Math.floor(r/60),o=Math.floor(a/24);f(o>0?`${o} day${o>1?"s":""} ago`:a>0?`${a} hour${a>1?"s":""} ago`:r>0?`${r} minute${r>1?"s":""} ago`:"Just now")}else f("Never")}catch(e){}},v=async()=>{r(!0);try{await chrome.runtime.sendMessage({type:"CHECK_UPDATE",data:{updateUrl:""}}),setTimeout(async()=>{await g(),r(!1)},2e3)}catch(e){r(!1)}};return"available"===e&&a?l.createElement("div",{className:"w-full max-w-md mx-auto p-4"},l.createElement(Mn,null,l.createElement(In,null,l.createElement("div",{className:"flex items-center justify-between"},l.createElement(jn,{className:"text-xl"},"Update Available! 🎉"),l.createElement($n,{variant:"default"},"New")),l.createElement(Fn,null,"Version ",a," is now available (you have ",p||"unknown",")")),l.createElement(An,{className:"space-y-4"},l.createElement("div",{className:"space-y-3"},i?l.createElement(Dn,{onClick:()=>window.open(i,"_blank"),className:"w-full"},"Download Update"):l.createElement("div",{className:"text-sm text-muted-foreground text-center p-3 bg-muted/50 rounded-md"},"Auto-update will be triggered soon, or go to browser settings → About to trigger update manually."),l.createElement(Dn,{variant:"outline",onClick:v,disabled:n,className:"w-full"},n?"Checking...":"Check Again"))))):l.createElement("div",{className:"w-full max-w-md mx-auto p-4"},l.createElement(Mn,null,l.createElement(In,null,l.createElement("div",{className:"flex items-center justify-between"},l.createElement(jn,{className:"text-xl"},"Update Status"),l.createElement($n,{variant:"success",className:"bg-green-500"},l.createElement("span",{className:"w-2 h-2 bg-white rounded-full mr-1.5 animate-pulse"}),"Up to date")),l.createElement(Fn,null,p?`Current version: ${p}`:"Version information not available")),l.createElement(An,{className:"space-y-4"},l.createElement("div",{className:"text-sm text-muted-foreground"},"Last checked: ",d),l.createElement(Dn,{onClick:v,disabled:n||!p,className:"w-full",variant:n?"secondary":"default"},p?n?"Checking for updates...":"Check for Updates":"Update check unavailable"))))},Zn=()=>{const e=[{title:"GitHub Repository",description:"View source code and contribute",url:"https://github.com/browseros-ai/BrowserOS/",icon:l.createElement("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24"},l.createElement("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"}))},{title:"Download Releases",description:"Get the latest version of BrowserOS",url:"https://github.com/browseros-ai/BrowserOS/releases/",icon:l.createElement("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"}))},{title:"Discord Community",description:"Join our Discord server for support",url:"https://discord.gg/your-discord-invite",icon:l.createElement("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24"},l.createElement("path",{d:"M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.**************.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z"}))},{title:"Help & Documentation",description:"Installation guides and tips",url:"https://browseros.notion.site/",icon:l.createElement("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"}))},{title:"Twitter / X",description:"Follow us for updates",url:"https://x.com/nxtscape",icon:l.createElement("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24"},l.createElement("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"}))}];return l.createElement("div",{className:"w-full max-w-md mx-auto"},l.createElement(Mn,null,l.createElement(In,null,l.createElement(jn,{className:"text-xl"},"Connect with BrowserOS"),l.createElement(Fn,null,"Join our community and stay updated")),l.createElement(An,{className:"space-y-3"},e.map(e=>l.createElement(Dn,{key:e.title,variant:"outline",className:"w-full justify-start text-left h-auto py-3 px-4",onClick:()=>window.open(e.url,"_blank")},l.createElement("div",{className:"mr-3 text-muted-foreground"},e.icon),l.createElement("div",{className:"flex-1"},l.createElement("div",{className:"font-medium"},e.title),l.createElement("div",{className:"text-sm text-muted-foreground"},e.description)),l.createElement("svg",{className:"w-4 h-4 ml-2 text-muted-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})))))))},Vn=()=>{const[e,t]=(0,l.useState)("updates"),[n,r]=(0,l.useState)(!1),[a,o]=(0,l.useState)(!0);return(0,l.useEffect)(()=>{chrome.runtime.getPlatformInfo(async e=>{const n="mac"===e.os;if(r(n),n)try{const e=!0===(await chrome.storage.local.get(["updateAvailable"])).updateAvailable;o(e),e||t("report")}catch(e){o(!1),t("report")}else o(!0)})},[]),l.createElement("div",{className:"min-h-screen bg-background flex flex-col"},l.createElement("div",{className:"bg-background border-b border-border text-foreground p-4"},l.createElement("h1",{className:"text-xl font-semibold text-center"},"BrowserOS Feedback")),l.createElement("div",{className:"flex border-b border-border bg-background"},a&&l.createElement("button",{onClick:()=>t("updates"),className:"flex-1 py-3 px-4 text-sm font-medium text-center transition-colors "+("updates"===e?"text-primary border-b-2 border-primary bg-secondary":"text-muted-foreground hover:text-foreground hover:bg-secondary/50")},"Check Updates"),l.createElement("button",{onClick:()=>t("report"),className:"flex-1 py-3 px-4 text-sm font-medium text-center transition-colors "+("report"===e?"text-primary border-b-2 border-primary bg-secondary":"text-muted-foreground hover:text-foreground hover:bg-secondary/50")},"Submit Feedback"),l.createElement("button",{onClick:()=>t("links"),className:"flex-1 py-3 px-4 text-sm font-medium text-center transition-colors "+("links"===e?"text-primary border-b-2 border-primary bg-secondary":"text-muted-foreground hover:text-foreground hover:bg-secondary/50")},"Links")),l.createElement("div",{className:"flex-1 p-4 overflow-y-auto"},"updates"===e&&a&&l.createElement(Un,null),"report"===e&&l.createElement(Ln,null),"links"===e&&l.createElement(Zn,null)))};var Bn=a(72),Wn=a.n(Bn),Hn=a(825),Qn=a.n(Hn),Kn=a(659),qn=a.n(Kn),Gn=a(56),Yn=a.n(Gn),Xn=a(159),Jn=a.n(Xn),er=a(113),tr=a.n(er),nr=a(439),rr={};rr.styleTagTransform=tr(),rr.setAttributes=Yn(),rr.insert=qn().bind(null,"head"),rr.domAPI=Qn(),rr.insertStyleElement=Jn();Wn()(nr.A,rr);nr.A&&nr.A.locals&&nr.A.locals;const ar=document.getElementById("root");if(!ar)throw new Error("Root container not found");(0,u.H)(ar).render(l.createElement(Vn,null))})();