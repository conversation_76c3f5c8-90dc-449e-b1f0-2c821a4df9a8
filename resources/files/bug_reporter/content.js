(()=>{"use strict";chrome.runtime.onMessage.addListener((t,e,n)=>{switch(t.type){case"GET_PAGE_INFO":n({url:window.location.href,title:document.title,timestamp:(new Date).toISOString()});break}return!0}),"undefined"!=typeof window&&(window.nxtscapeAgentContent={getPageContext:function(){return{url:window.location.href,title:document.title,userAgent:navigator.userAgent,viewport:{width:window.innerWidth,height:window.innerHeight},timestamp:(new Date).toISOString()}}})})();