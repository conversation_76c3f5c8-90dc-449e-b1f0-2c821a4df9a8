(()=>{"use strict";var e,t;!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const a of e)t[a]=a;return t},e.getValidEnumValues=t=>{const a=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),s={};for(const e of a)s[e]=t[e];return e.objectValues(s)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.push(a);return t},e.find=(e,t)=>{for(const a of e)if(t(a))return a},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(e||(e={})),function(e){e.mergeShapes=(e,t)=>({...e,...t})}(t||(t={}));const a=e.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),s=e=>{switch(typeof e){case"undefined":return a.undefined;case"string":return a.string;case"number":return Number.isNaN(e)?a.nan:a.number;case"boolean":return a.boolean;case"function":return a.function;case"bigint":return a.bigint;case"symbol":return a.symbol;case"object":return Array.isArray(e)?a.array:null===e?a.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?a.promise:"undefined"!=typeof Map&&e instanceof Map?a.map:"undefined"!=typeof Set&&e instanceof Set?a.set:"undefined"!=typeof Date&&e instanceof Date?a.date:a.object;default:return a.unknown}},n=e.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class r extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(e){return e.message},a={_errors:[]},s=e=>{for(const n of e.issues)if("invalid_union"===n.code)n.unionErrors.map(s);else if("invalid_return_type"===n.code)s(n.returnTypeError);else if("invalid_arguments"===n.code)s(n.argumentsError);else if(0===n.path.length)a._errors.push(t(n));else{let e=a,s=0;for(;s<n.path.length;){const a=n.path[s];s===n.path.length-1?(e[a]=e[a]||{_errors:[]},e[a]._errors.push(t(n))):e[a]=e[a]||{_errors:[]},e=e[a],s++}}};return s(this),a}static assert(e){if(!(e instanceof r))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,e.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},a=[];for(const s of this.issues)s.path.length>0?(t[s.path[0]]=t[s.path[0]]||[],t[s.path[0]].push(e(s))):a.push(e(s));return{formErrors:a,fieldErrors:t}}get formErrors(){return this.flatten()}}r.create=e=>new r(e);const i=(t,s)=>{let r;switch(t.code){case n.invalid_type:r=t.received===a.undefined?"Required":`Expected ${t.expected}, received ${t.received}`;break;case n.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(t.expected,e.jsonStringifyReplacer)}`;break;case n.unrecognized_keys:r=`Unrecognized key(s) in object: ${e.joinValues(t.keys,", ")}`;break;case n.invalid_union:r="Invalid input";break;case n.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${e.joinValues(t.options)}`;break;case n.invalid_enum_value:r=`Invalid enum value. Expected ${e.joinValues(t.options)}, received '${t.received}'`;break;case n.invalid_arguments:r="Invalid function arguments";break;case n.invalid_return_type:r="Invalid function return type";break;case n.invalid_date:r="Invalid date";break;case n.invalid_string:"object"==typeof t.validation?"includes"in t.validation?(r=`Invalid input: must include "${t.validation.includes}"`,"number"==typeof t.validation.position&&(r=`${r} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?r=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?r=`Invalid input: must end with "${t.validation.endsWith}"`:e.assertNever(t.validation):r="regex"!==t.validation?`Invalid ${t.validation}`:"Invalid";break;case n.too_small:r="array"===t.type?`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:"string"===t.type?`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:"number"===t.type?`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:"date"===t.type?`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:"Invalid input";break;case n.too_big:r="array"===t.type?`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:"string"===t.type?`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:"number"===t.type?`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:"bigint"===t.type?`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:"date"===t.type?`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:"Invalid input";break;case n.custom:r="Invalid input";break;case n.invalid_intersection_types:r="Intersection results could not be merged";break;case n.not_multiple_of:r=`Number must be a multiple of ${t.multipleOf}`;break;case n.not_finite:r="Number must be finite";break;default:r=s.defaultError,e.assertNever(t)}return{message:r}};let o=i;function d(){return o}const c=e=>{const{data:t,path:a,errorMaps:s,issueData:n}=e,r=[...a,...n.path||[]],i={...n,path:r};if(void 0!==n.message)return{...n,path:r,message:n.message};let o="";const d=s.filter(e=>!!e).slice().reverse();for(const e of d)o=e(i,{data:t,defaultError:o}).message;return{...n,path:r,message:o}};function u(e,t){const a=d(),s=c({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,a,a===i?void 0:i].filter(e=>!!e)});e.common.issues.push(s)}class l{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const a=[];for(const s of t){if("aborted"===s.status)return h;"dirty"===s.status&&e.dirty(),a.push(s.value)}return{status:e.value,value:a}}static async mergeObjectAsync(e,t){const a=[];for(const e of t){const t=await e.key,s=await e.value;a.push({key:t,value:s})}return l.mergeObjectSync(e,a)}static mergeObjectSync(e,t){const a={};for(const s of t){const{key:t,value:n}=s;if("aborted"===t.status)return h;if("aborted"===n.status)return h;"dirty"===t.status&&e.dirty(),"dirty"===n.status&&e.dirty(),"__proto__"===t.value||void 0===n.value&&!s.alwaysSet||(a[t.value]=n.value)}return{status:e.value,value:a}}}const h=Object.freeze({status:"aborted"}),p=e=>({status:"dirty",value:e}),m=e=>({status:"valid",value:e}),f=e=>"aborted"===e.status,_=e=>"dirty"===e.status,g=e=>"valid"===e.status,y=e=>"undefined"!=typeof Promise&&e instanceof Promise;var v;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(v||(v={}));class k{constructor(e,t,a,s){this._cachedPath=[],this.parent=e,this.data=t,this._path=a,this._key=s}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const x=(e,t)=>{if(g(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new r(e.common.issues);return this._error=t,this._error}}};function b(e){if(!e)return{};const{errorMap:t,invalid_type_error:a,required_error:s,description:n}=e;if(t&&(a||s))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:n};return{errorMap:(t,n)=>{const{message:r}=e;return"invalid_enum_value"===t.code?{message:r??n.defaultError}:void 0===n.data?{message:r??s??n.defaultError}:"invalid_type"!==t.code?{message:n.defaultError}:{message:r??a??n.defaultError}},description:n}}class w{get description(){return this._def.description}_getType(e){return s(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:s(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new l,ctx:{common:e.parent.common,data:e.data,parsedType:s(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(y(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const a=this.safeParse(e,t);if(a.success)return a.data;throw a.error}safeParse(e,t){const a={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)},n=this._parseSync({data:e,path:a.path,parent:a});return x(a,n)}"~validate"(e){const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:t});return g(a)?{value:a.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>g(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){const a=await this.safeParseAsync(e,t);if(a.success)return a.data;throw a.error}async safeParseAsync(e,t){const a={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)},n=this._parse({data:e,path:a.path,parent:a}),r=await(y(n)?n:Promise.resolve(n));return x(a,r)}refine(e,t){const a=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,s)=>{const r=e(t),i=()=>s.addIssue({code:n.custom,...a(t)});return"undefined"!=typeof Promise&&r instanceof Promise?r.then(e=>!!e||(i(),!1)):!!r||(i(),!1)})}refinement(e,t){return this._refinement((a,s)=>!!e(a)||(s.addIssue("function"==typeof t?t(a,s):t),!1))}_refinement(e){return new xe({schema:this,typeName:Ne.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return be.create(this,this._def)}nullable(){return we.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ie.create(this)}promise(){return ke.create(this,this._def)}or(e){return ce.create([this,e],this._def)}and(e){return le.create(this,e,this._def)}transform(e){return new xe({...b(this._def),schema:this,typeName:Ne.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new Te({...b(this._def),innerType:this,defaultValue:t,typeName:Ne.ZodDefault})}brand(){return new Oe({typeName:Ne.ZodBranded,type:this,...b(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new Ce({...b(this._def),innerType:this,catchValue:t,typeName:Ne.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return Se.create(this,e)}readonly(){return Ae.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const T=/^c[^\s-]{8,}$/i,C=/^[0-9a-z]+$/,Z=/^[0-9A-HJKMNP-TV-Z]{26}$/i,O=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,S=/^[a-z0-9_-]{21}$/i,A=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,N=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,j=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let I;const E=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,R=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,P=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,$=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,F=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,U=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,L="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",M=new RegExp(`^${L}$`);function D(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${e.precision?"+":"?"}`}function z(e){return new RegExp(`^${D(e)}$`)}function V(e){let t=`${L}T${D(e)}`;const a=[];return a.push(e.local?"Z?":"Z"),e.offset&&a.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${a.join("|")})`,new RegExp(`^${t}$`)}function B(e,t){return!("v4"!==t&&t||!E.test(e))||!("v6"!==t&&t||!P.test(e))}function K(e,t){if(!A.test(e))return!1;try{const[a]=e.split("."),s=a.replace(/-/g,"+").replace(/_/g,"/").padEnd(a.length+(4-a.length%4)%4,"="),n=JSON.parse(atob(s));return"object"==typeof n&&null!==n&&((!("typ"in n)||"JWT"===n?.typ)&&(!!n.alg&&(!t||n.alg===t)))}catch{return!1}}function W(e,t){return!("v4"!==t&&t||!R.test(e))||!("v6"!==t&&t||!$.test(e))}class q extends w{_parse(t){this._def.coerce&&(t.data=String(t.data));if(this._getType(t)!==a.string){const e=this._getOrReturnCtx(t);return u(e,{code:n.invalid_type,expected:a.string,received:e.parsedType}),h}const s=new l;let r;for(const a of this._def.checks)if("min"===a.kind)t.data.length<a.value&&(r=this._getOrReturnCtx(t,r),u(r,{code:n.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),s.dirty());else if("max"===a.kind)t.data.length>a.value&&(r=this._getOrReturnCtx(t,r),u(r,{code:n.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),s.dirty());else if("length"===a.kind){const e=t.data.length>a.value,i=t.data.length<a.value;(e||i)&&(r=this._getOrReturnCtx(t,r),e?u(r,{code:n.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):i&&u(r,{code:n.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),s.dirty())}else if("email"===a.kind)j.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"email",code:n.invalid_string,message:a.message}),s.dirty());else if("emoji"===a.kind)I||(I=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),I.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"emoji",code:n.invalid_string,message:a.message}),s.dirty());else if("uuid"===a.kind)O.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"uuid",code:n.invalid_string,message:a.message}),s.dirty());else if("nanoid"===a.kind)S.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"nanoid",code:n.invalid_string,message:a.message}),s.dirty());else if("cuid"===a.kind)T.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"cuid",code:n.invalid_string,message:a.message}),s.dirty());else if("cuid2"===a.kind)C.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"cuid2",code:n.invalid_string,message:a.message}),s.dirty());else if("ulid"===a.kind)Z.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"ulid",code:n.invalid_string,message:a.message}),s.dirty());else if("url"===a.kind)try{new URL(t.data)}catch{r=this._getOrReturnCtx(t,r),u(r,{validation:"url",code:n.invalid_string,message:a.message}),s.dirty()}else if("regex"===a.kind){a.regex.lastIndex=0;a.regex.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"regex",code:n.invalid_string,message:a.message}),s.dirty())}else if("trim"===a.kind)t.data=t.data.trim();else if("includes"===a.kind)t.data.includes(a.value,a.position)||(r=this._getOrReturnCtx(t,r),u(r,{code:n.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),s.dirty());else if("toLowerCase"===a.kind)t.data=t.data.toLowerCase();else if("toUpperCase"===a.kind)t.data=t.data.toUpperCase();else if("startsWith"===a.kind)t.data.startsWith(a.value)||(r=this._getOrReturnCtx(t,r),u(r,{code:n.invalid_string,validation:{startsWith:a.value},message:a.message}),s.dirty());else if("endsWith"===a.kind)t.data.endsWith(a.value)||(r=this._getOrReturnCtx(t,r),u(r,{code:n.invalid_string,validation:{endsWith:a.value},message:a.message}),s.dirty());else if("datetime"===a.kind){V(a).test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{code:n.invalid_string,validation:"datetime",message:a.message}),s.dirty())}else if("date"===a.kind){M.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{code:n.invalid_string,validation:"date",message:a.message}),s.dirty())}else if("time"===a.kind){z(a).test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{code:n.invalid_string,validation:"time",message:a.message}),s.dirty())}else"duration"===a.kind?N.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"duration",code:n.invalid_string,message:a.message}),s.dirty()):"ip"===a.kind?B(t.data,a.version)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"ip",code:n.invalid_string,message:a.message}),s.dirty()):"jwt"===a.kind?K(t.data,a.alg)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"jwt",code:n.invalid_string,message:a.message}),s.dirty()):"cidr"===a.kind?W(t.data,a.version)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"cidr",code:n.invalid_string,message:a.message}),s.dirty()):"base64"===a.kind?F.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"base64",code:n.invalid_string,message:a.message}),s.dirty()):"base64url"===a.kind?U.test(t.data)||(r=this._getOrReturnCtx(t,r),u(r,{validation:"base64url",code:n.invalid_string,message:a.message}),s.dirty()):e.assertNever(a);return{status:s.value,value:t.data}}_regex(e,t,a){return this.refinement(t=>e.test(t),{validation:t,code:n.invalid_string,...v.errToObj(a)})}_addCheck(e){return new q({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...v.errToObj(e)})}url(e){return this._addCheck({kind:"url",...v.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...v.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...v.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...v.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...v.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...v.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...v.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...v.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...v.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...v.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...v.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...v.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...v.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...v.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...v.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...v.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...v.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...v.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...v.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...v.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...v.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...v.errToObj(t)})}nonempty(e){return this.min(1,v.errToObj(e))}trim(){return new q({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new q({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new q({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function G(e,t){const a=(e.toString().split(".")[1]||"").length,s=(t.toString().split(".")[1]||"").length,n=a>s?a:s;return Number.parseInt(e.toFixed(n).replace(".",""))%Number.parseInt(t.toFixed(n).replace(".",""))/10**n}q.create=e=>new q({checks:[],typeName:Ne.ZodString,coerce:e?.coerce??!1,...b(e)});class H extends w{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){this._def.coerce&&(t.data=Number(t.data));if(this._getType(t)!==a.number){const e=this._getOrReturnCtx(t);return u(e,{code:n.invalid_type,expected:a.number,received:e.parsedType}),h}let s;const r=new l;for(const a of this._def.checks)if("int"===a.kind)e.isInteger(t.data)||(s=this._getOrReturnCtx(t,s),u(s,{code:n.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty());else if("min"===a.kind){(a.inclusive?t.data<a.value:t.data<=a.value)&&(s=this._getOrReturnCtx(t,s),u(s,{code:n.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty())}else if("max"===a.kind){(a.inclusive?t.data>a.value:t.data>=a.value)&&(s=this._getOrReturnCtx(t,s),u(s,{code:n.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty())}else"multipleOf"===a.kind?0!==G(t.data,a.value)&&(s=this._getOrReturnCtx(t,s),u(s,{code:n.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):"finite"===a.kind?Number.isFinite(t.data)||(s=this._getOrReturnCtx(t,s),u(s,{code:n.not_finite,message:a.message}),r.dirty()):e.assertNever(a);return{status:r.value,value:t.data}}gte(e,t){return this.setLimit("min",e,!0,v.toString(t))}gt(e,t){return this.setLimit("min",e,!1,v.toString(t))}lte(e,t){return this.setLimit("max",e,!0,v.toString(t))}lt(e,t){return this.setLimit("max",e,!1,v.toString(t))}setLimit(e,t,a,s){return new H({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:v.toString(s)}]})}_addCheck(e){return new H({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:v.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:v.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:v.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:v.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:v.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:v.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:v.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:v.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:v.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(t=>"int"===t.kind||"multipleOf"===t.kind&&e.isInteger(t.value))}get isFinite(){let e=null,t=null;for(const a of this._def.checks){if("finite"===a.kind||"int"===a.kind||"multipleOf"===a.kind)return!0;"min"===a.kind?(null===t||a.value>t)&&(t=a.value):"max"===a.kind&&(null===e||a.value<e)&&(e=a.value)}return Number.isFinite(t)&&Number.isFinite(e)}}H.create=e=>new H({checks:[],typeName:Ne.ZodNumber,coerce:e?.coerce||!1,...b(e)});class J extends w{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==a.bigint)return this._getInvalidInput(t);let s;const r=new l;for(const a of this._def.checks)if("min"===a.kind){(a.inclusive?t.data<a.value:t.data<=a.value)&&(s=this._getOrReturnCtx(t,s),u(s,{code:n.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty())}else if("max"===a.kind){(a.inclusive?t.data>a.value:t.data>=a.value)&&(s=this._getOrReturnCtx(t,s),u(s,{code:n.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty())}else"multipleOf"===a.kind?t.data%a.value!==BigInt(0)&&(s=this._getOrReturnCtx(t,s),u(s,{code:n.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):e.assertNever(a);return{status:r.value,value:t.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return u(t,{code:n.invalid_type,expected:a.bigint,received:t.parsedType}),h}gte(e,t){return this.setLimit("min",e,!0,v.toString(t))}gt(e,t){return this.setLimit("min",e,!1,v.toString(t))}lte(e,t){return this.setLimit("max",e,!0,v.toString(t))}lt(e,t){return this.setLimit("max",e,!1,v.toString(t))}setLimit(e,t,a,s){return new J({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:v.toString(s)}]})}_addCheck(e){return new J({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:v.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:v.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:v.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:v.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:v.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}J.create=e=>new J({checks:[],typeName:Ne.ZodBigInt,coerce:e?.coerce??!1,...b(e)});class Y extends w{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==a.boolean){const t=this._getOrReturnCtx(e);return u(t,{code:n.invalid_type,expected:a.boolean,received:t.parsedType}),h}return m(e.data)}}Y.create=e=>new Y({typeName:Ne.ZodBoolean,coerce:e?.coerce||!1,...b(e)});class X extends w{_parse(t){this._def.coerce&&(t.data=new Date(t.data));if(this._getType(t)!==a.date){const e=this._getOrReturnCtx(t);return u(e,{code:n.invalid_type,expected:a.date,received:e.parsedType}),h}if(Number.isNaN(t.data.getTime())){return u(this._getOrReturnCtx(t),{code:n.invalid_date}),h}const s=new l;let r;for(const a of this._def.checks)"min"===a.kind?t.data.getTime()<a.value&&(r=this._getOrReturnCtx(t,r),u(r,{code:n.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),s.dirty()):"max"===a.kind?t.data.getTime()>a.value&&(r=this._getOrReturnCtx(t,r),u(r,{code:n.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),s.dirty()):e.assertNever(a);return{status:s.value,value:new Date(t.data.getTime())}}_addCheck(e){return new X({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:v.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:v.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}X.create=e=>new X({checks:[],coerce:e?.coerce||!1,typeName:Ne.ZodDate,...b(e)});class Q extends w{_parse(e){if(this._getType(e)!==a.symbol){const t=this._getOrReturnCtx(e);return u(t,{code:n.invalid_type,expected:a.symbol,received:t.parsedType}),h}return m(e.data)}}Q.create=e=>new Q({typeName:Ne.ZodSymbol,...b(e)});class ee extends w{_parse(e){if(this._getType(e)!==a.undefined){const t=this._getOrReturnCtx(e);return u(t,{code:n.invalid_type,expected:a.undefined,received:t.parsedType}),h}return m(e.data)}}ee.create=e=>new ee({typeName:Ne.ZodUndefined,...b(e)});class te extends w{_parse(e){if(this._getType(e)!==a.null){const t=this._getOrReturnCtx(e);return u(t,{code:n.invalid_type,expected:a.null,received:t.parsedType}),h}return m(e.data)}}te.create=e=>new te({typeName:Ne.ZodNull,...b(e)});class ae extends w{constructor(){super(...arguments),this._any=!0}_parse(e){return m(e.data)}}ae.create=e=>new ae({typeName:Ne.ZodAny,...b(e)});class se extends w{constructor(){super(...arguments),this._unknown=!0}_parse(e){return m(e.data)}}se.create=e=>new se({typeName:Ne.ZodUnknown,...b(e)});class ne extends w{_parse(e){const t=this._getOrReturnCtx(e);return u(t,{code:n.invalid_type,expected:a.never,received:t.parsedType}),h}}ne.create=e=>new ne({typeName:Ne.ZodNever,...b(e)});class re extends w{_parse(e){if(this._getType(e)!==a.undefined){const t=this._getOrReturnCtx(e);return u(t,{code:n.invalid_type,expected:a.void,received:t.parsedType}),h}return m(e.data)}}re.create=e=>new re({typeName:Ne.ZodVoid,...b(e)});class ie extends w{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),r=this._def;if(t.parsedType!==a.array)return u(t,{code:n.invalid_type,expected:a.array,received:t.parsedType}),h;if(null!==r.exactLength){const e=t.data.length>r.exactLength.value,a=t.data.length<r.exactLength.value;(e||a)&&(u(t,{code:e?n.too_big:n.too_small,minimum:a?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),s.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(u(t,{code:n.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),s.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(u(t,{code:n.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((e,a)=>r.type._parseAsync(new k(t,e,t.path,a)))).then(e=>l.mergeArray(s,e));const i=[...t.data].map((e,a)=>r.type._parseSync(new k(t,e,t.path,a)));return l.mergeArray(s,i)}get element(){return this._def.type}min(e,t){return new ie({...this._def,minLength:{value:e,message:v.toString(t)}})}max(e,t){return new ie({...this._def,maxLength:{value:e,message:v.toString(t)}})}length(e,t){return new ie({...this._def,exactLength:{value:e,message:v.toString(t)}})}nonempty(e){return this.min(1,e)}}function oe(e){if(e instanceof de){const t={};for(const a in e.shape){const s=e.shape[a];t[a]=be.create(oe(s))}return new de({...e._def,shape:()=>t})}return e instanceof ie?new ie({...e._def,type:oe(e.element)}):e instanceof be?be.create(oe(e.unwrap())):e instanceof we?we.create(oe(e.unwrap())):e instanceof he?he.create(e.items.map(e=>oe(e))):e}ie.create=(e,t)=>new ie({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Ne.ZodArray,...b(t)});class de extends w{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const t=this._def.shape(),a=e.objectKeys(t);return this._cached={shape:t,keys:a},this._cached}_parse(e){if(this._getType(e)!==a.object){const t=this._getOrReturnCtx(e);return u(t,{code:n.invalid_type,expected:a.object,received:t.parsedType}),h}const{status:t,ctx:s}=this._processInputParams(e),{shape:r,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof ne&&"strip"===this._def.unknownKeys))for(const e in s.data)i.includes(e)||o.push(e);const d=[];for(const e of i){const t=r[e],a=s.data[e];d.push({key:{status:"valid",value:e},value:t._parse(new k(s,a,s.path,e)),alwaysSet:e in s.data})}if(this._def.catchall instanceof ne){const e=this._def.unknownKeys;if("passthrough"===e)for(const e of o)d.push({key:{status:"valid",value:e},value:{status:"valid",value:s.data[e]}});else if("strict"===e)o.length>0&&(u(s,{code:n.unrecognized_keys,keys:o}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of o){const a=s.data[t];d.push({key:{status:"valid",value:t},value:e._parse(new k(s,a,s.path,t)),alwaysSet:t in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const e=[];for(const t of d){const a=await t.key,s=await t.value;e.push({key:a,value:s,alwaysSet:t.alwaysSet})}return e}).then(e=>l.mergeObjectSync(t,e)):l.mergeObjectSync(t,d)}get shape(){return this._def.shape()}strict(e){return v.errToObj,new de({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,a)=>{const s=this._def.errorMap?.(t,a).message??a.defaultError;return"unrecognized_keys"===t.code?{message:v.errToObj(e).message??s}:{message:s}}}:{}})}strip(){return new de({...this._def,unknownKeys:"strip"})}passthrough(){return new de({...this._def,unknownKeys:"passthrough"})}extend(e){return new de({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new de({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Ne.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new de({...this._def,catchall:e})}pick(t){const a={};for(const s of e.objectKeys(t))t[s]&&this.shape[s]&&(a[s]=this.shape[s]);return new de({...this._def,shape:()=>a})}omit(t){const a={};for(const s of e.objectKeys(this.shape))t[s]||(a[s]=this.shape[s]);return new de({...this._def,shape:()=>a})}deepPartial(){return oe(this)}partial(t){const a={};for(const s of e.objectKeys(this.shape)){const e=this.shape[s];t&&!t[s]?a[s]=e:a[s]=e.optional()}return new de({...this._def,shape:()=>a})}required(t){const a={};for(const s of e.objectKeys(this.shape))if(t&&!t[s])a[s]=this.shape[s];else{let e=this.shape[s];for(;e instanceof be;)e=e._def.innerType;a[s]=e}return new de({...this._def,shape:()=>a})}keyof(){return ge(e.objectKeys(this.shape))}}de.create=(e,t)=>new de({shape:()=>e,unknownKeys:"strip",catchall:ne.create(),typeName:Ne.ZodObject,...b(t)}),de.strictCreate=(e,t)=>new de({shape:()=>e,unknownKeys:"strict",catchall:ne.create(),typeName:Ne.ZodObject,...b(t)}),de.lazycreate=(e,t)=>new de({shape:e,unknownKeys:"strip",catchall:ne.create(),typeName:Ne.ZodObject,...b(t)});class ce extends w{_parse(e){const{ctx:t}=this._processInputParams(e),a=this._def.options;if(t.common.async)return Promise.all(a.map(async e=>{const a={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;const a=e.map(e=>new r(e.ctx.common.issues));return u(t,{code:n.invalid_union,unionErrors:a}),h});{let e;const s=[];for(const n of a){const a={...t,common:{...t.common,issues:[]},parent:null},r=n._parseSync({data:t.data,path:t.path,parent:a});if("valid"===r.status)return r;"dirty"!==r.status||e||(e={result:r,ctx:a}),a.common.issues.length&&s.push(a.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const i=s.map(e=>new r(e));return u(t,{code:n.invalid_union,unionErrors:i}),h}}get options(){return this._def.options}}ce.create=(e,t)=>new ce({options:e,typeName:Ne.ZodUnion,...b(t)});function ue(t,n){const r=s(t),i=s(n);if(t===n)return{valid:!0,data:t};if(r===a.object&&i===a.object){const a=e.objectKeys(n),s=e.objectKeys(t).filter(e=>-1!==a.indexOf(e)),r={...t,...n};for(const e of s){const a=ue(t[e],n[e]);if(!a.valid)return{valid:!1};r[e]=a.data}return{valid:!0,data:r}}if(r===a.array&&i===a.array){if(t.length!==n.length)return{valid:!1};const e=[];for(let a=0;a<t.length;a++){const s=ue(t[a],n[a]);if(!s.valid)return{valid:!1};e.push(s.data)}return{valid:!0,data:e}}return r===a.date&&i===a.date&&+t===+n?{valid:!0,data:t}:{valid:!1}}class le extends w{_parse(e){const{status:t,ctx:a}=this._processInputParams(e),s=(e,s)=>{if(f(e)||f(s))return h;const r=ue(e.value,s.value);return r.valid?((_(e)||_(s))&&t.dirty(),{status:t.value,value:r.data}):(u(a,{code:n.invalid_intersection_types}),h)};return a.common.async?Promise.all([this._def.left._parseAsync({data:a.data,path:a.path,parent:a}),this._def.right._parseAsync({data:a.data,path:a.path,parent:a})]).then(([e,t])=>s(e,t)):s(this._def.left._parseSync({data:a.data,path:a.path,parent:a}),this._def.right._parseSync({data:a.data,path:a.path,parent:a}))}}le.create=(e,t,a)=>new le({left:e,right:t,typeName:Ne.ZodIntersection,...b(a)});class he extends w{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==a.array)return u(s,{code:n.invalid_type,expected:a.array,received:s.parsedType}),h;if(s.data.length<this._def.items.length)return u(s,{code:n.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),h;!this._def.rest&&s.data.length>this._def.items.length&&(u(s,{code:n.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...s.data].map((e,t)=>{const a=this._def.items[t]||this._def.rest;return a?a._parse(new k(s,e,s.path,t)):null}).filter(e=>!!e);return s.common.async?Promise.all(r).then(e=>l.mergeArray(t,e)):l.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new he({...this._def,rest:e})}}he.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new he({items:e,typeName:Ne.ZodTuple,rest:null,...b(t)})};class pe extends w{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==a.map)return u(s,{code:n.invalid_type,expected:a.map,received:s.parsedType}),h;const r=this._def.keyType,i=this._def.valueType,o=[...s.data.entries()].map(([e,t],a)=>({key:r._parse(new k(s,e,s.path,[a,"key"])),value:i._parse(new k(s,t,s.path,[a,"value"]))}));if(s.common.async){const e=new Map;return Promise.resolve().then(async()=>{for(const a of o){const s=await a.key,n=await a.value;if("aborted"===s.status||"aborted"===n.status)return h;"dirty"!==s.status&&"dirty"!==n.status||t.dirty(),e.set(s.value,n.value)}return{status:t.value,value:e}})}{const e=new Map;for(const a of o){const s=a.key,n=a.value;if("aborted"===s.status||"aborted"===n.status)return h;"dirty"!==s.status&&"dirty"!==n.status||t.dirty(),e.set(s.value,n.value)}return{status:t.value,value:e}}}}pe.create=(e,t,a)=>new pe({valueType:t,keyType:e,typeName:Ne.ZodMap,...b(a)});class me extends w{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==a.set)return u(s,{code:n.invalid_type,expected:a.set,received:s.parsedType}),h;const r=this._def;null!==r.minSize&&s.data.size<r.minSize.value&&(u(s,{code:n.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&s.data.size>r.maxSize.value&&(u(s,{code:n.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const i=this._def.valueType;function o(e){const a=new Set;for(const s of e){if("aborted"===s.status)return h;"dirty"===s.status&&t.dirty(),a.add(s.value)}return{status:t.value,value:a}}const d=[...s.data.values()].map((e,t)=>i._parse(new k(s,e,s.path,t)));return s.common.async?Promise.all(d).then(e=>o(e)):o(d)}min(e,t){return new me({...this._def,minSize:{value:e,message:v.toString(t)}})}max(e,t){return new me({...this._def,maxSize:{value:e,message:v.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}me.create=(e,t)=>new me({valueType:e,minSize:null,maxSize:null,typeName:Ne.ZodSet,...b(t)});class fe extends w{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}fe.create=(e,t)=>new fe({getter:e,typeName:Ne.ZodLazy,...b(t)});class _e extends w{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return u(t,{received:t.data,code:n.invalid_literal,expected:this._def.value}),h}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ge(e,t){return new ye({values:e,typeName:Ne.ZodEnum,...b(t)})}_e.create=(e,t)=>new _e({value:e,typeName:Ne.ZodLiteral,...b(t)});class ye extends w{_parse(t){if("string"!=typeof t.data){const a=this._getOrReturnCtx(t),s=this._def.values;return u(a,{expected:e.joinValues(s),received:a.parsedType,code:n.invalid_type}),h}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(t.data)){const e=this._getOrReturnCtx(t),a=this._def.values;return u(e,{received:e.data,code:n.invalid_enum_value,options:a}),h}return m(t.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ye.create(e,{...this._def,...t})}exclude(e,t=this._def){return ye.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}ye.create=ge;class ve extends w{_parse(t){const s=e.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==a.string&&r.parsedType!==a.number){const t=e.objectValues(s);return u(r,{expected:e.joinValues(t),received:r.parsedType,code:n.invalid_type}),h}if(this._cache||(this._cache=new Set(e.getValidEnumValues(this._def.values))),!this._cache.has(t.data)){const t=e.objectValues(s);return u(r,{received:r.data,code:n.invalid_enum_value,options:t}),h}return m(t.data)}get enum(){return this._def.values}}ve.create=(e,t)=>new ve({values:e,typeName:Ne.ZodNativeEnum,...b(t)});class ke extends w{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==a.promise&&!1===t.common.async)return u(t,{code:n.invalid_type,expected:a.promise,received:t.parsedType}),h;const s=t.parsedType===a.promise?t.data:Promise.resolve(t.data);return m(s.then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}ke.create=(e,t)=>new ke({type:e,typeName:Ne.ZodPromise,...b(t)});class xe extends w{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Ne.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:a,ctx:s}=this._processInputParams(t),n=this._def.effect||null,r={addIssue:e=>{u(s,e),e.fatal?a.abort():a.dirty()},get path(){return s.path}};if(r.addIssue=r.addIssue.bind(r),"preprocess"===n.type){const e=n.transform(s.data,r);if(s.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===a.value)return h;const t=await this._def.schema._parseAsync({data:e,path:s.path,parent:s});return"aborted"===t.status?h:"dirty"===t.status||"dirty"===a.value?p(t.value):t});{if("aborted"===a.value)return h;const t=this._def.schema._parseSync({data:e,path:s.path,parent:s});return"aborted"===t.status?h:"dirty"===t.status||"dirty"===a.value?p(t.value):t}}if("refinement"===n.type){const e=e=>{const t=n.refinement(e,r);if(s.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===s.common.async){const t=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return"aborted"===t.status?h:("dirty"===t.status&&a.dirty(),e(t.value),{status:a.value,value:t.value})}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(t=>"aborted"===t.status?h:("dirty"===t.status&&a.dirty(),e(t.value).then(()=>({status:a.value,value:t.value}))))}if("transform"===n.type){if(!1===s.common.async){const e=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!g(e))return h;const t=n.transform(e.value,r);if(t instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:a.value,value:t}}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(e=>g(e)?Promise.resolve(n.transform(e.value,r)).then(e=>({status:a.value,value:e})):h)}e.assertNever(n)}}xe.create=(e,t,a)=>new xe({schema:e,typeName:Ne.ZodEffects,effect:t,...b(a)}),xe.createWithPreprocess=(e,t,a)=>new xe({schema:t,effect:{type:"preprocess",transform:e},typeName:Ne.ZodEffects,...b(a)});class be extends w{_parse(e){return this._getType(e)===a.undefined?m(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}be.create=(e,t)=>new be({innerType:e,typeName:Ne.ZodOptional,...b(t)});class we extends w{_parse(e){return this._getType(e)===a.null?m(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}we.create=(e,t)=>new we({innerType:e,typeName:Ne.ZodNullable,...b(t)});class Te extends w{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===a.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Te.create=(e,t)=>new Te({innerType:e,typeName:Ne.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...b(t)});class Ce extends w{_parse(e){const{ctx:t}=this._processInputParams(e),a={...t,common:{...t.common,issues:[]}},s=this._def.innerType._parse({data:a.data,path:a.path,parent:{...a}});return y(s)?s.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new r(a.common.issues)},input:a.data})})):{status:"valid",value:"valid"===s.status?s.value:this._def.catchValue({get error(){return new r(a.common.issues)},input:a.data})}}removeCatch(){return this._def.innerType}}Ce.create=(e,t)=>new Ce({innerType:e,typeName:Ne.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...b(t)});class Ze extends w{_parse(e){if(this._getType(e)!==a.nan){const t=this._getOrReturnCtx(e);return u(t,{code:n.invalid_type,expected:a.nan,received:t.parsedType}),h}return{status:"valid",value:e.data}}}Ze.create=e=>new Ze({typeName:Ne.ZodNaN,...b(e)});Symbol("zod_brand");class Oe extends w{_parse(e){const{ctx:t}=this._processInputParams(e),a=t.data;return this._def.type._parse({data:a,path:t.path,parent:t})}unwrap(){return this._def.type}}class Se extends w{_parse(e){const{status:t,ctx:a}=this._processInputParams(e);if(a.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?h:"dirty"===e.status?(t.dirty(),p(e.value)):this._def.out._parseAsync({data:e.value,path:a.path,parent:a})})()}{const e=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?h:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:a.path,parent:a})}}static create(e,t){return new Se({in:e,out:t,typeName:Ne.ZodPipeline})}}class Ae extends w{_parse(e){const t=this._def.innerType._parse(e),a=e=>(g(e)&&(e.value=Object.freeze(e.value)),e);return y(t)?t.then(e=>a(e)):a(t)}unwrap(){return this._def.innerType}}Ae.create=(e,t)=>new Ae({innerType:e,typeName:Ne.ZodReadonly,...b(t)});de.lazycreate;var Ne;!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(Ne||(Ne={}));const je=q.create,Ie=H.create,Ee=(Ze.create,J.create,Y.create),Re=X.create,Pe=(Q.create,ee.create,te.create,ae.create,se.create),$e=(ne.create,re.create,ie.create,de.create),Fe=(de.strictCreate,ce.create),Ue=(le.create,he.create,pe.create,me.create,fe.create,_e.create,ye.create),Le=(ve.create,ke.create,xe.create,be.create,we.create,xe.createWithPreprocess,Se.create,$e({type:Ue(["bug","feature"]),title:je().min(1).max(200),description:je().min(10).max(5e3),email:je().email().optional(),timestamp:Re().optional(),url:je().url().optional(),currentUrl:je().optional(),screenshot:je().optional()})),Me=(Ue(["checking","available","current","error"]),$e({currentVersion:je(),latestVersion:je(),updateUrl:je().url(),releaseNotes:je().optional(),checkDate:Re()}),$e({updateCheckEnabled:Ee().default(!0),updateCheckInterval:Ie().int().min(1).max(1440).default(10),updateUrl:je().url().optional(),lastUpdateCheck:Re().optional(),notifications:$e({showUpdateNotifications:Ee().default(!0),showSubmitConfirmations:Ee().default(!0)})}),$e({type:Ue(["SUBMIT_REPORT","CHECK_UPDATE","UPDATE_SETTINGS","GET_SETTINGS"]),data:Pe().optional(),tabId:Ie().int().optional(),timestamp:Fe([Re(),je()]).optional()}));Le.extend({id:je().uuid(),status:Ue(["pending","sent","failed"]),attempts:Ie().int().min(0).default(0),lastAttempt:Re().optional()});async function De(e){try{const e=chrome.runtime.getManifest().browser_version;if(!e)return await chrome.storage.local.set({lastUpdateCheck:(new Date).toISOString(),updateAvailable:!1,settings:{...(await chrome.storage.local.get(["settings"])).settings||{},lastUpdateCheck:new Date}}),void chrome.action.setBadgeText({text:""});const t=(await chrome.runtime.getPlatformInfo()).os,a=`https://cdn.browseros.com/api/check-update?version=${encodeURIComponent(e)}&os=${encodeURIComponent(t)}`,s=await fetch(a);if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);const n=await s.json();let r=n.updateAvailable;"mac"===t&&(r=n.updateAvailable&&!0===n.showMacOSUpdate),await chrome.storage.local.set({lastUpdateCheck:(new Date).toISOString(),updateAvailable:r,latestVersion:n.latestVersion,currentVersion:n.currentVersion,downloadUrl:n.downloadUrl||"",settings:{...(await chrome.storage.local.get(["settings"])).settings||{},lastUpdateCheck:new Date}}),r?(chrome.action.setBadgeText({text:"1"}),chrome.action.setBadgeBackgroundColor({color:"#FF0000"})):chrome.action.setBadgeText({text:""})}catch(e){chrome.action.setBadgeText({text:""})}}(async()=>{try{(await chrome.storage.local.get(["updateAvailable"])).updateAvailable&&(chrome.action.setBadgeText({text:"1"}),chrome.action.setBadgeBackgroundColor({color:"#FF0000"}))}catch(e){}})(),chrome.runtime.onInstalled.addListener(async()=>{chrome.alarms.create("checkForUpdates",{periodInMinutes:10,delayInMinutes:.1}),await De({updateUrl:""})}),chrome.alarms.onAlarm.addListener(async e=>{"checkForUpdates"===e.name&&await De({updateUrl:""})}),chrome.runtime.onMessage.addListener((e,t,a)=>{try{const t=Me.parse(e);switch(t.type){case"SUBMIT_REPORT":(async function(e){try{const t=(await chrome.storage.local.get(["reports"])).reports||[],a={...e,id:Date.now().toString(),timestamp:(new Date).toISOString(),userAgent:navigator.userAgent};t.push(a),await chrome.storage.local.set({reports:t});const s=await fetch("https://cdn.browseros.com/api/feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,request:e.type,contents:e.description,screenshot:e.screenshot||void 0})});if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`)}catch(e){throw e}})(t.data).then(()=>a({success:!0})).catch(e=>a({success:!1,error:e.message}));break;case"CHECK_UPDATE":De(t.data).then(()=>a({success:!0})).catch(e=>a({success:!1,error:e.message}));break;case"UPDATE_SETTINGS":(async function(e){try{const t=await chrome.storage.local.get(["settings"]),a={...t.settings||{},...e};await chrome.storage.local.set({settings:a})}catch(e){throw e}})(t.data).then(()=>a({success:!0})).catch(e=>a({success:!1,error:e.message}));break;case"GET_SETTINGS":(async function(){try{return(await chrome.storage.local.get(["settings"])).settings||{}}catch(e){throw e}})().then(e=>a({success:!0,data:e})).catch(e=>a({success:!1,error:e.message}));break;default:a({success:!1,error:"Unknown message type"})}}catch(e){a({success:!1,error:"Invalid message format"})}return!0})})();