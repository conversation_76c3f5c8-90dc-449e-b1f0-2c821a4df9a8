(()=>{"use strict";var e,t;!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const a of e)t[a]=a;return t},e.getValidEnumValues=t=>{const a=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),n={};for(const e of a)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.push(a);return t},e.find=(e,t)=>{for(const a of e)if(t(a))return a},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(e||(e={})),function(e){e.mergeShapes=(e,t)=>({...e,...t})}(t||(t={}));const a=e.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),n=e=>{switch(typeof e){case"undefined":return a.undefined;case"string":return a.string;case"number":return Number.isNaN(e)?a.nan:a.number;case"boolean":return a.boolean;case"function":return a.function;case"bigint":return a.bigint;case"symbol":return a.symbol;case"object":return Array.isArray(e)?a.array:null===e?a.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?a.promise:"undefined"!=typeof Map&&e instanceof Map?a.map:"undefined"!=typeof Set&&e instanceof Set?a.set:"undefined"!=typeof Date&&e instanceof Date?a.date:a.object;default:return a.unknown}},s=e.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class r extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(e){return e.message},a={_errors:[]},n=e=>{for(const s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(n);else if("invalid_return_type"===s.code)n(s.returnTypeError);else if("invalid_arguments"===s.code)n(s.argumentsError);else if(0===s.path.length)a._errors.push(t(s));else{let e=a,n=0;for(;n<s.path.length;){const a=s.path[n];n===s.path.length-1?(e[a]=e[a]||{_errors:[]},e[a]._errors.push(t(s))):e[a]=e[a]||{_errors:[]},e=e[a],n++}}};return n(this),a}static assert(e){if(!(e instanceof r))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,e.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},a=[];for(const n of this.issues)if(n.path.length>0){const a=n.path[0];t[a]=t[a]||[],t[a].push(e(n))}else a.push(e(n));return{formErrors:a,fieldErrors:t}}get formErrors(){return this.flatten()}}r.create=e=>new r(e);const i=(t,n)=>{let r;switch(t.code){case s.invalid_type:r=t.received===a.undefined?"Required":`Expected ${t.expected}, received ${t.received}`;break;case s.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(t.expected,e.jsonStringifyReplacer)}`;break;case s.unrecognized_keys:r=`Unrecognized key(s) in object: ${e.joinValues(t.keys,", ")}`;break;case s.invalid_union:r="Invalid input";break;case s.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${e.joinValues(t.options)}`;break;case s.invalid_enum_value:r=`Invalid enum value. Expected ${e.joinValues(t.options)}, received '${t.received}'`;break;case s.invalid_arguments:r="Invalid function arguments";break;case s.invalid_return_type:r="Invalid function return type";break;case s.invalid_date:r="Invalid date";break;case s.invalid_string:"object"==typeof t.validation?"includes"in t.validation?(r=`Invalid input: must include "${t.validation.includes}"`,"number"==typeof t.validation.position&&(r=`${r} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?r=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?r=`Invalid input: must end with "${t.validation.endsWith}"`:e.assertNever(t.validation):r="regex"!==t.validation?`Invalid ${t.validation}`:"Invalid";break;case s.too_small:r="array"===t.type?`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:"string"===t.type?`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:"number"===t.type||"bigint"===t.type?`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:"date"===t.type?`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:"Invalid input";break;case s.too_big:r="array"===t.type?`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:"string"===t.type?`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:"number"===t.type?`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:"bigint"===t.type?`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:"date"===t.type?`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:"Invalid input";break;case s.custom:r="Invalid input";break;case s.invalid_intersection_types:r="Intersection results could not be merged";break;case s.not_multiple_of:r=`Number must be a multiple of ${t.multipleOf}`;break;case s.not_finite:r="Number must be finite";break;default:r=n.defaultError,e.assertNever(t)}return{message:r}};let o=i;function d(){return o}var c;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(c||(c={}));const u=e=>{const{data:t,path:a,errorMaps:n,issueData:s}=e,r=[...a,...s.path||[]],i={...s,path:r};if(void 0!==s.message)return{...s,path:r,message:s.message};let o="";const d=n.filter(e=>!!e).slice().reverse();for(const e of d)o=e(i,{data:t,defaultError:o}).message;return{...s,path:r,message:o}};function l(e,t){const a=d(),n=u({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,a,a===i?void 0:i].filter(e=>!!e)});e.common.issues.push(n)}class p{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const a=[];for(const n of t){if("aborted"===n.status)return h;"dirty"===n.status&&e.dirty(),a.push(n.value)}return{status:e.value,value:a}}static async mergeObjectAsync(e,t){const a=[];for(const e of t){const t=await e.key,n=await e.value;a.push({key:t,value:n})}return p.mergeObjectSync(e,a)}static mergeObjectSync(e,t){const a={};for(const n of t){const{key:t,value:s}=n;if("aborted"===t.status)return h;if("aborted"===s.status)return h;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"===t.value||void 0===s.value&&!n.alwaysSet||(a[t.value]=s.value)}return{status:e.value,value:a}}}const h=Object.freeze({status:"aborted"}),m=e=>({status:"dirty",value:e}),f=e=>({status:"valid",value:e}),_=e=>"aborted"===e.status,y=e=>"dirty"===e.status,g=e=>"valid"===e.status,v=e=>"undefined"!=typeof Promise&&e instanceof Promise;class b{constructor(e,t,a,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=a,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const x=(e,t)=>{if(g(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new r(e.common.issues);return this._error=t,this._error}}};function k(e){if(!e)return{};const{errorMap:t,invalid_type_error:a,required_error:n,description:s}=e;if(t&&(a||n))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:s};return{errorMap:(t,s)=>{const{message:r}=e;return"invalid_enum_value"===t.code?{message:r??s.defaultError}:void 0===s.data?{message:r??n??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:r??a??s.defaultError}},description:s}}class T{get description(){return this._def.description}_getType(e){return n(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:n(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new p,ctx:{common:e.parent.common,data:e.data,parsedType:n(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(v(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const a=this.safeParse(e,t);if(a.success)return a.data;throw a.error}safeParse(e,t){const a={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:n(e)},s=this._parseSync({data:e,path:a.path,parent:a});return x(a,s)}"~validate"(e){const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:n(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:t});return g(a)?{value:a.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>g(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){const a=await this.safeParseAsync(e,t);if(a.success)return a.data;throw a.error}async safeParseAsync(e,t){const a={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:n(e)},s=this._parse({data:e,path:a.path,parent:a}),r=await(v(s)?s:Promise.resolve(s));return x(a,r)}refine(e,t){const a=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,n)=>{const r=e(t),i=()=>n.addIssue({code:s.custom,...a(t)});return"undefined"!=typeof Promise&&r instanceof Promise?r.then(e=>!!e||(i(),!1)):!!r||(i(),!1)})}refinement(e,t){return this._refinement((a,n)=>!!e(a)||(n.addIssue("function"==typeof t?t(a,n):t),!1))}_refinement(e){return new Te({schema:this,typeName:Ze.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return Ee.create(this,this._def)}nullable(){return we.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ie.create(this)}promise(){return ke.create(this,this._def)}or(e){return ce.create([this,e],this._def)}and(e){return he.create(this,e,this._def)}transform(e){return new Te({...k(this._def),schema:this,typeName:Ze.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new Oe({...k(this._def),innerType:this,defaultValue:t,typeName:Ze.ZodDefault})}brand(){return new Ae({typeName:Ze.ZodBranded,type:this,...k(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new Ce({...k(this._def),innerType:this,catchValue:t,typeName:Ze.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return Se.create(this,e)}readonly(){return Ie.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const E=/^c[^\s-]{8,}$/i,w=/^[0-9a-z]+$/,O=/^[0-9A-HJKMNP-TV-Z]{26}$/i,C=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,N=/^[a-z0-9_-]{21}$/i,A=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,S=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,I=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Z;const R=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,j=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,L=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,P=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,$=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,M=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,D="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",U=new RegExp(`^${D}$`);function F(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${e.precision?"+":"?"}`}function B(e){return new RegExp(`^${F(e)}$`)}function K(e){let t=`${D}T${F(e)}`;const a=[];return a.push(e.local?"Z?":"Z"),e.offset&&a.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${a.join("|")})`,new RegExp(`^${t}$`)}function z(e,t){return!("v4"!==t&&t||!R.test(e))||!("v6"!==t&&t||!L.test(e))}function V(e,t){if(!A.test(e))return!1;try{const[a]=e.split(".");if(!a)return!1;const n=a.replace(/-/g,"+").replace(/_/g,"/").padEnd(a.length+(4-a.length%4)%4,"="),s=JSON.parse(atob(n));return"object"==typeof s&&null!==s&&((!("typ"in s)||"JWT"===s?.typ)&&(!!s.alg&&(!t||s.alg===t)))}catch{return!1}}function W(e,t){return!("v4"!==t&&t||!j.test(e))||!("v6"!==t&&t||!P.test(e))}class G extends T{_parse(t){this._def.coerce&&(t.data=String(t.data));if(this._getType(t)!==a.string){const e=this._getOrReturnCtx(t);return l(e,{code:s.invalid_type,expected:a.string,received:e.parsedType}),h}const n=new p;let r;for(const a of this._def.checks)if("min"===a.kind)t.data.length<a.value&&(r=this._getOrReturnCtx(t,r),l(r,{code:s.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),n.dirty());else if("max"===a.kind)t.data.length>a.value&&(r=this._getOrReturnCtx(t,r),l(r,{code:s.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),n.dirty());else if("length"===a.kind){const e=t.data.length>a.value,i=t.data.length<a.value;(e||i)&&(r=this._getOrReturnCtx(t,r),e?l(r,{code:s.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):i&&l(r,{code:s.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),n.dirty())}else if("email"===a.kind)I.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"email",code:s.invalid_string,message:a.message}),n.dirty());else if("emoji"===a.kind)Z||(Z=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Z.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"emoji",code:s.invalid_string,message:a.message}),n.dirty());else if("uuid"===a.kind)C.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"uuid",code:s.invalid_string,message:a.message}),n.dirty());else if("nanoid"===a.kind)N.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"nanoid",code:s.invalid_string,message:a.message}),n.dirty());else if("cuid"===a.kind)E.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"cuid",code:s.invalid_string,message:a.message}),n.dirty());else if("cuid2"===a.kind)w.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"cuid2",code:s.invalid_string,message:a.message}),n.dirty());else if("ulid"===a.kind)O.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"ulid",code:s.invalid_string,message:a.message}),n.dirty());else if("url"===a.kind)try{new URL(t.data)}catch{r=this._getOrReturnCtx(t,r),l(r,{validation:"url",code:s.invalid_string,message:a.message}),n.dirty()}else if("regex"===a.kind){a.regex.lastIndex=0;a.regex.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"regex",code:s.invalid_string,message:a.message}),n.dirty())}else if("trim"===a.kind)t.data=t.data.trim();else if("includes"===a.kind)t.data.includes(a.value,a.position)||(r=this._getOrReturnCtx(t,r),l(r,{code:s.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),n.dirty());else if("toLowerCase"===a.kind)t.data=t.data.toLowerCase();else if("toUpperCase"===a.kind)t.data=t.data.toUpperCase();else if("startsWith"===a.kind)t.data.startsWith(a.value)||(r=this._getOrReturnCtx(t,r),l(r,{code:s.invalid_string,validation:{startsWith:a.value},message:a.message}),n.dirty());else if("endsWith"===a.kind)t.data.endsWith(a.value)||(r=this._getOrReturnCtx(t,r),l(r,{code:s.invalid_string,validation:{endsWith:a.value},message:a.message}),n.dirty());else if("datetime"===a.kind){K(a).test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{code:s.invalid_string,validation:"datetime",message:a.message}),n.dirty())}else if("date"===a.kind){U.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{code:s.invalid_string,validation:"date",message:a.message}),n.dirty())}else if("time"===a.kind){B(a).test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{code:s.invalid_string,validation:"time",message:a.message}),n.dirty())}else"duration"===a.kind?S.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"duration",code:s.invalid_string,message:a.message}),n.dirty()):"ip"===a.kind?z(t.data,a.version)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"ip",code:s.invalid_string,message:a.message}),n.dirty()):"jwt"===a.kind?V(t.data,a.alg)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"jwt",code:s.invalid_string,message:a.message}),n.dirty()):"cidr"===a.kind?W(t.data,a.version)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"cidr",code:s.invalid_string,message:a.message}),n.dirty()):"base64"===a.kind?$.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"base64",code:s.invalid_string,message:a.message}),n.dirty()):"base64url"===a.kind?M.test(t.data)||(r=this._getOrReturnCtx(t,r),l(r,{validation:"base64url",code:s.invalid_string,message:a.message}),n.dirty()):e.assertNever(a);return{status:n.value,value:t.data}}_regex(e,t,a){return this.refinement(t=>e.test(t),{validation:t,code:s.invalid_string,...c.errToObj(a)})}_addCheck(e){return new G({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...c.errToObj(e)})}url(e){return this._addCheck({kind:"url",...c.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...c.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...c.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...c.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...c.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...c.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...c.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...c.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...c.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...c.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...c.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...c.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...c.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...c.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...c.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...c.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...c.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...c.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...c.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...c.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...c.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...c.errToObj(t)})}nonempty(e){return this.min(1,c.errToObj(e))}trim(){return new G({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new G({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new G({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function H(e,t){const a=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,s=a>n?a:n;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}G.create=e=>new G({checks:[],typeName:Ze.ZodString,coerce:e?.coerce??!1,...k(e)});class q extends T{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){this._def.coerce&&(t.data=Number(t.data));if(this._getType(t)!==a.number){const e=this._getOrReturnCtx(t);return l(e,{code:s.invalid_type,expected:a.number,received:e.parsedType}),h}let n;const r=new p;for(const a of this._def.checks)if("int"===a.kind)e.isInteger(t.data)||(n=this._getOrReturnCtx(t,n),l(n,{code:s.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty());else if("min"===a.kind){(a.inclusive?t.data<a.value:t.data<=a.value)&&(n=this._getOrReturnCtx(t,n),l(n,{code:s.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty())}else if("max"===a.kind){(a.inclusive?t.data>a.value:t.data>=a.value)&&(n=this._getOrReturnCtx(t,n),l(n,{code:s.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty())}else"multipleOf"===a.kind?0!==H(t.data,a.value)&&(n=this._getOrReturnCtx(t,n),l(n,{code:s.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):"finite"===a.kind?Number.isFinite(t.data)||(n=this._getOrReturnCtx(t,n),l(n,{code:s.not_finite,message:a.message}),r.dirty()):e.assertNever(a);return{status:r.value,value:t.data}}gte(e,t){return this.setLimit("min",e,!0,c.toString(t))}gt(e,t){return this.setLimit("min",e,!1,c.toString(t))}lte(e,t){return this.setLimit("max",e,!0,c.toString(t))}lt(e,t){return this.setLimit("max",e,!1,c.toString(t))}setLimit(e,t,a,n){return new q({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:c.toString(n)}]})}_addCheck(e){return new q({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:c.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:c.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:c.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:c.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:c.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:c.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:c.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:c.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:c.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(t=>"int"===t.kind||"multipleOf"===t.kind&&e.isInteger(t.value))}get isFinite(){let e=null,t=null;for(const a of this._def.checks){if("finite"===a.kind||"int"===a.kind||"multipleOf"===a.kind)return!0;"min"===a.kind?(null===t||a.value>t)&&(t=a.value):"max"===a.kind&&(null===e||a.value<e)&&(e=a.value)}return Number.isFinite(t)&&Number.isFinite(e)}}q.create=e=>new q({checks:[],typeName:Ze.ZodNumber,coerce:e?.coerce||!1,...k(e)});class Y extends T{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==a.bigint)return this._getInvalidInput(t);let n;const r=new p;for(const a of this._def.checks)if("min"===a.kind){(a.inclusive?t.data<a.value:t.data<=a.value)&&(n=this._getOrReturnCtx(t,n),l(n,{code:s.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty())}else if("max"===a.kind){(a.inclusive?t.data>a.value:t.data>=a.value)&&(n=this._getOrReturnCtx(t,n),l(n,{code:s.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty())}else"multipleOf"===a.kind?t.data%a.value!==BigInt(0)&&(n=this._getOrReturnCtx(t,n),l(n,{code:s.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):e.assertNever(a);return{status:r.value,value:t.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return l(t,{code:s.invalid_type,expected:a.bigint,received:t.parsedType}),h}gte(e,t){return this.setLimit("min",e,!0,c.toString(t))}gt(e,t){return this.setLimit("min",e,!1,c.toString(t))}lte(e,t){return this.setLimit("max",e,!0,c.toString(t))}lt(e,t){return this.setLimit("max",e,!1,c.toString(t))}setLimit(e,t,a,n){return new Y({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:a,message:c.toString(n)}]})}_addCheck(e){return new Y({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:c.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:c.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:c.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:c.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:c.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}Y.create=e=>new Y({checks:[],typeName:Ze.ZodBigInt,coerce:e?.coerce??!1,...k(e)});class X extends T{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==a.boolean){const t=this._getOrReturnCtx(e);return l(t,{code:s.invalid_type,expected:a.boolean,received:t.parsedType}),h}return f(e.data)}}X.create=e=>new X({typeName:Ze.ZodBoolean,coerce:e?.coerce||!1,...k(e)});class J extends T{_parse(t){this._def.coerce&&(t.data=new Date(t.data));if(this._getType(t)!==a.date){const e=this._getOrReturnCtx(t);return l(e,{code:s.invalid_type,expected:a.date,received:e.parsedType}),h}if(Number.isNaN(t.data.getTime())){return l(this._getOrReturnCtx(t),{code:s.invalid_date}),h}const n=new p;let r;for(const a of this._def.checks)"min"===a.kind?t.data.getTime()<a.value&&(r=this._getOrReturnCtx(t,r),l(r,{code:s.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),n.dirty()):"max"===a.kind?t.data.getTime()>a.value&&(r=this._getOrReturnCtx(t,r),l(r,{code:s.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),n.dirty()):e.assertNever(a);return{status:n.value,value:new Date(t.data.getTime())}}_addCheck(e){return new J({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:c.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:c.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}J.create=e=>new J({checks:[],coerce:e?.coerce||!1,typeName:Ze.ZodDate,...k(e)});class Q extends T{_parse(e){if(this._getType(e)!==a.symbol){const t=this._getOrReturnCtx(e);return l(t,{code:s.invalid_type,expected:a.symbol,received:t.parsedType}),h}return f(e.data)}}Q.create=e=>new Q({typeName:Ze.ZodSymbol,...k(e)});class ee extends T{_parse(e){if(this._getType(e)!==a.undefined){const t=this._getOrReturnCtx(e);return l(t,{code:s.invalid_type,expected:a.undefined,received:t.parsedType}),h}return f(e.data)}}ee.create=e=>new ee({typeName:Ze.ZodUndefined,...k(e)});class te extends T{_parse(e){if(this._getType(e)!==a.null){const t=this._getOrReturnCtx(e);return l(t,{code:s.invalid_type,expected:a.null,received:t.parsedType}),h}return f(e.data)}}te.create=e=>new te({typeName:Ze.ZodNull,...k(e)});class ae extends T{constructor(){super(...arguments),this._any=!0}_parse(e){return f(e.data)}}ae.create=e=>new ae({typeName:Ze.ZodAny,...k(e)});class ne extends T{constructor(){super(...arguments),this._unknown=!0}_parse(e){return f(e.data)}}ne.create=e=>new ne({typeName:Ze.ZodUnknown,...k(e)});class se extends T{_parse(e){const t=this._getOrReturnCtx(e);return l(t,{code:s.invalid_type,expected:a.never,received:t.parsedType}),h}}se.create=e=>new se({typeName:Ze.ZodNever,...k(e)});class re extends T{_parse(e){if(this._getType(e)!==a.undefined){const t=this._getOrReturnCtx(e);return l(t,{code:s.invalid_type,expected:a.void,received:t.parsedType}),h}return f(e.data)}}re.create=e=>new re({typeName:Ze.ZodVoid,...k(e)});class ie extends T{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==a.array)return l(t,{code:s.invalid_type,expected:a.array,received:t.parsedType}),h;if(null!==r.exactLength){const e=t.data.length>r.exactLength.value,a=t.data.length<r.exactLength.value;(e||a)&&(l(t,{code:e?s.too_big:s.too_small,minimum:a?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(l(t,{code:s.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(l(t,{code:s.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((e,a)=>r.type._parseAsync(new b(t,e,t.path,a)))).then(e=>p.mergeArray(n,e));const i=[...t.data].map((e,a)=>r.type._parseSync(new b(t,e,t.path,a)));return p.mergeArray(n,i)}get element(){return this._def.type}min(e,t){return new ie({...this._def,minLength:{value:e,message:c.toString(t)}})}max(e,t){return new ie({...this._def,maxLength:{value:e,message:c.toString(t)}})}length(e,t){return new ie({...this._def,exactLength:{value:e,message:c.toString(t)}})}nonempty(e){return this.min(1,e)}}function oe(e){if(e instanceof de){const t={};for(const a in e.shape){const n=e.shape[a];t[a]=Ee.create(oe(n))}return new de({...e._def,shape:()=>t})}return e instanceof ie?new ie({...e._def,type:oe(e.element)}):e instanceof Ee?Ee.create(oe(e.unwrap())):e instanceof we?we.create(oe(e.unwrap())):e instanceof me?me.create(e.items.map(e=>oe(e))):e}ie.create=(e,t)=>new ie({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Ze.ZodArray,...k(t)});class de extends T{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const t=this._def.shape(),a=e.objectKeys(t);return this._cached={shape:t,keys:a},this._cached}_parse(e){if(this._getType(e)!==a.object){const t=this._getOrReturnCtx(e);return l(t,{code:s.invalid_type,expected:a.object,received:t.parsedType}),h}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof se&&"strip"===this._def.unknownKeys))for(const e in n.data)i.includes(e)||o.push(e);const d=[];for(const e of i){const t=r[e],a=n.data[e];d.push({key:{status:"valid",value:e},value:t._parse(new b(n,a,n.path,e)),alwaysSet:e in n.data})}if(this._def.catchall instanceof se){const e=this._def.unknownKeys;if("passthrough"===e)for(const e of o)d.push({key:{status:"valid",value:e},value:{status:"valid",value:n.data[e]}});else if("strict"===e)o.length>0&&(l(n,{code:s.unrecognized_keys,keys:o}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of o){const a=n.data[t];d.push({key:{status:"valid",value:t},value:e._parse(new b(n,a,n.path,t)),alwaysSet:t in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const e=[];for(const t of d){const a=await t.key,n=await t.value;e.push({key:a,value:n,alwaysSet:t.alwaysSet})}return e}).then(e=>p.mergeObjectSync(t,e)):p.mergeObjectSync(t,d)}get shape(){return this._def.shape()}strict(e){return c.errToObj,new de({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,a)=>{const n=this._def.errorMap?.(t,a).message??a.defaultError;return"unrecognized_keys"===t.code?{message:c.errToObj(e).message??n}:{message:n}}}:{}})}strip(){return new de({...this._def,unknownKeys:"strip"})}passthrough(){return new de({...this._def,unknownKeys:"passthrough"})}extend(e){return new de({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new de({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Ze.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new de({...this._def,catchall:e})}pick(t){const a={};for(const n of e.objectKeys(t))t[n]&&this.shape[n]&&(a[n]=this.shape[n]);return new de({...this._def,shape:()=>a})}omit(t){const a={};for(const n of e.objectKeys(this.shape))t[n]||(a[n]=this.shape[n]);return new de({...this._def,shape:()=>a})}deepPartial(){return oe(this)}partial(t){const a={};for(const n of e.objectKeys(this.shape)){const e=this.shape[n];t&&!t[n]?a[n]=e:a[n]=e.optional()}return new de({...this._def,shape:()=>a})}required(t){const a={};for(const n of e.objectKeys(this.shape))if(t&&!t[n])a[n]=this.shape[n];else{let e=this.shape[n];for(;e instanceof Ee;)e=e._def.innerType;a[n]=e}return new de({...this._def,shape:()=>a})}keyof(){return ve(e.objectKeys(this.shape))}}de.create=(e,t)=>new de({shape:()=>e,unknownKeys:"strip",catchall:se.create(),typeName:Ze.ZodObject,...k(t)}),de.strictCreate=(e,t)=>new de({shape:()=>e,unknownKeys:"strict",catchall:se.create(),typeName:Ze.ZodObject,...k(t)}),de.lazycreate=(e,t)=>new de({shape:e,unknownKeys:"strip",catchall:se.create(),typeName:Ze.ZodObject,...k(t)});class ce extends T{_parse(e){const{ctx:t}=this._processInputParams(e),a=this._def.options;if(t.common.async)return Promise.all(a.map(async e=>{const a={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;const a=e.map(e=>new r(e.ctx.common.issues));return l(t,{code:s.invalid_union,unionErrors:a}),h});{let e;const n=[];for(const s of a){const a={...t,common:{...t.common,issues:[]},parent:null},r=s._parseSync({data:t.data,path:t.path,parent:a});if("valid"===r.status)return r;"dirty"!==r.status||e||(e={result:r,ctx:a}),a.common.issues.length&&n.push(a.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const i=n.map(e=>new r(e));return l(t,{code:s.invalid_union,unionErrors:i}),h}}get options(){return this._def.options}}ce.create=(e,t)=>new ce({options:e,typeName:Ze.ZodUnion,...k(t)});const ue=t=>t instanceof ye?ue(t.schema):t instanceof Te?ue(t.innerType()):t instanceof ge?[t.value]:t instanceof be?t.options:t instanceof xe?e.objectValues(t.enum):t instanceof Oe?ue(t._def.innerType):t instanceof ee?[void 0]:t instanceof te?[null]:t instanceof Ee?[void 0,...ue(t.unwrap())]:t instanceof we?[null,...ue(t.unwrap())]:t instanceof Ae||t instanceof Ie?ue(t.unwrap()):t instanceof Ce?ue(t._def.innerType):[];class le extends T{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==a.object)return l(t,{code:s.invalid_type,expected:a.object,received:t.parsedType}),h;const n=this.discriminator,r=t.data[n],i=this.optionsMap.get(r);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(l(t,{code:s.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),h)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,a){const n=new Map;for(const a of t){const t=ue(a.shape[e]);if(!t.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const s of t){if(n.has(s))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);n.set(s,a)}}return new le({typeName:Ze.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...k(a)})}}function pe(t,s){const r=n(t),i=n(s);if(t===s)return{valid:!0,data:t};if(r===a.object&&i===a.object){const a=e.objectKeys(s),n=e.objectKeys(t).filter(e=>-1!==a.indexOf(e)),r={...t,...s};for(const e of n){const a=pe(t[e],s[e]);if(!a.valid)return{valid:!1};r[e]=a.data}return{valid:!0,data:r}}if(r===a.array&&i===a.array){if(t.length!==s.length)return{valid:!1};const e=[];for(let a=0;a<t.length;a++){const n=pe(t[a],s[a]);if(!n.valid)return{valid:!1};e.push(n.data)}return{valid:!0,data:e}}return r===a.date&&i===a.date&&+t===+s?{valid:!0,data:t}:{valid:!1}}class he extends T{_parse(e){const{status:t,ctx:a}=this._processInputParams(e),n=(e,n)=>{if(_(e)||_(n))return h;const r=pe(e.value,n.value);return r.valid?((y(e)||y(n))&&t.dirty(),{status:t.value,value:r.data}):(l(a,{code:s.invalid_intersection_types}),h)};return a.common.async?Promise.all([this._def.left._parseAsync({data:a.data,path:a.path,parent:a}),this._def.right._parseAsync({data:a.data,path:a.path,parent:a})]).then(([e,t])=>n(e,t)):n(this._def.left._parseSync({data:a.data,path:a.path,parent:a}),this._def.right._parseSync({data:a.data,path:a.path,parent:a}))}}he.create=(e,t,a)=>new he({left:e,right:t,typeName:Ze.ZodIntersection,...k(a)});class me extends T{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==a.array)return l(n,{code:s.invalid_type,expected:a.array,received:n.parsedType}),h;if(n.data.length<this._def.items.length)return l(n,{code:s.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),h;!this._def.rest&&n.data.length>this._def.items.length&&(l(n,{code:s.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((e,t)=>{const a=this._def.items[t]||this._def.rest;return a?a._parse(new b(n,e,n.path,t)):null}).filter(e=>!!e);return n.common.async?Promise.all(r).then(e=>p.mergeArray(t,e)):p.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new me({...this._def,rest:e})}}me.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new me({items:e,typeName:Ze.ZodTuple,rest:null,...k(t)})};class fe extends T{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==a.map)return l(n,{code:s.invalid_type,expected:a.map,received:n.parsedType}),h;const r=this._def.keyType,i=this._def.valueType,o=[...n.data.entries()].map(([e,t],a)=>({key:r._parse(new b(n,e,n.path,[a,"key"])),value:i._parse(new b(n,t,n.path,[a,"value"]))}));if(n.common.async){const e=new Map;return Promise.resolve().then(async()=>{for(const a of o){const n=await a.key,s=await a.value;if("aborted"===n.status||"aborted"===s.status)return h;"dirty"!==n.status&&"dirty"!==s.status||t.dirty(),e.set(n.value,s.value)}return{status:t.value,value:e}})}{const e=new Map;for(const a of o){const n=a.key,s=a.value;if("aborted"===n.status||"aborted"===s.status)return h;"dirty"!==n.status&&"dirty"!==s.status||t.dirty(),e.set(n.value,s.value)}return{status:t.value,value:e}}}}fe.create=(e,t,a)=>new fe({valueType:t,keyType:e,typeName:Ze.ZodMap,...k(a)});class _e extends T{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==a.set)return l(n,{code:s.invalid_type,expected:a.set,received:n.parsedType}),h;const r=this._def;null!==r.minSize&&n.data.size<r.minSize.value&&(l(n,{code:s.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&n.data.size>r.maxSize.value&&(l(n,{code:s.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const i=this._def.valueType;function o(e){const a=new Set;for(const n of e){if("aborted"===n.status)return h;"dirty"===n.status&&t.dirty(),a.add(n.value)}return{status:t.value,value:a}}const d=[...n.data.values()].map((e,t)=>i._parse(new b(n,e,n.path,t)));return n.common.async?Promise.all(d).then(e=>o(e)):o(d)}min(e,t){return new _e({...this._def,minSize:{value:e,message:c.toString(t)}})}max(e,t){return new _e({...this._def,maxSize:{value:e,message:c.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}_e.create=(e,t)=>new _e({valueType:e,minSize:null,maxSize:null,typeName:Ze.ZodSet,...k(t)});class ye extends T{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ye.create=(e,t)=>new ye({getter:e,typeName:Ze.ZodLazy,...k(t)});class ge extends T{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return l(t,{received:t.data,code:s.invalid_literal,expected:this._def.value}),h}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ve(e,t){return new be({values:e,typeName:Ze.ZodEnum,...k(t)})}ge.create=(e,t)=>new ge({value:e,typeName:Ze.ZodLiteral,...k(t)});class be extends T{_parse(t){if("string"!=typeof t.data){const a=this._getOrReturnCtx(t),n=this._def.values;return l(a,{expected:e.joinValues(n),received:a.parsedType,code:s.invalid_type}),h}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(t.data)){const e=this._getOrReturnCtx(t),a=this._def.values;return l(e,{received:e.data,code:s.invalid_enum_value,options:a}),h}return f(t.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return be.create(e,{...this._def,...t})}exclude(e,t=this._def){return be.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}be.create=ve;class xe extends T{_parse(t){const n=e.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==a.string&&r.parsedType!==a.number){const t=e.objectValues(n);return l(r,{expected:e.joinValues(t),received:r.parsedType,code:s.invalid_type}),h}if(this._cache||(this._cache=new Set(e.getValidEnumValues(this._def.values))),!this._cache.has(t.data)){const t=e.objectValues(n);return l(r,{received:r.data,code:s.invalid_enum_value,options:t}),h}return f(t.data)}get enum(){return this._def.values}}xe.create=(e,t)=>new xe({values:e,typeName:Ze.ZodNativeEnum,...k(t)});class ke extends T{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==a.promise&&!1===t.common.async)return l(t,{code:s.invalid_type,expected:a.promise,received:t.parsedType}),h;const n=t.parsedType===a.promise?t.data:Promise.resolve(t.data);return f(n.then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}ke.create=(e,t)=>new ke({type:e,typeName:Ze.ZodPromise,...k(t)});class Te extends T{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Ze.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:a,ctx:n}=this._processInputParams(t),s=this._def.effect||null,r={addIssue:e=>{l(n,e),e.fatal?a.abort():a.dirty()},get path(){return n.path}};if(r.addIssue=r.addIssue.bind(r),"preprocess"===s.type){const e=s.transform(n.data,r);if(n.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===a.value)return h;const t=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return"aborted"===t.status?h:"dirty"===t.status||"dirty"===a.value?m(t.value):t});{if("aborted"===a.value)return h;const t=this._def.schema._parseSync({data:e,path:n.path,parent:n});return"aborted"===t.status?h:"dirty"===t.status||"dirty"===a.value?m(t.value):t}}if("refinement"===s.type){const e=e=>{const t=s.refinement(e,r);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===n.common.async){const t=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===t.status?h:("dirty"===t.status&&a.dirty(),e(t.value),{status:a.value,value:t.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(t=>"aborted"===t.status?h:("dirty"===t.status&&a.dirty(),e(t.value).then(()=>({status:a.value,value:t.value}))))}if("transform"===s.type){if(!1===n.common.async){const e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!g(e))return h;const t=s.transform(e.value,r);if(t instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:a.value,value:t}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(e=>g(e)?Promise.resolve(s.transform(e.value,r)).then(e=>({status:a.value,value:e})):h)}e.assertNever(s)}}Te.create=(e,t,a)=>new Te({schema:e,typeName:Ze.ZodEffects,effect:t,...k(a)}),Te.createWithPreprocess=(e,t,a)=>new Te({schema:t,effect:{type:"preprocess",transform:e},typeName:Ze.ZodEffects,...k(a)});class Ee extends T{_parse(e){return this._getType(e)===a.undefined?f(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ee.create=(e,t)=>new Ee({innerType:e,typeName:Ze.ZodOptional,...k(t)});class we extends T{_parse(e){return this._getType(e)===a.null?f(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}we.create=(e,t)=>new we({innerType:e,typeName:Ze.ZodNullable,...k(t)});class Oe extends T{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===a.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Oe.create=(e,t)=>new Oe({innerType:e,typeName:Ze.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...k(t)});class Ce extends T{_parse(e){const{ctx:t}=this._processInputParams(e),a={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:a.data,path:a.path,parent:{...a}});return v(n)?n.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new r(a.common.issues)},input:a.data})})):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new r(a.common.issues)},input:a.data})}}removeCatch(){return this._def.innerType}}Ce.create=(e,t)=>new Ce({innerType:e,typeName:Ze.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...k(t)});class Ne extends T{_parse(e){if(this._getType(e)!==a.nan){const t=this._getOrReturnCtx(e);return l(t,{code:s.invalid_type,expected:a.nan,received:t.parsedType}),h}return{status:"valid",value:e.data}}}Ne.create=e=>new Ne({typeName:Ze.ZodNaN,...k(e)});Symbol("zod_brand");class Ae extends T{_parse(e){const{ctx:t}=this._processInputParams(e),a=t.data;return this._def.type._parse({data:a,path:t.path,parent:t})}unwrap(){return this._def.type}}class Se extends T{_parse(e){const{status:t,ctx:a}=this._processInputParams(e);if(a.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?h:"dirty"===e.status?(t.dirty(),m(e.value)):this._def.out._parseAsync({data:e.value,path:a.path,parent:a})})()}{const e=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?h:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:a.path,parent:a})}}static create(e,t){return new Se({in:e,out:t,typeName:Ze.ZodPipeline})}}class Ie extends T{_parse(e){const t=this._def.innerType._parse(e),a=e=>(g(e)&&(e.value=Object.freeze(e.value)),e);return v(t)?t.then(e=>a(e)):a(t)}unwrap(){return this._def.innerType}}Ie.create=(e,t)=>new Ie({innerType:e,typeName:Ze.ZodReadonly,...k(t)});de.lazycreate;var Ze;!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(Ze||(Ze={}));const Re=G.create,je=q.create,Le=(Ne.create,Y.create,X.create),Pe=(J.create,Q.create,ee.create,te.create,ae.create),$e=ne.create,Me=(se.create,re.create,ie.create),De=de.create,Ue=(de.strictCreate,ce.create,le.create),Fe=(he.create,me.create,fe.create,_e.create,ye.create,ge.create),Be=be.create,Ke=xe.create;ke.create,Te.create,Ee.create,we.create,Te.createWithPreprocess,Se.create;var ze;!function(e){e.NAVIGATE="NAVIGATE",e.CLICK="CLICK",e.EXTRACT="EXTRACT",e.LOG="LOG",e.CONTENT_READY="CONTENT_READY",e.EXECUTE_WORKFLOW="EXECUTE_WORKFLOW",e.WORKFLOW_STATUS="WORKFLOW_STATUS",e.CONNECTION_STATUS="CONNECTION_STATUS",e.EXECUTE_QUERY="EXECUTE_QUERY",e.HEARTBEAT="HEARTBEAT",e.HEARTBEAT_ACK="HEARTBEAT_ACK",e.AGENT_STREAM_UPDATE="AGENT_STREAM_UPDATE",e.CANCEL_TASK="CANCEL_TASK",e.CLOSE_PANEL="CLOSE_PANEL",e.RESET_CONVERSATION="RESET_CONVERSATION",e.GET_TABS="GET_TABS",e.GET_TAB_HISTORY="GET_TAB_HISTORY",e.INTENT_PREDICTION_UPDATED="INTENT_PREDICTION_UPDATED",e.INTENT_BUBBLES_SHOW="INTENT_BUBBLES_SHOW",e.INTENT_BUBBLE_CLICKED="INTENT_BUBBLE_CLICKED"}(ze||(ze={}));const Ve=De({type:Ke(ze),payload:$e()});Ue("type",[Ve.extend({type:Fe(ze.NAVIGATE),payload:De({url:Re()})}),Ve.extend({type:Fe(ze.CLICK),payload:De({selector:Re()})}),Ve.extend({type:Fe(ze.LOG),payload:De({source:Re(),message:Re(),level:Be(["info","error","warning"]),timestamp:Re()})}),Ve.extend({type:Fe(ze.CONTENT_READY),payload:De({url:Re(),title:Re()})}),Ve.extend({type:Fe(ze.EXECUTE_WORKFLOW),payload:De({dsl:Re()})}),Ve.extend({type:Fe(ze.WORKFLOW_STATUS),payload:De({workflowId:Re(),steps:Me(De({id:Re(),status:Re(),message:Re().optional(),error:Re().optional()})),output:$e().optional()})}),Ve.extend({type:Fe(ze.CONNECTION_STATUS),payload:De({connected:Le(),port:Re().optional()})}),Ve.extend({type:Fe(ze.EXECUTE_QUERY),payload:De({query:Re(),tabIds:Me(je()).optional(),source:Re().optional()})}),Ve.extend({type:Fe(ze.HEARTBEAT),payload:De({timestamp:je()})}),Ve.extend({type:Fe(ze.HEARTBEAT_ACK),payload:De({timestamp:je()})}),Ve.extend({type:Fe(ze.AGENT_STREAM_UPDATE),payload:De({step:je(),action:Re(),status:Be(["thinking","executing","completed","error","debug"]),details:De({content:Re().optional(),toolName:Re().optional(),toolArgs:Pe().optional(),toolResult:Re().optional(),error:Re().optional(),messageType:Re().optional(),messageId:Re().optional(),segmentId:je().optional(),data:Pe().optional(),timestamp:Re().optional()})})}),Ve.extend({type:Fe(ze.CANCEL_TASK),payload:De({reason:Re().optional(),source:Re().optional()})}),Ve.extend({type:Fe(ze.CLOSE_PANEL),payload:De({reason:Re().optional()})}),Ve.extend({type:Fe(ze.RESET_CONVERSATION),payload:De({source:Re().optional()})}),Ve.extend({type:Fe(ze.GET_TABS),payload:De({currentWindowOnly:Le().default(!0)})}),Ve.extend({type:Fe(ze.GET_TAB_HISTORY),payload:De({tabId:je(),limit:je().optional().default(5)})}),Ve.extend({type:Fe(ze.INTENT_PREDICTION_UPDATED),payload:De({tabId:je(),url:Re(),intents:Me(Re()),confidence:je().optional(),timestamp:je(),error:Re().optional()})}),Ve.extend({type:Fe(ze.INTENT_BUBBLES_SHOW),payload:De({intents:Me(Re()),confidence:je().optional()})}),Ve.extend({type:Fe(ze.INTENT_BUBBLE_CLICKED),payload:De({intent:Re()})})]);var We;!function(e){e.OPTIONS_TO_BACKGROUND="options-to-background",e.SIDEPANEL_TO_BACKGROUND="sidepanel-to-background"}(We||(We={}));Ke(We),De({type:Ke(ze),payload:$e(),id:Re().optional()});De({DEV_MODE:Le(),MOCK_LLM_SETTINGS:Le(),VERSION:Re(),LOG_LEVEL:Be(["info","error","warning","debug"]).default("info")});const Ge={DEV_MODE:!1,MOCK_LLM_SETTINGS:!1,VERSION:"0.1.0",LOG_LEVEL:"info"};const He=Be(["info","error","warning"]);De({source:Re(),message:Re(),level:He,timestamp:Re()});class qe{static initialize(e={}){this.debugMode=e.debugMode||!1}static registerPort(e,t){this.connectedPorts.set(e,t)}static unregisterPort(e){this.connectedPorts.delete(e)}static log(e,t,a="info"){if(!this.debugMode&&"info"===a)return;const n={source:e,message:t,level:a,timestamp:(new Date).toISOString()};let s=!1;if(Ge.DEV_MODE){const e=this.connectedPorts.get(We.OPTIONS_TO_BACKGROUND);if(e)try{void 0!==e.name?(e.postMessage({type:ze.LOG,payload:n}),s=!0):this.unregisterPort(We.OPTIONS_TO_BACKGROUND)}catch(e){this.unregisterPort(We.OPTIONS_TO_BACKGROUND),"info"!==a||t.includes("heartbeat")}}!s&&"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.sendMessage({type:ze.LOG,payload:n}).catch(e=>{})}}qe.connectedPorts=new Map,qe.debugMode=!1;function Ye(e,t="info"){qe.log("Content",e,t)}function Xe(e,t,a){Ye(`Message received: ${JSON.stringify(e)}`);try{if("string"!=typeof e.type)throw new Error("Message type must be a string");const t=e.type,n=e.payload;switch(t){case ze.CLICK:if(!n||"object"!=typeof n||!("selector"in n)||"string"!=typeof n.selector)throw new Error("Invalid click payload: missing or invalid selector");return function(e,t){try{const{selector:a}=e;Ye(`Attempting to click element: ${a}`);const n=document.querySelector(a);if(!n){const e=`Element not found: ${a}`;return Ye(e,"error"),void t({error:e})}n.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout(()=>{try{if(n instanceof HTMLElement)n.click(),Ye(`Clicked element: ${a}`),t({success:!0});else{const e=`Element is not clickable: ${a}`;Ye(e,"error"),t({error:e})}}catch(e){const a=e instanceof Error?e.message:String(e);Ye(`Click operation failed: ${a}`,"error"),t({error:a})}},300)}catch(e){const a=e instanceof Error?e.message:String(e);Ye(`Click operation failed: ${a}`,"error"),t({error:a})}}(n,a),!1;case ze.EXTRACT:return Ye("Extraction functionality requested but not implemented in v0","warning"),a({error:"Extraction not implemented in v0"}),!1;case ze.INTENT_BUBBLES_SHOW:if(!(n&&"object"==typeof n&&"intents"in n&&Array.isArray(n.intents)))throw new Error("Invalid intent bubbles payload: missing or invalid intents array");return function(e){try{Ye(`Showing intent bubbles: ${JSON.stringify(e.intents)}`),et();const t=document.createElement("div");t.id="nxtscape-intent-bubbles-host",Qe=t.attachShadow({mode:"closed"}),Je=document.createElement("div"),Je.className="intent-bubbles-container";const a=document.createElement("style");a.textContent="\n      .intent-bubbles-container {\n        position: fixed;\n        bottom: 20px;\n        left: 50%;\n        transform: translateX(-50%);\n        z-index: 999999;\n        display: flex;\n        flex-direction: row;\n        gap: 12px;\n        justify-content: center;\n        align-items: center;\n        pointer-events: none;\n      }\n      \n      .intent-bubble {\n        background: rgba(30, 30, 30, 0.75);\n        backdrop-filter: blur(12px);\n        -webkit-backdrop-filter: blur(12px);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n        color: rgba(255, 255, 255, 0.95);\n        padding: 10px 20px;\n        border-radius: 24px;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        font-size: 14px;\n        font-weight: 500;\n        cursor: pointer;\n        pointer-events: auto;\n        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2), \n                    0 1px 2px rgba(0, 0, 0, 0.1),\n                    inset 0 0 0 1px rgba(255, 255, 255, 0.05);\n        transition: all 0.3s ease;\n        animation: slideUp 0.4s ease-out;\n        white-space: nowrap;\n        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);\n      }\n      \n      .intent-bubble:hover {\n        transform: translateY(-3px);\n        background: rgba(40, 40, 40, 0.85);\n        box-shadow: 0 6px 30px rgba(0, 0, 0, 0.25),\n                    0 2px 4px rgba(0, 0, 0, 0.1),\n                    inset 0 0 0 1px rgba(255, 255, 255, 0.1);\n        border-color: rgba(255, 255, 255, 0.15);\n      }\n      \n      @keyframes slideUp {\n        from {\n          opacity: 0;\n          transform: translateY(20px);\n        }\n        to {\n          opacity: 1;\n          transform: translateY(0);\n        }\n      }\n      \n      @supports not (backdrop-filter: blur(12px)) {\n        .intent-bubble {\n          background: rgba(30, 30, 30, 0.95);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n        }\n        \n        .intent-bubble:hover {\n          background: rgba(20, 20, 20, 0.98);\n        }\n      }\n    ",Qe.appendChild(a),Qe.appendChild(Je);e.intents.slice(0,3).forEach(e=>{const t=document.createElement("div");t.className="intent-bubble",t.textContent=e,t.addEventListener("click",()=>function(e){Ye(`Intent bubble clicked: ${e}`),chrome.runtime.sendMessage({type:ze.INTENT_BUBBLE_CLICKED,payload:{intent:e}}).catch(e=>{Ye(`Failed to send bubble click message: ${e.message}`,"error")}),et()}(e)),Je.appendChild(t)}),document.body.appendChild(t),setTimeout(()=>et(),3e4)}catch(e){Ye(`Failed to show intent bubbles: ${e instanceof Error?e.message:String(e)}`,"error")}}(n),a({success:!0}),!1;default:return Ye(`Unknown message type: ${t}`,"warning"),a({error:`Unknown message type: ${t}`}),!1}}catch(e){const t=e instanceof Error?e.message:String(e);return Ye(`Error handling message: ${t}`,"error"),a({error:t}),!1}}qe.initialize({debugMode:!0});let Je=null,Qe=null;function et(){const e=document.getElementById("nxtscape-intent-bubbles-host");e&&e.remove(),Je=null,Qe=null}window.addEventListener("beforeunload",()=>{et()}),function(){Ye("Content script initialized"),chrome.runtime.onMessage.addListener(Xe);try{chrome.runtime.sendMessage({type:ze.CONTENT_READY,payload:{url:window.location.href,title:document.title}}).catch(e=>{Ye(`Failed to send ready message: ${e.message}`,"error")})}catch(e){Ye(`Failed to send ready message: ${e instanceof Error?e.message:String(e)}`,"error")}}()})();