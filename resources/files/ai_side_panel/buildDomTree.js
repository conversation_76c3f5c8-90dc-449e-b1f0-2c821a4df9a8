window.buildDomTree=(e={showHighlightElements:!0,focusHighlightIndex:-1,viewportExpansion:0,debugMode:!1})=>{const{showHighlightElements:t,focusHighlightIndex:n,viewportExpansion:o,debugMode:i}=e;let r=0;const s={nodeProcessing:[],treeTraversal:[],highlighting:[],current:null};const c=i?{buildDomTreeCalls:0,timings:{buildDomTree:0,highlightElement:0,isInteractiveElement:0,isElementVisible:0,isTopElement:0,isInExpandedViewport:0,isTextNodeVisible:0,getEffectiveScroll:0},cacheMetrics:{boundingRectCacheHits:0,boundingRectCacheMisses:0,computedStyleCacheHits:0,computedStyleCacheMisses:0,getBoundingClientRectTime:0,getComputedStyleTime:0,boundingRectHitRate:0,computedStyleHitRate:0,overallHitRate:0,clientRectsCacheHits:0,clientRectsCacheMisses:0},nodeMetrics:{totalNodes:0,processedNodes:0,skippedNodes:0},buildDomTreeBreakdown:{totalTime:0,totalSelfTime:0,buildDomTreeCalls:0,domOperations:{getBoundingClientRect:0,getComputedStyle:0},domOperationCounts:{getBoundingClientRect:0,getComputedStyle:0}}}:null;function l(e){return i?function(...t){const n=performance.now(),o=e.apply(this,t);performance.now();return o}:e}function a(e,t){if(!i)return e();const n=performance.now(),o=e(),r=performance.now()-n;return c&&t in c.buildDomTreeBreakdown.domOperations&&(c.buildDomTreeBreakdown.domOperations[t]+=r,c.buildDomTreeBreakdown.domOperationCounts[t]++),o}const d={boundingRects:new WeakMap,clientRects:new WeakMap,computedStyles:new WeakMap,clearCache:()=>{d.boundingRects=new WeakMap,d.clientRects=new WeakMap,d.computedStyles=new WeakMap}};function u(e){if(!e)return null;if(d.boundingRects.has(e))return i&&c&&c.cacheMetrics.boundingRectCacheHits++,d.boundingRects.get(e);let t;if(i&&c&&c.cacheMetrics.boundingRectCacheMisses++,i){const n=performance.now();t=e.getBoundingClientRect();const o=performance.now()-n;c&&(c.buildDomTreeBreakdown.domOperations.getBoundingClientRect+=o,c.buildDomTreeBreakdown.domOperationCounts.getBoundingClientRect++)}else t=e.getBoundingClientRect();return t&&d.boundingRects.set(e,t),t}function h(e){if(!e)return null;if(d.computedStyles.has(e))return i&&c&&c.cacheMetrics.computedStyleCacheHits++,d.computedStyles.get(e);let t;if(i&&c&&c.cacheMetrics.computedStyleCacheMisses++,i){const n=performance.now();t=window.getComputedStyle(e);const o=performance.now()-n;c&&(c.buildDomTreeBreakdown.domOperations.getComputedStyle+=o,c.buildDomTreeBreakdown.domOperationCounts.getComputedStyle++)}else t=window.getComputedStyle(e);return t&&d.computedStyles.set(e,t),t}const p={},f={current:0},m="playwright-highlight-container",g=new WeakMap;new IntersectionObserver((e=>{e.forEach((e=>{elementVisibilityMap.set(e.target,e.isIntersecting)}))}),{rootMargin:`${o}px`});function w(e,n,o=null){var i;if(s[i="highlighting"]=s[i]||[],s[i].push(performance.now()),!e)return n;const r=[];let c=null,l=20,a=16,d=null;try{let i=document.getElementById(m);i?i.style.display=t?"block":"none":(i=document.createElement("div"),i.id=m,i.style.position="fixed",i.style.pointerEvents="none",i.style.top="0",i.style.left="0",i.style.width="100%",i.style.height="100%",i.style.zIndex="2147483640",i.style.backgroundColor="transparent",i.style.display=t?"block":"none",document.body.appendChild(i));const s=e.getClientRects();if(!s||0===s.length)return n;const u=["#FF0000","#00FF00","#0000FF","#FFA500","#800080","#008080","#FF69B4","#4B0082","#FF4500","#2E8B57","#DC143C","#4682B4"],h=u[n%u.length],p=h+"1A";let f={x:0,y:0};if(o){const e=o.getBoundingClientRect();f.x=e.left,f.y=e.top}const g=document.createDocumentFragment();for(const e of s){if(0===e.width||0===e.height)continue;const t=document.createElement("div");t.style.position="fixed",t.style.border=`2px solid ${h}`,t.style.backgroundColor=p,t.style.pointerEvents="none",t.style.boxSizing="border-box";const n=e.top+f.y,o=e.left+f.x;t.style.top=`${n}px`,t.style.left=`${o}px`,t.style.width=`${e.width}px`,t.style.height=`${e.height}px`,g.appendChild(t),r.push({element:t,initialRect:e})}const w=s[0];c=document.createElement("div"),c.className="playwright-highlight-label",c.style.position="fixed",c.style.background=h,c.style.color="white",c.style.padding="1px 4px",c.style.borderRadius="4px",c.style.fontSize=`${Math.min(12,Math.max(8,w.height/2))}px`,c.textContent=n,l=c.offsetWidth>0?c.offsetWidth:l,a=c.offsetHeight>0?c.offsetHeight:a;const b=w.top+f.y,y=w.left+f.x;let E=b+2,C=y+w.width-l-2;(w.width<l+4||w.height<a+4)&&(E=b-a-2,C=y+w.width-l,C<f.x&&(C=y)),E=Math.max(0,Math.min(E,window.innerHeight-a)),C=Math.max(0,Math.min(C,window.innerWidth-l)),c.style.top=`${E}px`,c.style.left=`${C}px`,g.appendChild(c);const N=(e,t)=>{let n=0;return(...o)=>{const i=performance.now();if(!(i-n<t))return n=i,e(...o)}},k=N((()=>{const t=e.getClientRects();let n={x:0,y:0};if(o){const e=o.getBoundingClientRect();n.x=e.left,n.y=e.top}if(r.forEach(((e,o)=>{if(o<t.length){const i=t[o],r=i.top+n.y,s=i.left+n.x;e.element.style.top=`${r}px`,e.element.style.left=`${s}px`,e.element.style.width=`${i.width}px`,e.element.style.height=`${i.height}px`,e.element.style.display=0===i.width||0===i.height?"none":"block"}else e.element.style.display="none"})),t.length<r.length)for(let e=t.length;e<r.length;e++)r[e].element.style.display="none";if(c&&t.length>0){const e=t[0],o=e.top+n.y,i=e.left+n.x;let r=o+2,s=i+e.width-l-2;(e.width<l+4||e.height<a+4)&&(r=o-a-2,s=i+e.width-l,s<n.x&&(s=i)),r=Math.max(0,Math.min(r,window.innerHeight-a)),s=Math.max(0,Math.min(s,window.innerWidth-l)),c.style.top=`${r}px`,c.style.left=`${s}px`,c.style.display="block"}else c&&(c.style.display="none")}),16);return window.addEventListener("scroll",k,!0),window.addEventListener("resize",k),d=()=>{window.removeEventListener("scroll",k,!0),window.removeEventListener("resize",k),r.forEach((e=>e.element.remove())),c&&c.remove()},i.appendChild(g),n+1}finally{!function(e){const t=s[e].pop();performance.now()}("highlighting"),d&&(window._highlightCleanupFunctions=window._highlightCleanupFunctions||[]).push(d)}}function b(e){if(!e.parentElement)return 0;const t=e.nodeName.toLowerCase(),n=Array.from(e.parentElement.children).filter((e=>e.nodeName.toLowerCase()===t));if(1===n.length)return 0;return n.indexOf(e)+1}function y(e,t=!0){if(g.has(e))return g.get(e);const n=[];let o=e;for(;o&&o.nodeType===Node.ELEMENT_NODE&&(!t||!(o.parentNode instanceof ShadowRoot||o.parentNode instanceof HTMLIFrameElement));){const e=b(o),t=o.nodeName.toLowerCase(),i=e>0?`[${e}]`:"";n.unshift(`${t}${i}`),o=o.parentNode}const i=n.join("/");return g.set(e,i),i}function E(e){try{if(-1===o){const t=e.parentElement;if(!t)return!1;try{return t.checkVisibility({checkOpacity:!0,checkVisibilityCSS:!0})}catch(e){const n=window.getComputedStyle(t);return"none"!==n.display&&"hidden"!==n.visibility&&"0"!==n.opacity}}const t=document.createRange();t.selectNodeContents(e);const n=t.getClientRects();if(!n||0===n.length)return!1;let i=!1,r=!1;for(const e of n)if(e.width>0&&e.height>0&&(i=!0,!(e.bottom<-o||e.top>window.innerHeight+o||e.right<-o||e.left>window.innerWidth+o))){r=!0;break}if(!i||!r)return!1;const s=e.parentElement;if(!s)return!1;try{return s.checkVisibility({checkOpacity:!0,checkVisibilityCSS:!0})}catch(e){const t=window.getComputedStyle(s);return"none"!==t.display&&"hidden"!==t.visibility&&"0"!==t.opacity}}catch(e){return!1}}function C(e){const t=h(e);return e.offsetWidth>0&&e.offsetHeight>0&&"hidden"!==t.visibility&&"none"!==t.display}function N(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=e.tagName.toLowerCase(),n=h(e),o=new Set(["pointer","move","text","grab","grabbing","cell","copy","alias","all-scroll","col-resize","context-menu","crosshair","e-resize","ew-resize","help","n-resize","ne-resize","nesw-resize","ns-resize","nw-resize","nwse-resize","row-resize","s-resize","se-resize","sw-resize","vertical-text","w-resize","zoom-in","zoom-out"]),i=new Set(["not-allowed","no-drop","wait","progress","initial","inherit"]);let r=function(e){return"html"!==e.tagName.toLowerCase()&&!!o.has(n.cursor)}(e);if(r)return!0;const s=new Set(["a","button","input","select","textarea","details","summary","label","option","optgroup","fieldset","legend"]),c=new Set(["disabled","readonly"]);if(s.has(t)){if(i.has(n.cursor))return!1;for(const t of c)if(e.hasAttribute(t)||"true"===e.getAttribute(t)||""===e.getAttribute(t))return!1;return!e.disabled&&(!e.readOnly&&!e.inert)}const l=e.getAttribute("role"),a=e.getAttribute("aria-role");if("true"===e.getAttribute("contenteditable")||e.isContentEditable)return!0;if(e.classList&&(e.classList.contains("button")||e.classList.contains("dropdown-toggle")||e.getAttribute("data-index")||"dropdown"===e.getAttribute("data-toggle")||"true"===e.getAttribute("aria-haspopup")))return!0;const d=new Set(["button","menuitemradio","menuitemcheckbox","radio","checkbox","tab","switch","slider","spinbutton","combobox","searchbox","textbox","option","scrollbar"]);if(s.has(t)||d.has(l)||d.has(a))return!0;try{if("function"==typeof getEventListeners){const t=getEventListeners(e),n=["click","mousedown","mouseup","dblclick"];for(const e of n)if(t[e]&&t[e].length>0)return!0}const t=window.getEventListenersForNode;if("function"==typeof t){const n=t(e),o=["click","mousedown","mouseup","keydown","keyup","submit","change","input","focus","blur"];for(const e of o)for(const t of n)if(t.type===e)return!0}const n=["onclick","onmousedown","onmouseup","ondblclick"];for(const t of n)if(e.hasAttribute(t)||"function"==typeof e[t])return!0}catch(e){}return!1}function k(e){if(-1===o)return!0;const t=function(e){if(!e)return null;if(d.clientRects.has(e))return i&&c&&c.cacheMetrics.clientRectsCacheHits++,d.clientRects.get(e);i&&c&&c.cacheMetrics.clientRectsCacheMisses++;const t=e.getClientRects();return t&&d.clientRects.set(e,t),t}(e);if(!t||0===t.length)return!1;let n=!1;for(const e of t)if(e.width>0&&e.height>0&&!(e.bottom<-o||e.top>window.innerHeight+o||e.right<-o||e.left>window.innerWidth+o)){n=!0;break}if(!n)return!1;if(e.ownerDocument!==window.document)return!0;const r=e.getRootNode();if(r instanceof ShadowRoot){const n=t[Math.floor(t.length/2)].left+t[Math.floor(t.length/2)].width/2,o=t[Math.floor(t.length/2)].top+t[Math.floor(t.length/2)].height/2;try{const t=a((()=>r.elementFromPoint(n,o)),"elementFromPoint");if(!t)return!1;let i=t;for(;i&&i!==r;){if(i===e)return!0;i=i.parentElement}return!1}catch(e){return!0}}const s=t[Math.floor(t.length/2)].left+t[Math.floor(t.length/2)].width/2,l=t[Math.floor(t.length/2)].top+t[Math.floor(t.length/2)].height/2;try{const t=document.elementFromPoint(s,l);if(!t)return!1;let n=t;for(;n&&n!==document.documentElement;){if(n===e)return!0;n=n.parentElement}return!1}catch(e){return!0}}function M(e,t){if(-1===t)return!0;const n=e.getClientRects();if(!n||0===n.length){const n=u(e);return!(!n||0===n.width||0===n.height)&&!(n.bottom<-t||n.top>window.innerHeight+t||n.right<-t||n.left>window.innerWidth+t)}for(const e of n)if(0!==e.width&&0!==e.height&&!(e.bottom<-t||e.top>window.innerHeight+t||e.right<-t||e.left>window.innerWidth+t))return!0;return!1}function x(e){let t=e,n=0,o=0;return a((()=>{for(;t&&t!==document.documentElement;)(t.scrollLeft||t.scrollTop)&&(n+=t.scrollLeft,o+=t.scrollTop),t=t.parentElement;return n+=window.scrollX,o+=window.scrollY,{scrollX:n,scrollY:o}}),"scrollOperations")}window.cleanupHighlights=function(){window._highlightCleanupFunctions&&window._highlightCleanupFunctions.length&&(window._highlightCleanupFunctions.forEach((e=>e())),window._highlightCleanupFunctions=[]);const e=document.getElementById(m);e&&e.remove()};const T=new Set(["a","button","input","select","textarea","summary","details","label","option"]),R=new Set(["button","link","menuitem","menuitemradio","menuitemcheckbox","radio","checkbox","tab","switch","slider","spinbutton","combobox","searchbox","textbox","listbox","option","scrollbar"]);function D(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=e.tagName.toLowerCase(),n=e.getAttribute("role");if("iframe"===t)return!0;if(T.has(t))return!0;if(n&&R.has(n))return!0;if(e.isContentEditable||"true"===e.getAttribute("contenteditable"))return!0;if(e.hasAttribute("data-testid")||e.hasAttribute("data-cy")||e.hasAttribute("data-test"))return!0;if(e.hasAttribute("onclick")||"function"==typeof e.onclick)return!0;try{const t=window.getEventListenersForNode;if("function"==typeof t){const n=t(e),o=["click","mousedown","mouseup","keydown","keyup","submit","change","input","focus","blur"];for(const e of o)for(const t of n)if(t.type===e)return!0}if(["onmousedown","onmouseup","onkeydown","onkeyup","onsubmit","onchange","oninput","onfocus","onblur"].some((t=>e.hasAttribute(t))))return!0}catch(e){}return!!function(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;if(!C(e))return!1;const t=e.hasAttribute("role")||e.hasAttribute("tabindex")||e.hasAttribute("onclick")||"function"==typeof e.onclick,n=/\b(btn|clickable|menu|item|entry|link)\b/i.test(e.className||""),o=Boolean(e.closest('button,a,[role="button"],.menu,.dropdown,.list,.toolbar')),i=[...e.children].some(C),r=e.parentElement&&e.parentElement.isSameNode(document.body);return(N(e)||t||n)&&i&&o&&!r}(e)}w=l(w),N=l(N),C=l(C),k=l(k),M=l(M),E=l(E),x=l(x);const S=function e(t,s=null,l=!1){if(!t||t.id===m||t.nodeType!==Node.ELEMENT_NODE&&t.nodeType!==Node.TEXT_NODE)return i&&c.nodeMetrics.skippedNodes++,null;if(i&&c.nodeMetrics.totalNodes++,!t||t.id===m)return i&&c.nodeMetrics.skippedNodes++,null;if(t===document.body){const n={tagName:"body",attributes:{},xpath:"/body",children:[]};for(const o of t.childNodes){const t=e(o,s,!1);t&&n.children.push(t)}const o=""+f.current++;return p[o]=n,i&&c.nodeMetrics.processedNodes++,o}if(t.nodeType!==Node.ELEMENT_NODE&&t.nodeType!==Node.TEXT_NODE)return i&&c.nodeMetrics.skippedNodes++,null;if(t.nodeType===Node.TEXT_NODE){const e=t.textContent.trim();if(!e)return i&&c.nodeMetrics.skippedNodes++,null;const n=t.parentElement;if(!n||"script"===n.tagName.toLowerCase())return i&&c.nodeMetrics.skippedNodes++,null;const o=""+f.current++;return p[o]={type:"TEXT_NODE",text:e,isVisible:E(t)},i&&c.nodeMetrics.processedNodes++,o}if(t.nodeType===Node.ELEMENT_NODE&&!function(e){if(!e||!e.tagName)return!1;const t=new Set(["body","div","main","article","section","nav","header","footer"]),n=e.tagName.toLowerCase();return!!t.has(n)||!new Set(["svg","script","style","link","meta","noscript","template"]).has(n)}(t))return i&&c.nodeMetrics.skippedNodes++,null;if(-1!==o){const e=u(t),n=h(t),r=n&&("fixed"===n.position||"sticky"===n.position),s=t.offsetWidth>0||t.offsetHeight>0;if(!e||!r&&!s&&(e.bottom<-o||e.top>window.innerHeight+o||e.right<-o||e.left>window.innerWidth+o))return i&&c.nodeMetrics.skippedNodes++,null}const a={tagName:t.tagName.toLowerCase(),attributes:{},xpath:y(t,!0),children:[]};if(function(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=e.tagName.toLowerCase();return!!new Set(["a","button","input","select","textarea","details","summary","label"]).has(t)||(e.hasAttribute("onclick")||e.hasAttribute("role")||e.hasAttribute("tabindex")||e.hasAttribute("aria-")||e.hasAttribute("data-action")||"true"===e.getAttribute("contenteditable"))}(t)||"iframe"===t.tagName.toLowerCase()||"body"===t.tagName.toLowerCase()){const e=t.getAttributeNames?.()||[];for(const n of e)a.attributes[n]=t.getAttribute(n)}let d=!1;if(t.nodeType===Node.ELEMENT_NODE&&(a.isVisible=C(t),a.isVisible&&(a.isTopElement=k(t),a.isTopElement&&(a.isInteractive=N(t),d=function(e,t,i,s){if(!e.isInteractive)return!1;let c=!1;return c=!s||!!D(t),!(!c||(e.isInViewport=M(t,o),!e.isInViewport&&-1!==o)||(e.highlightIndex=r++,n>=0?n===e.highlightIndex&&w(t,e.highlightIndex,i):w(t,e.highlightIndex,i),0))}(a,t,s,l)))),t.tagName){const n=t.tagName.toLowerCase();if("iframe"===n)try{const n=t.contentDocument||t.contentWindow?.document;if(n)for(const o of n.childNodes){const n=e(o,t,!1);n&&a.children.push(n)}}catch(e){}else if(t.isContentEditable||"true"===t.getAttribute("contenteditable")||"tinymce"===t.id||t.classList.contains("mce-content-body")||"body"===n&&t.getAttribute("data-id")?.startsWith("mce_"))for(const n of t.childNodes){const t=e(n,s,d);t&&a.children.push(t)}else{if(t.shadowRoot){a.shadowRoot=!0;for(const n of t.shadowRoot.childNodes){const t=e(n,s,d);t&&a.children.push(t)}}for(const n of t.childNodes){const t=e(n,s,d||l);t&&a.children.push(t)}}}if("a"===a.tagName&&0===a.children.length&&!a.attributes.href)return i&&c.nodeMetrics.skippedNodes++,null;const g=""+f.current++;return p[g]=a,i&&c.nodeMetrics.processedNodes++,g}(document.body);if(d.clearCache(),i&&c){Object.keys(c.timings).forEach((e=>{c.timings[e]=c.timings[e]/1e3})),Object.keys(c.buildDomTreeBreakdown).forEach((e=>{"number"==typeof c.buildDomTreeBreakdown[e]&&(c.buildDomTreeBreakdown[e]=c.buildDomTreeBreakdown[e]/1e3)})),c.buildDomTreeBreakdown.buildDomTreeCalls>0&&(c.buildDomTreeBreakdown.averageTimePerNode=c.buildDomTreeBreakdown.totalTime/c.buildDomTreeBreakdown.buildDomTreeCalls),c.buildDomTreeBreakdown.timeInChildCalls=c.buildDomTreeBreakdown.totalTime-c.buildDomTreeBreakdown.totalSelfTime,Object.keys(c.buildDomTreeBreakdown.domOperations).forEach((e=>{const t=c.buildDomTreeBreakdown.domOperations[e],n=c.buildDomTreeBreakdown.domOperationCounts[e];n>0&&(c.buildDomTreeBreakdown.domOperations[`${e}Average`]=t/n)}));const e=c.cacheMetrics.boundingRectCacheHits+c.cacheMetrics.boundingRectCacheMisses,t=c.cacheMetrics.computedStyleCacheHits+c.cacheMetrics.computedStyleCacheMisses;e>0&&(c.cacheMetrics.boundingRectHitRate=c.cacheMetrics.boundingRectCacheHits/e),t>0&&(c.cacheMetrics.computedStyleHitRate=c.cacheMetrics.computedStyleCacheHits/t),e+t>0&&(c.cacheMetrics.overallHitRate=(c.cacheMetrics.boundingRectCacheHits+c.cacheMetrics.computedStyleCacheHits)/(e+t))}return i?{rootId:S,map:p,perfMetrics:c}:{rootId:S,map:p}};