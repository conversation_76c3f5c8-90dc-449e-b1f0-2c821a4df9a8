# Copyright 2024 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//ui/webui/resources/tools/generate_grd.gni")

generate_grd("build") {
  grd_prefix = "ai_side_panel"
  out_grd = "$target_gen_dir/resources.grd"

  input_files = [
    "manifest.json",
    "background.js",
    "content.js",
    "sidepanel.html",
    "sidepanel.js",
    "options.html",
    "options.js",
    "Readability.js",
    "buildDomTree.js",
    "assets/icon16.png",
    "assets/icon48.png",
    "assets/icon128.png",
  ]
  
  input_files_base_dir = rebase_path(".", "//")
}
