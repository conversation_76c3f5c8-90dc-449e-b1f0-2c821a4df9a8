<!doctype html><html lang="en" class="dark"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>Nxtscape Side Panel</title><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet"><style>/* Reset default margins and add base font for side panel */
    html, body {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
      background-color: #272728;
      overflow: hidden;
    }

    #root {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    /* Side panel specific optimizations */
    * {
      box-sizing: border-box;
    }</style><script defer="defer" src="sidepanel.js"></script></head><body><div id="root"></div><script src="sidepanel.js"></script></body></html>