(()=>{"use strict";var e={540:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},873:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(1601),a=n.n(r),o=n(6314),i=n.n(o)()(a());i.push([e.id,"*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n  :root {\n    --background: 0 0% 100%;\n    --foreground: 240 10% 3.9%;\n    --card: 0 0% 100%;\n    --card-foreground: 240 10% 3.9%;\n    --popover: 0 0% 100%;\n    --popover-foreground: 240 10% 3.9%;\n    --primary: 221.2 83.2% 53.3%;\n    --primary-foreground: 210 40% 98%;\n    --secondary: 240 4.8% 95.9%;\n    --secondary-foreground: 240 5.9% 10%;\n    --muted: 240 4.8% 95.9%;\n    --muted-foreground: 240 3.8% 46.1%;\n    --accent: 240 4.8% 95.9%;\n    --accent-foreground: 240 5.9% 10%;\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 0 0% 98%;\n    --border: 240 5.9% 90%;\n    --input: 240 5.9% 90%;\n    --ring: 221.2 83.2% 53.3%;\n    --radius: 0.5rem;\n  }\n  * {\n  border-color: hsl(var(--border));\n}\n  body {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n    font-feature-settings: \"rlig\" 1, \"calt\" 1;\n    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n}\n.container {\n  width: 100%;\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 2rem;\n  padding-left: 2rem;\n}\n@media (min-width: 1400px) {\n\n  .container {\n    max-width: 1400px;\n  }\n}\n.visible {\n  visibility: visible;\n}\n.static {\n  position: static;\n}\n.fixed {\n  position: fixed;\n}\n.relative {\n  position: relative;\n}\n.block {\n  display: block;\n}\n.inline {\n  display: inline;\n}\n.flex {\n  display: flex;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.table {\n  display: table;\n}\n.contents {\n  display: contents;\n}\n.h-10 {\n  height: 2.5rem;\n}\n.h-8 {\n  height: 2rem;\n}\n.h-9 {\n  height: 2.25rem;\n}\n.h-\\[1px\\] {\n  height: 1px;\n}\n.h-full {\n  height: 100%;\n}\n.min-h-\\[80px\\] {\n  min-height: 80px;\n}\n.w-9 {\n  width: 2.25rem;\n}\n.w-\\[1px\\] {\n  width: 1px;\n}\n.w-full {\n  width: 100%;\n}\n.shrink-0 {\n  flex-shrink: 0;\n}\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.flex-col {\n  flex-direction: column;\n}\n.items-center {\n  align-items: center;\n}\n.justify-center {\n  justify-content: center;\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: var(--radius);\n}\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\n.border {\n  border-width: 1px;\n}\n.border-border {\n  border-color: hsl(var(--border));\n}\n.border-input {\n  border-color: hsl(var(--input));\n}\n.border-transparent {\n  border-color: transparent;\n}\n.bg-blue-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.bg-border {\n  background-color: hsl(var(--border));\n}\n.bg-card {\n  background-color: hsl(var(--card));\n}\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\n.bg-green-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\n.bg-transparent {\n  background-color: transparent;\n}\n.bg-yellow-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\n}\n.p-6 {\n  padding: 1.5rem;\n}\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.pt-0 {\n  padding-top: 0px;\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-medium {\n  font-weight: 500;\n}\n.font-semibold {\n  font-weight: 600;\n}\n.leading-none {\n  line-height: 1;\n}\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\n.text-foreground {\n  color: hsl(var(--foreground));\n}\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\n.text-primary {\n  color: hsl(var(--primary));\n}\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline {\n  outline-style: solid;\n}\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n.blur {\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.\\!filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;\n}\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-filter {\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.running {\n  animation-play-state: running;\n}\n.paused {\n  animation-play-state: paused;\n}\n\n/* Side panel specific overrides */\n:root {\n  color-scheme: light dark; /* Support both light and dark modes */\n}\n\n/* Automatically use system theme preference */\n@media (prefers-color-scheme: dark) {\n\n  html {\n    --background: 222.2 84% 4.9%;\n    --foreground: 210 40% 98%;\n    --card: 224 71% 4%;\n    --card-foreground: 210 40% 98%;\n    --popover: 224 71% 4%;\n    --popover-foreground: 210 40% 98%;\n    --primary: 217.2 91.2% 59.8%;\n    --primary-foreground: 210 40% 98%;\n    --secondary: 222.2 47.4% 11.2%;\n    --secondary-foreground: 210 40% 98%;\n    --muted: 217.2 32.6% 17.5%;\n    --muted-foreground: 215 20.2% 65.1%;\n    --accent: 217.2 32.6% 17.5%;\n    --accent-foreground: 210 40% 98%;\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 217.2 32.6% 17.5%;\n    --input: 217.2 32.6% 17.5%;\n    --ring: 224.3 76.3% 48%;\n  }\n}\n\n/* Base styles for sidepanel */\nhtml, body {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n  transition: background-color 0.3s ease, color 0.3s ease;\n}\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\n.file\\:bg-transparent::file-selector-button {\n  background-color: transparent;\n}\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\n.placeholder\\:text-muted-foreground::-moz-placeholder {\n  color: hsl(var(--muted-foreground));\n}\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\n.hover\\:bg-blue-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-destructive\\/80:hover {\n  background-color: hsl(var(--destructive) / 0.8);\n}\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\n.hover\\:bg-green-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary\\/80:hover {\n  background-color: hsl(var(--primary) / 0.8);\n}\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\n.hover\\:bg-yellow-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));\n}\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.focus-visible\\:ring-1:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n} ",""]);const l=i},890:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(1601),a=n.n(r),o=n(6314),i=n.n(o)()(a());i.push([e.id,':root{--background-primary: #222222;--background-secondary: #2a2a2b;--surface-primary: #333334;--surface-secondary: #3a3a3b;--surface-hover: #404041;--background-secondary-alpha-50: rgba(42, 42, 43, 0.5);--background-secondary-alpha-30: rgba(42, 42, 43, 0.3);--surface-primary-alpha-30: rgba(51, 51, 52, 0.3);--text-primary: #fafafa;--text-secondary: #a1a1aa;--text-muted: #71717a;--text-disabled: #52525b;--primary: #3b82f6;--primary-rgb: 59, 130, 246;--primary-hover: #2563eb;--primary-foreground: #fafafa;--primary-alpha-05: rgba(59, 130, 246, 0.05);--primary-alpha-10: rgba(59, 130, 246, 0.1);--primary-alpha-15: rgba(59, 130, 246, 0.15);--primary-alpha-20: rgba(59, 130, 246, 0.2);--primary-alpha-30: rgba(59, 130, 246, 0.3);--accent: #06b6d4;--accent-hover: #0891b2;--success: #22c55e;--success-alpha-05: rgba(34, 197, 94, 0.05);--success-alpha-10: rgba(34, 197, 94, 0.1);--success-alpha-20: rgba(34, 197, 94, 0.2);--success-alpha-30: rgba(34, 197, 94, 0.3);--error: #ef4444;--error-alpha-05: rgba(239, 68, 68, 0.05);--error-alpha-10: rgba(239, 68, 68, 0.1);--error-alpha-20: rgba(239, 68, 68, 0.2);--error-alpha-30: rgba(239, 68, 68, 0.3);--error-alpha-50: rgba(239, 68, 68, 0.5);--warning: #f59e0b;--warning-alpha-20: rgba(245, 158, 11, 0.2);--warning-alpha-30: rgba(245, 158, 11, 0.3);--message-bg: #1E1E22;--message-border: rgba(255, 255, 255, 0.1);--border-color: rgba(255, 255, 255, 0.1);--border-hover: rgba(255, 255, 255, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.5);--font-mono: "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Fira Code", monospace;--scrollbar-track: #222222;--scrollbar-thumb: #3a3a3b;--scrollbar-thumb-hover: #404041}@media(prefers-color-scheme: light){:root{--background-primary: #ffffff;--background-secondary: #f8f9fa;--surface-primary: #f1f3f5;--surface-secondary: #e9ecef;--surface-hover: #dee2e6;--background-secondary-alpha-50: rgba(248, 249, 250, 0.5);--background-secondary-alpha-30: rgba(248, 249, 250, 0.3);--surface-primary-alpha-30: rgba(241, 243, 245, 0.3);--text-primary: #0a0a0a;--text-secondary: #52525b;--text-muted: #71717a;--text-disabled: #a1a1aa;--primary: #2563eb;--primary-rgb: 37, 99, 235;--primary-hover: #1d4ed8;--primary-foreground: #ffffff;--primary-alpha-05: rgba(37, 99, 235, 0.05);--primary-alpha-10: rgba(37, 99, 235, 0.1);--primary-alpha-15: rgba(37, 99, 235, 0.15);--primary-alpha-20: rgba(37, 99, 235, 0.2);--primary-alpha-30: rgba(37, 99, 235, 0.3);--accent: #0891b2;--accent-hover: #0e7490;--success: #16a34a;--success-alpha-05: rgba(22, 163, 74, 0.05);--success-alpha-10: rgba(22, 163, 74, 0.1);--success-alpha-20: rgba(22, 163, 74, 0.2);--success-alpha-30: rgba(22, 163, 74, 0.3);--error: #dc2626;--error-alpha-05: rgba(220, 38, 38, 0.05);--error-alpha-10: rgba(220, 38, 38, 0.1);--error-alpha-20: rgba(220, 38, 38, 0.2);--error-alpha-30: rgba(220, 38, 38, 0.3);--error-alpha-50: rgba(220, 38, 38, 0.5);--warning: #d97706;--warning-alpha-20: rgba(217, 119, 6, 0.2);--warning-alpha-30: rgba(217, 119, 6, 0.3);--message-bg: #F4F4F5;--message-border: rgba(0, 0, 0, 0.1);--border-color: rgba(0, 0, 0, 0.1);--border-hover: rgba(0, 0, 0, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.1);--scrollbar-track: #f8f9fa;--scrollbar-thumb: #e9ecef;--scrollbar-thumb-hover: #dee2e6}}*{margin:0;padding:0;box-sizing:border-box}@keyframes index__spin--MtCJA{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes index__pulse--VVeHd{0%,100%{opacity:1}50%{opacity:.5}}@keyframes index__slideIn--X2fEH{0%{transform:translateX(100%)}100%{transform:translateX(0)}}.index__nxtscape-sidepanel--kKz0I{font-family:"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;background-color:var(--background-primary);color:var(--text-primary)}.index__scrollable--RVGQL::-webkit-scrollbar{width:6px}.index__scrollable--RVGQL::-webkit-scrollbar-track{background:var(--scrollbar-track)}.index__scrollable--RVGQL::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.index__scrollable--RVGQL::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.index__processing-spinner--TAwOA{animation:index__spin--MtCJA 1s linear infinite}.index__status-indicator--connected--pbulZ{color:var(--success)}.index__status-indicator--disconnected--iepIU{color:var(--error)}.index__status-indicator--processing--Y_eOv{color:var(--primary)}',""]),i.locals={"nxtscape-sidepanel":"index__nxtscape-sidepanel--kKz0I",scrollable:"index__scrollable--RVGQL","processing-spinner":"index__processing-spinner--TAwOA",spin:"index__spin--MtCJA","status-indicator--connected":"index__status-indicator--connected--pbulZ","status-indicator--disconnected":"index__status-indicator--disconnected--iepIU","status-indicator--processing":"index__status-indicator--processing--Y_eOv",pulse:"index__pulse--VVeHd",slideIn:"index__slideIn--X2fEH"};const l=i},961:(e,t,n)=>{!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){}}(),e.exports=n(2551)},1113:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},1601:e=>{e.exports=function(e){return e[1]}},1729:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(1601),a=n.n(r),o=n(6314),i=n.n(o)()(a());i.push([e.id,':root{--background-primary: #222222;--background-secondary: #2a2a2b;--surface-primary: #333334;--surface-secondary: #3a3a3b;--surface-hover: #404041;--background-secondary-alpha-50: rgba(42, 42, 43, 0.5);--background-secondary-alpha-30: rgba(42, 42, 43, 0.3);--surface-primary-alpha-30: rgba(51, 51, 52, 0.3);--text-primary: #fafafa;--text-secondary: #a1a1aa;--text-muted: #71717a;--text-disabled: #52525b;--primary: #3b82f6;--primary-rgb: 59, 130, 246;--primary-hover: #2563eb;--primary-foreground: #fafafa;--primary-alpha-05: rgba(59, 130, 246, 0.05);--primary-alpha-10: rgba(59, 130, 246, 0.1);--primary-alpha-15: rgba(59, 130, 246, 0.15);--primary-alpha-20: rgba(59, 130, 246, 0.2);--primary-alpha-30: rgba(59, 130, 246, 0.3);--accent: #06b6d4;--accent-hover: #0891b2;--success: #22c55e;--success-alpha-05: rgba(34, 197, 94, 0.05);--success-alpha-10: rgba(34, 197, 94, 0.1);--success-alpha-20: rgba(34, 197, 94, 0.2);--success-alpha-30: rgba(34, 197, 94, 0.3);--error: #ef4444;--error-alpha-05: rgba(239, 68, 68, 0.05);--error-alpha-10: rgba(239, 68, 68, 0.1);--error-alpha-20: rgba(239, 68, 68, 0.2);--error-alpha-30: rgba(239, 68, 68, 0.3);--error-alpha-50: rgba(239, 68, 68, 0.5);--warning: #f59e0b;--warning-alpha-20: rgba(245, 158, 11, 0.2);--warning-alpha-30: rgba(245, 158, 11, 0.3);--message-bg: #1E1E22;--message-border: rgba(255, 255, 255, 0.1);--border-color: rgba(255, 255, 255, 0.1);--border-hover: rgba(255, 255, 255, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.5);--font-mono: "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Fira Code", monospace;--scrollbar-track: #222222;--scrollbar-thumb: #3a3a3b;--scrollbar-thumb-hover: #404041}@media(prefers-color-scheme: light){:root{--background-primary: #ffffff;--background-secondary: #f8f9fa;--surface-primary: #f1f3f5;--surface-secondary: #e9ecef;--surface-hover: #dee2e6;--background-secondary-alpha-50: rgba(248, 249, 250, 0.5);--background-secondary-alpha-30: rgba(248, 249, 250, 0.3);--surface-primary-alpha-30: rgba(241, 243, 245, 0.3);--text-primary: #0a0a0a;--text-secondary: #52525b;--text-muted: #71717a;--text-disabled: #a1a1aa;--primary: #2563eb;--primary-rgb: 37, 99, 235;--primary-hover: #1d4ed8;--primary-foreground: #ffffff;--primary-alpha-05: rgba(37, 99, 235, 0.05);--primary-alpha-10: rgba(37, 99, 235, 0.1);--primary-alpha-15: rgba(37, 99, 235, 0.15);--primary-alpha-20: rgba(37, 99, 235, 0.2);--primary-alpha-30: rgba(37, 99, 235, 0.3);--accent: #0891b2;--accent-hover: #0e7490;--success: #16a34a;--success-alpha-05: rgba(22, 163, 74, 0.05);--success-alpha-10: rgba(22, 163, 74, 0.1);--success-alpha-20: rgba(22, 163, 74, 0.2);--success-alpha-30: rgba(22, 163, 74, 0.3);--error: #dc2626;--error-alpha-05: rgba(220, 38, 38, 0.05);--error-alpha-10: rgba(220, 38, 38, 0.1);--error-alpha-20: rgba(220, 38, 38, 0.2);--error-alpha-30: rgba(220, 38, 38, 0.3);--error-alpha-50: rgba(220, 38, 38, 0.5);--warning: #d97706;--warning-alpha-20: rgba(217, 119, 6, 0.2);--warning-alpha-30: rgba(217, 119, 6, 0.3);--message-bg: #F4F4F5;--message-border: rgba(0, 0, 0, 0.1);--border-color: rgba(0, 0, 0, 0.1);--border-hover: rgba(0, 0, 0, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.1);--scrollbar-track: #f8f9fa;--scrollbar-thumb: #e9ecef;--scrollbar-thumb-hover: #dee2e6}}*{margin:0;padding:0;box-sizing:border-box}@keyframes MarkdownContent-module__spin--scmx9{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes MarkdownContent-module__pulse--bCScw{0%,100%{opacity:1}50%{opacity:.5}}@keyframes MarkdownContent-module__slideIn--hVspc{0%{transform:translateX(100%)}100%{transform:translateX(0)}}.MarkdownContent-module__nxtscape-sidepanel--W7cDm{font-family:"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;background-color:var(--background-primary);color:var(--text-primary)}.MarkdownContent-module__scrollable--yA_Tz::-webkit-scrollbar{width:6px}.MarkdownContent-module__scrollable--yA_Tz::-webkit-scrollbar-track{background:var(--scrollbar-track)}.MarkdownContent-module__scrollable--yA_Tz::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.MarkdownContent-module__scrollable--yA_Tz::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.MarkdownContent-module__processing-spinner--AwZ8d{animation:MarkdownContent-module__spin--scmx9 1s linear infinite}.MarkdownContent-module__status-indicator--connected--FxHET{color:var(--success)}.MarkdownContent-module__status-indicator--disconnected--PdYzf{color:var(--error)}.MarkdownContent-module__status-indicator--processing--HF0Xx{color:var(--primary)}:root{--background-primary: #222222;--background-secondary: #2a2a2b;--surface-primary: #333334;--surface-secondary: #3a3a3b;--surface-hover: #404041;--background-secondary-alpha-50: rgba(42, 42, 43, 0.5);--background-secondary-alpha-30: rgba(42, 42, 43, 0.3);--surface-primary-alpha-30: rgba(51, 51, 52, 0.3);--text-primary: #fafafa;--text-secondary: #a1a1aa;--text-muted: #71717a;--text-disabled: #52525b;--primary: #3b82f6;--primary-rgb: 59, 130, 246;--primary-hover: #2563eb;--primary-foreground: #fafafa;--primary-alpha-05: rgba(59, 130, 246, 0.05);--primary-alpha-10: rgba(59, 130, 246, 0.1);--primary-alpha-15: rgba(59, 130, 246, 0.15);--primary-alpha-20: rgba(59, 130, 246, 0.2);--primary-alpha-30: rgba(59, 130, 246, 0.3);--accent: #06b6d4;--accent-hover: #0891b2;--success: #22c55e;--success-alpha-05: rgba(34, 197, 94, 0.05);--success-alpha-10: rgba(34, 197, 94, 0.1);--success-alpha-20: rgba(34, 197, 94, 0.2);--success-alpha-30: rgba(34, 197, 94, 0.3);--error: #ef4444;--error-alpha-05: rgba(239, 68, 68, 0.05);--error-alpha-10: rgba(239, 68, 68, 0.1);--error-alpha-20: rgba(239, 68, 68, 0.2);--error-alpha-30: rgba(239, 68, 68, 0.3);--error-alpha-50: rgba(239, 68, 68, 0.5);--warning: #f59e0b;--warning-alpha-20: rgba(245, 158, 11, 0.2);--warning-alpha-30: rgba(245, 158, 11, 0.3);--message-bg: #1E1E22;--message-border: rgba(255, 255, 255, 0.1);--border-color: rgba(255, 255, 255, 0.1);--border-hover: rgba(255, 255, 255, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.5);--font-mono: "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Fira Code", monospace;--scrollbar-track: #222222;--scrollbar-thumb: #3a3a3b;--scrollbar-thumb-hover: #404041}@media(prefers-color-scheme: light){:root{--background-primary: #ffffff;--background-secondary: #f8f9fa;--surface-primary: #f1f3f5;--surface-secondary: #e9ecef;--surface-hover: #dee2e6;--background-secondary-alpha-50: rgba(248, 249, 250, 0.5);--background-secondary-alpha-30: rgba(248, 249, 250, 0.3);--surface-primary-alpha-30: rgba(241, 243, 245, 0.3);--text-primary: #0a0a0a;--text-secondary: #52525b;--text-muted: #71717a;--text-disabled: #a1a1aa;--primary: #2563eb;--primary-rgb: 37, 99, 235;--primary-hover: #1d4ed8;--primary-foreground: #ffffff;--primary-alpha-05: rgba(37, 99, 235, 0.05);--primary-alpha-10: rgba(37, 99, 235, 0.1);--primary-alpha-15: rgba(37, 99, 235, 0.15);--primary-alpha-20: rgba(37, 99, 235, 0.2);--primary-alpha-30: rgba(37, 99, 235, 0.3);--accent: #0891b2;--accent-hover: #0e7490;--success: #16a34a;--success-alpha-05: rgba(22, 163, 74, 0.05);--success-alpha-10: rgba(22, 163, 74, 0.1);--success-alpha-20: rgba(22, 163, 74, 0.2);--success-alpha-30: rgba(22, 163, 74, 0.3);--error: #dc2626;--error-alpha-05: rgba(220, 38, 38, 0.05);--error-alpha-10: rgba(220, 38, 38, 0.1);--error-alpha-20: rgba(220, 38, 38, 0.2);--error-alpha-30: rgba(220, 38, 38, 0.3);--error-alpha-50: rgba(220, 38, 38, 0.5);--warning: #d97706;--warning-alpha-20: rgba(217, 119, 6, 0.2);--warning-alpha-30: rgba(217, 119, 6, 0.3);--message-bg: #F4F4F5;--message-border: rgba(0, 0, 0, 0.1);--border-color: rgba(0, 0, 0, 0.1);--border-hover: rgba(0, 0, 0, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.1);--scrollbar-track: #f8f9fa;--scrollbar-thumb: #e9ecef;--scrollbar-thumb-hover: #dee2e6}}.MarkdownContent-module__container--ubL5m{line-height:1.6;word-break:break-word;overflow-wrap:break-word}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__processing--s28dx{opacity:.6;font-style:italic}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__plainText--IMq1z{white-space:pre-wrap;font-family:inherit;color:var(--text-primary)}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__markdown--DHmLo{font-family:inherit;line-height:1.6;color:var(--text-primary)}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o{line-height:1.5}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o.MarkdownContent-module__plainText--IMq1z{font-size:.9em}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__paragraph--JUj9D{margin:.5em 0}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__list--xoShA,.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__orderedList--I8Xbv{margin:.5em 0;padding-left:1.5em}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__list--xoShA li,.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__orderedList--I8Xbv li{margin:.2em 0}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__heading1--akExY,.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__heading2--S6zT_,.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__heading3--uDYZP,.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__heading4--TPquY,.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__heading5--aS4KX,.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__heading6--Q6i7T{margin:.75em 0 .5em}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__codeBlock--Jbz9R{margin:.5em 0;padding:.75em}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__blockquote--ClD2c{margin:.5em 0}.MarkdownContent-module__container--ubL5m.MarkdownContent-module__compact--DDV9o .MarkdownContent-module__divider--d_iaV{margin:.75em 0}.MarkdownContent-module__paragraph--JUj9D{margin:.75em 0}.MarkdownContent-module__paragraph--JUj9D:first-child{margin-top:0}.MarkdownContent-module__paragraph--JUj9D:last-child{margin-bottom:0}.MarkdownContent-module__heading1--akExY,.MarkdownContent-module__heading2--S6zT_,.MarkdownContent-module__heading3--uDYZP,.MarkdownContent-module__heading4--TPquY,.MarkdownContent-module__heading5--aS4KX,.MarkdownContent-module__heading6--Q6i7T{margin:1em 0 .5em;font-weight:600;line-height:1.25;color:var(--text-primary)}.MarkdownContent-module__heading1--akExY:first-child,.MarkdownContent-module__heading2--S6zT_:first-child,.MarkdownContent-module__heading3--uDYZP:first-child,.MarkdownContent-module__heading4--TPquY:first-child,.MarkdownContent-module__heading5--aS4KX:first-child,.MarkdownContent-module__heading6--Q6i7T:first-child{margin-top:0}.MarkdownContent-module__heading1--akExY{font-size:1.5em}.MarkdownContent-module__heading2--S6zT_{font-size:1.3em}.MarkdownContent-module__heading3--uDYZP{font-size:1.1em}.MarkdownContent-module__heading4--TPquY,.MarkdownContent-module__heading5--aS4KX,.MarkdownContent-module__heading6--Q6i7T{font-size:1em}.MarkdownContent-module__list--xoShA,.MarkdownContent-module__orderedList--I8Xbv{margin:.75em 0;padding-left:1.75em;color:var(--text-primary)}.MarkdownContent-module__list--xoShA li,.MarkdownContent-module__orderedList--I8Xbv li{margin:.3em 0;color:var(--text-primary)}.MarkdownContent-module__list--xoShA ul,.MarkdownContent-module__list--xoShA ol,.MarkdownContent-module__orderedList--I8Xbv ul,.MarkdownContent-module__orderedList--I8Xbv ol{margin:.3em 0}.MarkdownContent-module__taskCheckbox--xNpAS{margin-right:.5em;vertical-align:middle;cursor:default}.MarkdownContent-module__inlineCode--ryDGE{background:var(--background-secondary);border-radius:3px;padding:.1em .3em;font-family:"SF Mono",Monaco,"Cascadia Code","Roboto Mono",Consolas,"Courier New",monospace;font-size:.9em;color:var(--text-primary)}.MarkdownContent-module__codeBlock--Jbz9R{background:var(--background-secondary);border-radius:5px;padding:1em;overflow-x:auto;margin:.5em 0}.MarkdownContent-module__codeBlock--Jbz9R::-webkit-scrollbar{width:6px}.MarkdownContent-module__codeBlock--Jbz9R::-webkit-scrollbar-track{background:var(--scrollbar-track)}.MarkdownContent-module__codeBlock--Jbz9R::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.MarkdownContent-module__codeBlock--Jbz9R::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.MarkdownContent-module__codeBlock--Jbz9R code{background:none;padding:0;font-size:.85em;line-height:1.5;color:var(--text-primary)}.MarkdownContent-module__link--U9nZL{color:var(--primary);text-decoration:none}.MarkdownContent-module__link--U9nZL:hover{text-decoration:underline;opacity:.8}.MarkdownContent-module__blockquote--ClD2c{border-left:3px solid var(--primary);padding-left:1em;margin:.5em 0;color:var(--text-secondary)}.MarkdownContent-module__tableWrapper--c7gqX{overflow-x:auto;margin:.5em 0}.MarkdownContent-module__tableWrapper--c7gqX::-webkit-scrollbar{width:6px}.MarkdownContent-module__tableWrapper--c7gqX::-webkit-scrollbar-track{background:var(--scrollbar-track)}.MarkdownContent-module__tableWrapper--c7gqX::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.MarkdownContent-module__tableWrapper--c7gqX::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.MarkdownContent-module__table--ry5xt{width:100%;border-collapse:collapse;min-width:100%}.MarkdownContent-module__table--ry5xt th,.MarkdownContent-module__table--ry5xt td{padding:.5rem;border:1px solid var(--surface-primary);text-align:left;color:var(--text-primary)}.MarkdownContent-module__table--ry5xt th{background:var(--background-secondary);font-weight:600}.MarkdownContent-module__table--ry5xt tr:hover{background:var(--surface-hover)}.MarkdownContent-module__divider--d_iaV{border:none;border-top:1px solid var(--surface-primary);margin:1em 0}.MarkdownContent-module__processingText--JT0iv{color:var(--text-muted);font-size:.9em}.MarkdownContent-module__markdownContent--IZ4xe{color:var(--text-primary) !important;line-height:1.6}.MarkdownContent-module__markdownContent--IZ4xe *{color:inherit !important}.MarkdownContent-module__markdownContent--IZ4xe h1,.MarkdownContent-module__markdownContent--IZ4xe h2,.MarkdownContent-module__markdownContent--IZ4xe h3,.MarkdownContent-module__markdownContent--IZ4xe h4,.MarkdownContent-module__markdownContent--IZ4xe h5,.MarkdownContent-module__markdownContent--IZ4xe h6{font-weight:600;margin-top:1em;margin-bottom:.5em;color:var(--text-primary) !important}.MarkdownContent-module__markdownContent--IZ4xe h1{font-size:1.5rem}.MarkdownContent-module__markdownContent--IZ4xe h2{font-size:1.25rem}.MarkdownContent-module__markdownContent--IZ4xe h3{font-size:1.1rem}.MarkdownContent-module__markdownContent--IZ4xe h4,.MarkdownContent-module__markdownContent--IZ4xe h5,.MarkdownContent-module__markdownContent--IZ4xe h6{font-size:1rem}.MarkdownContent-module__markdownContent--IZ4xe p{margin-bottom:.75em;color:var(--text-primary) !important}.MarkdownContent-module__markdownContent--IZ4xe ul,.MarkdownContent-module__markdownContent--IZ4xe ol{margin-bottom:.75em;padding-left:1.5em;color:var(--text-primary) !important}.MarkdownContent-module__markdownContent--IZ4xe li{margin-bottom:.25em;color:var(--text-primary) !important}.MarkdownContent-module__markdownContent--IZ4xe code{background:var(--background-secondary);padding:.125em .25em;border-radius:6px;font-family:"SF Mono",Monaco,"Cascadia Code","Roboto Mono",Consolas,"Courier New",monospace;font-size:.875em;color:var(--text-primary) !important}.MarkdownContent-module__markdownContent--IZ4xe pre{background:var(--background-secondary);padding:.75rem;border-radius:8px;overflow-x:auto;margin-bottom:.75em}.MarkdownContent-module__markdownContent--IZ4xe pre::-webkit-scrollbar{width:6px}.MarkdownContent-module__markdownContent--IZ4xe pre::-webkit-scrollbar-track{background:var(--scrollbar-track)}.MarkdownContent-module__markdownContent--IZ4xe pre::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.MarkdownContent-module__markdownContent--IZ4xe pre::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.MarkdownContent-module__markdownContent--IZ4xe pre code{background:none;padding:0;color:var(--text-primary) !important}.MarkdownContent-module__markdownContent--IZ4xe a{color:var(--primary) !important;text-decoration:none}.MarkdownContent-module__markdownContent--IZ4xe a:hover{text-decoration:underline}.MarkdownContent-module__markdownContent--IZ4xe blockquote{border-left:3px solid var(--primary);padding-left:.75rem;margin-left:0;margin-bottom:.75em;color:var(--text-secondary) !important}.MarkdownContent-module__markdownContent--IZ4xe table{width:100%;border-collapse:collapse;margin-bottom:.75em}.MarkdownContent-module__markdownContent--IZ4xe th,.MarkdownContent-module__markdownContent--IZ4xe td{padding:.5rem;border:1px solid var(--surface-primary);text-align:left;color:var(--text-primary) !important}.MarkdownContent-module__markdownContent--IZ4xe th{background:var(--background-secondary);font-weight:600}.MarkdownContent-module__markdownContent--IZ4xe hr{border:none;border-top:1px solid var(--surface-primary);margin:1em 0}.MarkdownContent-module__markdownContent--IZ4xe strong{font-weight:600;color:var(--text-primary) !important}.MarkdownContent-module__markdownContent--IZ4xe em{font-style:italic;color:var(--text-primary) !important}.MarkdownContent-module__plainContent--fFUjw{color:var(--text-primary) !important;white-space:pre-wrap;word-break:break-word;line-height:1.6}',""]),i.locals={"nxtscape-sidepanel":"MarkdownContent-module__nxtscape-sidepanel--W7cDm",scrollable:"MarkdownContent-module__scrollable--yA_Tz","processing-spinner":"MarkdownContent-module__processing-spinner--AwZ8d",spin:"MarkdownContent-module__spin--scmx9","status-indicator--connected":"MarkdownContent-module__status-indicator--connected--FxHET","status-indicator--disconnected":"MarkdownContent-module__status-indicator--disconnected--PdYzf","status-indicator--processing":"MarkdownContent-module__status-indicator--processing--HF0Xx",container:"MarkdownContent-module__container--ubL5m",processing:"MarkdownContent-module__processing--s28dx",plainText:"MarkdownContent-module__plainText--IMq1z",markdown:"MarkdownContent-module__markdown--DHmLo",compact:"MarkdownContent-module__compact--DDV9o",paragraph:"MarkdownContent-module__paragraph--JUj9D",list:"MarkdownContent-module__list--xoShA",orderedList:"MarkdownContent-module__orderedList--I8Xbv",heading1:"MarkdownContent-module__heading1--akExY",heading2:"MarkdownContent-module__heading2--S6zT_",heading3:"MarkdownContent-module__heading3--uDYZP",heading4:"MarkdownContent-module__heading4--TPquY",heading5:"MarkdownContent-module__heading5--aS4KX",heading6:"MarkdownContent-module__heading6--Q6i7T",codeBlock:"MarkdownContent-module__codeBlock--Jbz9R",blockquote:"MarkdownContent-module__blockquote--ClD2c",divider:"MarkdownContent-module__divider--d_iaV",taskCheckbox:"MarkdownContent-module__taskCheckbox--xNpAS",inlineCode:"MarkdownContent-module__inlineCode--ryDGE",link:"MarkdownContent-module__link--U9nZL",tableWrapper:"MarkdownContent-module__tableWrapper--c7gqX",table:"MarkdownContent-module__table--ry5xt",processingText:"MarkdownContent-module__processingText--JT0iv",markdownContent:"MarkdownContent-module__markdownContent--IZ4xe",plainContent:"MarkdownContent-module__plainContent--fFUjw",pulse:"MarkdownContent-module__pulse--bCScw",slideIn:"MarkdownContent-module__slideIn--hVspc"};const l=i},2162:(e,t,n)=>{var r=n(6540),a=n(9888);var o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=a.useSyncExternalStore,l=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,a){var d=l(null);if(null===d.current){var p={hasValue:!1,value:null};d.current=p}else p=d.current;d=c(function(){function e(e){if(!s){if(s=!0,i=e,e=r(e),void 0!==a&&p.hasValue){var t=p.value;if(a(t,e))return l=t}return l=e}if(t=l,o(i,e))return t;var n=r(e);return void 0!==a&&a(t,n)?(i=e,t):(i=e,l=n)}var i,l,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,n,r,a]);var m=i(e,d[0],d[1]);return s(function(){p.hasValue=!0,p.value=m},[m]),u(m),m}},2551:(e,t,n)=>{var r=n(6540),a=n(9982);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function s(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(l[e]=t,e=0;e<t.length;e++)i.add(t[e])}var u=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),d=Object.prototype.hasOwnProperty,p=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,m={},f={};function g(e,t,n,r,a,o,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var h={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){h[e]=new g(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];h[t]=new g(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){h[e]=new g(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){h[e]=new g(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){h[e]=new g(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){h[e]=new g(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){h[e]=new g(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){h[e]=new g(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){h[e]=new g(e,5,!1,e.toLowerCase(),null,!1,!1)});var b=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function v(e,t,n,r){var a=h.hasOwnProperty(t)?h[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(f,e)||!d.call(m,e)&&(p.test(e)?f[e]=!0:(m[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(b,y);h[t]=new g(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(b,y);h[t]=new g(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(b,y);h[t]=new g(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){h[e]=new g(e,1,!1,e.toLowerCase(),null,!1,!1)}),h.xlinkHref=new g("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){h[e]=new g(e,1,!1,e.toLowerCase(),null,!0,!0)});var _=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),k=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),S=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),T=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),M=Symbol.for("react.suspense"),I=Symbol.for("react.suspense_list"),N=Symbol.for("react.memo"),D=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var L=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var z=Symbol.iterator;function O(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=z&&e[z]||e["@@iterator"])?e:null}var A,R=Object.assign;function F(e){if(void 0===A)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);A=t&&t[1]||""}return"\n"+A+e}var B=!1;function H(e,t){if(!e||B)return"";B=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}else{try{throw Error()}catch(e){r=e}e()}}catch(t){if(t&&r&&"string"==typeof t.stack){for(var a=t.stack.split("\n"),o=r.stack.split("\n"),i=a.length-1,l=o.length-1;1<=i&&0<=l&&a[i]!==o[l];)l--;for(;1<=i&&0<=l;i--,l--)if(a[i]!==o[l]){if(1!==i||1!==l)do{if(i--,0>--l||a[i]!==o[l]){var s="\n"+a[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=l);break}}}finally{B=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function U(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=H(e.type,!1);case 11:return e=H(e.type.render,!1);case 1:return e=H(e.type,!0);default:return""}}function j(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case x:return"Fragment";case k:return"Portal";case C:return"Profiler";case S:return"StrictMode";case M:return"Suspense";case I:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case T:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case N:return null!==(t=e.displayName||null)?t:j(e.type)||"Memo";case D:t=e._payload,e=e._init;try{return j(e(t))}catch(e){}}return null}function Z(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return j(t);case 8:return t===S?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function $(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function V(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function W(e){e._valueTracker||(e._valueTracker=function(e){var t=V(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function K(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=V(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function G(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Y(e,t){var n=t.checked;return R({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Q(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=$(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function q(e,t){null!=(t=t.checked)&&v(e,"checked",t,!1)}function X(e,t){q(e,t);var n=$(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,$(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&G(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+$(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return R({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:$(n)}}function oe(e,t){var n=$(t.value),r=$(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue=function(e){return"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,a){MSApp.execUnsafeLocalFunction(function(){return e(t,n)})}:e}(function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function de(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function fe(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=fe(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){me.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var he=R({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function be(e,t){if(t){if(he[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(o(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ve=null;function _e(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var we=null,ke=null,xe=null;function Se(e){if(e=ya(e)){if("function"!=typeof we)throw Error(o(280));var t=e.stateNode;t&&(t=_a(t),we(e.stateNode,e.type,t))}}function Ce(e){ke?xe?xe.push(e):xe=[e]:ke=e}function Ee(){if(ke){var e=ke,t=xe;if(xe=ke=null,Se(e),t)for(e=0;e<t.length;e++)Se(t[e])}}function Te(e,t){return e(t)}function Pe(){}var Me=!1;function Ie(e,t,n){if(Me)return e(t,n);Me=!0;try{return Te(e,t,n)}finally{Me=!1,(null!==ke||null!==xe)&&(Pe(),Ee())}}function Ne(e,t){var n=e.stateNode;if(null===n)return null;var r=_a(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(o(231,t,typeof n));return n}var De=!1;if(u)try{var Le={};Object.defineProperty(Le,"passive",{get:function(){De=!0}}),window.addEventListener("test",Le,Le),window.removeEventListener("test",Le,Le)}catch(e){De=!1}function ze(e,t,n,r,a,o,i,l,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(e){this.onError(e)}}var Oe=!1,Ae=null,Re=!1,Fe=null,Be={onError:function(e){Oe=!0,Ae=e}};function He(e,t,n,r,a,o,i,l,s){Oe=!1,Ae=null,ze.apply(Be,arguments)}function Ue(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function je(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ze(e){if(Ue(e)!==e)throw Error(o(188))}function $e(e){return e=function(e){var t=e.alternate;if(!t){if(null===(t=Ue(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var i=a.alternate;if(null===i){if(null!==(r=a.return)){n=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===n)return Ze(a),e;if(i===r)return Ze(a),t;i=i.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=i;else{for(var l=!1,s=a.child;s;){if(s===n){l=!0,n=a,r=i;break}if(s===r){l=!0,r=a,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=a;break}if(s===r){l=!0,r=i,n=a;break}s=s.sibling}if(!l)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e),null!==e?Ve(e):null}function Ve(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ve(e);if(null!==t)return t;e=e.sibling}return null}var We=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,Ge=a.unstable_shouldYield,Ye=a.unstable_requestPaint,Qe=a.unstable_now,qe=a.unstable_getCurrentPriorityLevel,Xe=a.unstable_ImmediatePriority,Je=a.unstable_UserBlockingPriority,et=a.unstable_NormalPriority,tt=a.unstable_LowPriority,nt=a.unstable_IdlePriority,rt=null,at=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(it(e)/lt|0)|0},it=Math.log,lt=Math.LN2;var st=64,ct=4194304;function ut(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function dt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,i=268435455&n;if(0!==i){var l=i&~a;0!==l?r=ut(l):0!==(o&=i)&&(r=ut(o))}else 0!==(i=n&~a)?r=ut(i):0!==o&&(r=ut(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&4194240&o))return t;if(4&r&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ft(){var e=st;return!(4194240&(st<<=1))&&(st=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ht(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function bt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var yt=0;function vt(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var _t,wt,kt,xt,St,Ct=!1,Et=[],Tt=null,Pt=null,Mt=null,It=new Map,Nt=new Map,Dt=[],Lt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zt(e,t){switch(e){case"focusin":case"focusout":Tt=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":Mt=null;break;case"pointerover":case"pointerout":It.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Nt.delete(t.pointerId)}}function Ot(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=ya(t))&&wt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function At(e){var t=ba(e.target);if(null!==t){var n=Ue(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=je(n)))return e.blockedOn=t,void St(e.priority,function(){kt(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Rt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ya(n))&&wt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);ve=r,n.target.dispatchEvent(r),ve=null,t.shift()}return!0}function Ft(e,t,n){Rt(e)&&n.delete(t)}function Bt(){Ct=!1,null!==Tt&&Rt(Tt)&&(Tt=null),null!==Pt&&Rt(Pt)&&(Pt=null),null!==Mt&&Rt(Mt)&&(Mt=null),It.forEach(Ft),Nt.forEach(Ft)}function Ht(e,t){e.blockedOn===t&&(e.blockedOn=null,Ct||(Ct=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Bt)))}function Ut(e){function t(t){return Ht(t,e)}if(0<Et.length){Ht(Et[0],e);for(var n=1;n<Et.length;n++){var r=Et[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Tt&&Ht(Tt,e),null!==Pt&&Ht(Pt,e),null!==Mt&&Ht(Mt,e),It.forEach(t),Nt.forEach(t),n=0;n<Dt.length;n++)(r=Dt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Dt.length&&null===(n=Dt[0]).blockedOn;)At(n),null===n.blockedOn&&Dt.shift()}var jt=_.ReactCurrentBatchConfig,Zt=!0;function $t(e,t,n,r){var a=yt,o=jt.transition;jt.transition=null;try{yt=1,Wt(e,t,n,r)}finally{yt=a,jt.transition=o}}function Vt(e,t,n,r){var a=yt,o=jt.transition;jt.transition=null;try{yt=4,Wt(e,t,n,r)}finally{yt=a,jt.transition=o}}function Wt(e,t,n,r){if(Zt){var a=Gt(e,t,n,r);if(null===a)Zr(e,t,r,Kt,n),zt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Tt=Ot(Tt,e,t,n,r,a),!0;case"dragenter":return Pt=Ot(Pt,e,t,n,r,a),!0;case"mouseover":return Mt=Ot(Mt,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return It.set(o,Ot(It.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,Nt.set(o,Ot(Nt.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(zt(e,r),4&t&&-1<Lt.indexOf(e)){for(;null!==a;){var o=ya(a);if(null!==o&&_t(o),null===(o=Gt(e,t,n,r))&&Zr(e,t,r,Kt,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Zr(e,t,r,null,n)}}var Kt=null;function Gt(e,t,n,r){if(Kt=null,null!==(e=ba(e=_e(r))))if(null===(t=Ue(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=je(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Yt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(qe()){case Xe:return 1;case Je:return 4;case et:case tt:return 16;case nt:return 536870912;default:return 16}default:return 16}}var Qt=null,qt=null,Xt=null;function Jt(){if(Xt)return Xt;var e,t,n=qt,r=n.length,a="value"in Qt?Qt.value:Qt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[o-t];t++);return Xt=a.slice(e,1<t?1-t:void 0)}function en(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function tn(){return!0}function nn(){return!1}function rn(e){function t(t,n,r,a,o){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?tn:nn,this.isPropagationStopped=nn,this}return R(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=tn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=tn)},persist:function(){},isPersistent:tn}),t}var an,on,ln,sn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=rn(sn),un=R({},sn,{view:0,detail:0}),dn=rn(un),pn=R({},un,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Sn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(an=e.screenX-ln.screenX,on=e.screenY-ln.screenY):on=an=0,ln=e),an)},movementY:function(e){return"movementY"in e?e.movementY:on}}),mn=rn(pn),fn=rn(R({},pn,{dataTransfer:0})),gn=rn(R({},un,{relatedTarget:0})),hn=rn(R({},sn,{animationName:0,elapsedTime:0,pseudoElement:0})),bn=R({},sn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),yn=rn(bn),vn=rn(R({},sn,{data:0})),_n={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function xn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function Sn(){return xn}var Cn=R({},un,{key:function(e){if(e.key){var t=_n[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=en(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?wn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Sn,charCode:function(e){return"keypress"===e.type?en(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?en(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),En=rn(Cn),Tn=rn(R({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pn=rn(R({},un,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Sn})),Mn=rn(R({},sn,{propertyName:0,elapsedTime:0,pseudoElement:0})),In=R({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Nn=rn(In),Dn=[9,13,27,32],Ln=u&&"CompositionEvent"in window,zn=null;u&&"documentMode"in document&&(zn=document.documentMode);var On=u&&"TextEvent"in window&&!zn,An=u&&(!Ln||zn&&8<zn&&11>=zn),Rn=String.fromCharCode(32),Fn=!1;function Bn(e,t){switch(e){case"keyup":return-1!==Dn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Hn(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Un=!1;var jn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Zn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!jn[e.type]:"textarea"===t}function $n(e,t,n,r){Ce(r),0<(t=Vr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Vn=null,Wn=null;function Kn(e){Rr(e,0)}function Gn(e){if(K(va(e)))return e}function Yn(e,t){if("change"===e)return t}var Qn=!1;if(u){var qn;if(u){var Xn="oninput"in document;if(!Xn){var Jn=document.createElement("div");Jn.setAttribute("oninput","return;"),Xn="function"==typeof Jn.oninput}qn=Xn}else qn=!1;Qn=qn&&(!document.documentMode||9<document.documentMode)}function er(){Vn&&(Vn.detachEvent("onpropertychange",tr),Wn=Vn=null)}function tr(e){if("value"===e.propertyName&&Gn(Wn)){var t=[];$n(t,Wn,e,_e(e)),Ie(Kn,t)}}function nr(e,t,n){"focusin"===e?(er(),Wn=n,(Vn=t).attachEvent("onpropertychange",tr)):"focusout"===e&&er()}function rr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Gn(Wn)}function ar(e,t){if("click"===e)return Gn(t)}function or(e,t){if("input"===e||"change"===e)return Gn(t)}var ir="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function lr(e,t){if(ir(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ir(e[a],t[a]))return!1}return!0}function sr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=sr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=sr(r)}}function ur(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?ur(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function dr(){for(var e=window,t=G();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=G((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=dr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ur(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=cr(n,o);var i=cr(n,r);a&&i&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var fr=u&&"documentMode"in document&&11>=document.documentMode,gr=null,hr=null,br=null,yr=!1;function vr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;yr||null==gr||gr!==G(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},br&&lr(br,r)||(br=r,0<(r=Vr(hr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function _r(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var wr={animationend:_r("Animation","AnimationEnd"),animationiteration:_r("Animation","AnimationIteration"),animationstart:_r("Animation","AnimationStart"),transitionend:_r("Transition","TransitionEnd")},kr={},xr={};function Sr(e){if(kr[e])return kr[e];if(!wr[e])return e;var t,n=wr[e];for(t in n)if(n.hasOwnProperty(t)&&t in xr)return kr[e]=n[t];return e}u&&(xr=document.createElement("div").style,"AnimationEvent"in window||(delete wr.animationend.animation,delete wr.animationiteration.animation,delete wr.animationstart.animation),"TransitionEvent"in window||delete wr.transitionend.transition);var Cr=Sr("animationend"),Er=Sr("animationiteration"),Tr=Sr("animationstart"),Pr=Sr("transitionend"),Mr=new Map,Ir="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Nr(e,t){Mr.set(e,t),s(t,[e])}for(var Dr=0;Dr<Ir.length;Dr++){var Lr=Ir[Dr];Nr(Lr.toLowerCase(),"on"+(Lr[0].toUpperCase()+Lr.slice(1)))}Nr(Cr,"onAnimationEnd"),Nr(Er,"onAnimationIteration"),Nr(Tr,"onAnimationStart"),Nr("dblclick","onDoubleClick"),Nr("focusin","onFocus"),Nr("focusout","onBlur"),Nr(Pr,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Or=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function Ar(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,i,l,s,c){if(He.apply(this,arguments),Oe){if(!Oe)throw Error(o(198));var u=Ae;Oe=!1,Ae=null,Re||(Re=!0,Fe=u)}}(r,t,void 0,e),e.currentTarget=null}function Rr(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,c=l.currentTarget;if(l=l.listener,s!==o&&a.isPropagationStopped())break e;Ar(a,l,c),o=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,c=l.currentTarget,l=l.listener,s!==o&&a.isPropagationStopped())break e;Ar(a,l,c),o=s}}}if(Re)throw e=Fe,Re=!1,Fe=null,e}function Fr(e,t){var n=t[fa];void 0===n&&(n=t[fa]=new Set);var r=e+"__bubble";n.has(r)||(jr(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),jr(n,e,r,t)}var Hr="_reactListening"+Math.random().toString(36).slice(2);function Ur(e){if(!e[Hr]){e[Hr]=!0,i.forEach(function(t){"selectionchange"!==t&&(Or.has(t)||Br(t,!1,e),Br(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Hr]||(t[Hr]=!0,Br("selectionchange",!1,t))}}function jr(e,t,n,r){switch(Yt(t)){case 1:var a=$t;break;case 4:a=Vt;break;default:a=Wt}n=a.bind(null,t,n,e),a=void 0,!De||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Zr(e,t,n,r,a){var o=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===a||8===l.nodeType&&l.parentNode===a)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;i=i.return}for(;null!==l;){if(null===(i=ba(l)))return;if(5===(s=i.tag)||6===s){r=o=i;continue e}l=l.parentNode}}r=r.return}Ie(function(){var r=o,a=_e(n),i=[];e:{var l=Mr.get(e);if(void 0!==l){var s=cn,c=e;switch(e){case"keypress":if(0===en(n))break e;case"keydown":case"keyup":s=En;break;case"focusin":c="focus",s=gn;break;case"focusout":c="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=fn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Pn;break;case Cr:case Er:case Tr:s=hn;break;case Pr:s=Mn;break;case"scroll":s=dn;break;case"wheel":s=Nn;break;case"copy":case"cut":case"paste":s=yn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Tn}var u=!!(4&t),d=!u&&"scroll"===e,p=u?null!==l?l+"Capture":null:l;u=[];for(var m,f=r;null!==f;){var g=(m=f).stateNode;if(5===m.tag&&null!==g&&(m=g,null!==p&&(null!=(g=Ne(f,p))&&u.push($r(f,g,m)))),d)break;f=f.return}0<u.length&&(l=new s(l,c,null,n,a),i.push({event:l,listeners:u}))}}if(!(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===ve||!(c=n.relatedTarget||n.fromElement)||!ba(c)&&!c[ma])&&(s||l)&&(l=a.window===a?a:(l=a.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?ba(c):null)&&(c!==(d=Ue(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=r),s!==c)){if(u=mn,g="onMouseLeave",p="onMouseEnter",f="mouse","pointerout"!==e&&"pointerover"!==e||(u=Tn,g="onPointerLeave",p="onPointerEnter",f="pointer"),d=null==s?l:va(s),m=null==c?l:va(c),(l=new u(g,f+"leave",s,n,a)).target=d,l.relatedTarget=m,g=null,ba(a)===r&&((u=new u(p,f+"enter",c,n,a)).target=m,u.relatedTarget=d,g=u),d=g,s&&c)e:{for(p=c,f=0,m=u=s;m;m=Wr(m))f++;for(m=0,g=p;g;g=Wr(g))m++;for(;0<f-m;)u=Wr(u),f--;for(;0<m-f;)p=Wr(p),m--;for(;f--;){if(u===p||null!==p&&u===p.alternate)break e;u=Wr(u),p=Wr(p)}u=null}else u=null;null!==s&&Kr(i,l,s,u,!1),null!==c&&null!==d&&Kr(i,d,c,u,!0)}if("select"===(s=(l=r?va(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var h=Yn;else if(Zn(l))if(Qn)h=or;else{h=rr;var b=nr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(h=ar);switch(h&&(h=h(e,r))?$n(i,h,n,a):(b&&b(e,l,r),"focusout"===e&&(b=l._wrapperState)&&b.controlled&&"number"===l.type&&ee(l,"number",l.value)),b=r?va(r):window,e){case"focusin":(Zn(b)||"true"===b.contentEditable)&&(gr=b,hr=r,br=null);break;case"focusout":br=hr=gr=null;break;case"mousedown":yr=!0;break;case"contextmenu":case"mouseup":case"dragend":yr=!1,vr(i,n,a);break;case"selectionchange":if(fr)break;case"keydown":case"keyup":vr(i,n,a)}var y;if(Ln)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else Un?Bn(e,n)&&(v="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(v="onCompositionStart");v&&(An&&"ko"!==n.locale&&(Un||"onCompositionStart"!==v?"onCompositionEnd"===v&&Un&&(y=Jt()):(qt="value"in(Qt=a)?Qt.value:Qt.textContent,Un=!0)),0<(b=Vr(r,v)).length&&(v=new vn(v,e,null,n,a),i.push({event:v,listeners:b}),y?v.data=y:null!==(y=Hn(n))&&(v.data=y))),(y=On?function(e,t){switch(e){case"compositionend":return Hn(t);case"keypress":return 32!==t.which?null:(Fn=!0,Rn);case"textInput":return(e=t.data)===Rn&&Fn?null:e;default:return null}}(e,n):function(e,t){if(Un)return"compositionend"===e||!Ln&&Bn(e,t)?(e=Jt(),Xt=qt=Qt=null,Un=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return An&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Vr(r,"onBeforeInput")).length&&(a=new vn("onBeforeInput","beforeinput",null,n,a),i.push({event:a,listeners:r}),a.data=y))}Rr(i,t)})}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Vr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=Ne(e,n))&&r.unshift($r(e,o,a)),null!=(o=Ne(e,t))&&r.push($r(e,o,a))),e=e.return}return r}function Wr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var o=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,c=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==c&&(l=c,a?null!=(s=Ne(n,o))&&i.unshift($r(n,s,l)):a||null!=(s=Ne(n,o))&&i.push($r(n,s,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Gr=/\r\n?/g,Yr=/\u0000|\uFFFD/g;function Qr(e){return("string"==typeof e?e:""+e).replace(Gr,"\n").replace(Yr,"")}function qr(e,t,n){if(t=Qr(t),Qr(e)!==t&&n)throw Error(o(425))}function Xr(){}var Jr=null,ea=null;function ta(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var na="function"==typeof setTimeout?setTimeout:void 0,ra="function"==typeof clearTimeout?clearTimeout:void 0,aa="function"==typeof Promise?Promise:void 0,oa="function"==typeof queueMicrotask?queueMicrotask:void 0!==aa?function(e){return aa.resolve(null).then(e).catch(ia)}:na;function ia(e){setTimeout(function(){throw e})}function la(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Ut(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Ut(t)}function sa(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var ua=Math.random().toString(36).slice(2),da="__reactFiber$"+ua,pa="__reactProps$"+ua,ma="__reactContainer$"+ua,fa="__reactEvents$"+ua,ga="__reactListeners$"+ua,ha="__reactHandles$"+ua;function ba(e){var t=e[da];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ma]||n[da]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[da])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ya(e){return!(e=e[da]||e[ma])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function va(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function _a(e){return e[pa]||null}var wa=[],ka=-1;function xa(e){return{current:e}}function Sa(e){0>ka||(e.current=wa[ka],wa[ka]=null,ka--)}function Ca(e,t){ka++,wa[ka]=e.current,e.current=t}var Ea={},Ta=xa(Ea),Pa=xa(!1),Ma=Ea;function Ia(e,t){var n=e.type.contextTypes;if(!n)return Ea;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Na(e){return null!=(e=e.childContextTypes)}function Da(){Sa(Pa),Sa(Ta)}function La(e,t,n){if(Ta.current!==Ea)throw Error(o(168));Ca(Ta,t),Ca(Pa,n)}function za(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(o(108,Z(e)||"Unknown",a));return R({},n,r)}function Oa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ea,Ma=Ta.current,Ca(Ta,e),Ca(Pa,Pa.current),!0}function Aa(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=za(e,t,Ma),r.__reactInternalMemoizedMergedChildContext=e,Sa(Pa),Sa(Ta),Ca(Ta,e)):Sa(Pa),Ca(Pa,n)}var Ra=null,Fa=!1,Ba=!1;function Ha(e){null===Ra?Ra=[e]:Ra.push(e)}function Ua(){if(!Ba&&null!==Ra){Ba=!0;var e=0,t=yt;try{var n=Ra;for(yt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ra=null,Fa=!1}catch(t){throw null!==Ra&&(Ra=Ra.slice(e+1)),We(Xe,Ua),t}finally{yt=t,Ba=!1}}return null}var ja=[],Za=0,$a=null,Va=0,Wa=[],Ka=0,Ga=null,Ya=1,Qa="";function qa(e,t){ja[Za++]=Va,ja[Za++]=$a,$a=e,Va=t}function Xa(e,t,n){Wa[Ka++]=Ya,Wa[Ka++]=Qa,Wa[Ka++]=Ga,Ga=e;var r=Ya;e=Qa;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var o=32-ot(t)+a;if(30<o){var i=a-a%5;o=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Ya=1<<32-ot(t)+a|n<<a|r,Qa=o+e}else Ya=1<<o|n<<a|r,Qa=e}function Ja(e){null!==e.return&&(qa(e,1),Xa(e,1,0))}function eo(e){for(;e===$a;)$a=ja[--Za],ja[Za]=null,Va=ja[--Za],ja[Za]=null;for(;e===Ga;)Ga=Wa[--Ka],Wa[Ka]=null,Qa=Wa[--Ka],Wa[Ka]=null,Ya=Wa[--Ka],Wa[Ka]=null}var to=null,no=null,ro=!1,ao=null;function oo(e,t){var n=Mc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function io(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,to=e,no=sa(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,to=e,no=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ga?{id:Ya,overflow:Qa}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Mc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,to=e,no=null,!0);default:return!1}}function lo(e){return!(!(1&e.mode)||128&e.flags)}function so(e){if(ro){var t=no;if(t){var n=t;if(!io(e,t)){if(lo(e))throw Error(o(418));t=sa(n.nextSibling);var r=to;t&&io(e,t)?oo(r,n):(e.flags=-4097&e.flags|2,ro=!1,to=e)}}else{if(lo(e))throw Error(o(418));e.flags=-4097&e.flags|2,ro=!1,to=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;to=e}function uo(e){if(e!==to)return!1;if(!ro)return co(e),ro=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ta(e.type,e.memoizedProps)),t&&(t=no)){if(lo(e))throw po(),Error(o(418));for(;t;)oo(e,t),t=sa(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){no=sa(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}no=null}}else no=to?sa(e.stateNode.nextSibling):null;return!0}function po(){for(var e=no;e;)e=sa(e.nextSibling)}function mo(){no=to=null,ro=!1}function fo(e){null===ao?ao=[e]:ao.push(e)}var go=_.ReactCurrentBatchConfig;function ho(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var a=r,i=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=a.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!=typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function bo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yo(e){return(0,e._init)(e._payload)}function vo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Nc(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Oc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var o=n.type;return o===x?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"==typeof o&&null!==o&&o.$$typeof===D&&yo(o)===t.type)?((r=a(t,n.props)).ref=ho(e,t,n),r.return=e,r):((r=Dc(n.type,n.key,n.props,null,e.mode,r)).ref=ho(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Ac(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Lc(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function p(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Oc(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Dc(t.type,t.key,t.props,null,e.mode,n)).ref=ho(e,null,t),n.return=e,n;case k:return(t=Ac(t,e.mode,n)).return=e,t;case D:return p(e,(0,t._init)(t._payload),n)}if(te(t)||O(t))return(t=Lc(t,e.mode,n,null)).return=e,t;bo(e,t)}return null}function m(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==a?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?c(e,t,n,r):null;case k:return n.key===a?u(e,t,n,r):null;case D:return m(e,t,(a=n._init)(n._payload),r)}if(te(n)||O(n))return null!==a?null:d(e,t,n,r,null);bo(e,n)}return null}function f(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case D:return f(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||O(r))return d(t,e=e.get(n)||null,r,a,null);bo(t,r)}return null}function g(a,o,l,s){for(var c=null,u=null,d=o,g=o=0,h=null;null!==d&&g<l.length;g++){d.index>g?(h=d,d=null):h=d.sibling;var b=m(a,d,l[g],s);if(null===b){null===d&&(d=h);break}e&&d&&null===b.alternate&&t(a,d),o=i(b,o,g),null===u?c=b:u.sibling=b,u=b,d=h}if(g===l.length)return n(a,d),ro&&qa(a,g),c;if(null===d){for(;g<l.length;g++)null!==(d=p(a,l[g],s))&&(o=i(d,o,g),null===u?c=d:u.sibling=d,u=d);return ro&&qa(a,g),c}for(d=r(a,d);g<l.length;g++)null!==(h=f(d,a,g,l[g],s))&&(e&&null!==h.alternate&&d.delete(null===h.key?g:h.key),o=i(h,o,g),null===u?c=h:u.sibling=h,u=h);return e&&d.forEach(function(e){return t(a,e)}),ro&&qa(a,g),c}function h(a,l,s,c){var u=O(s);if("function"!=typeof u)throw Error(o(150));if(null==(s=u.call(s)))throw Error(o(151));for(var d=u=null,g=l,h=l=0,b=null,y=s.next();null!==g&&!y.done;h++,y=s.next()){g.index>h?(b=g,g=null):b=g.sibling;var v=m(a,g,y.value,c);if(null===v){null===g&&(g=b);break}e&&g&&null===v.alternate&&t(a,g),l=i(v,l,h),null===d?u=v:d.sibling=v,d=v,g=b}if(y.done)return n(a,g),ro&&qa(a,h),u;if(null===g){for(;!y.done;h++,y=s.next())null!==(y=p(a,y.value,c))&&(l=i(y,l,h),null===d?u=y:d.sibling=y,d=y);return ro&&qa(a,h),u}for(g=r(a,g);!y.done;h++,y=s.next())null!==(y=f(g,a,h,y.value,c))&&(e&&null!==y.alternate&&g.delete(null===y.key?h:y.key),l=i(y,l,h),null===d?u=y:d.sibling=y,d=y);return e&&g.forEach(function(e){return t(a,e)}),ro&&qa(a,h),u}return function e(r,o,i,s){if("object"==typeof i&&null!==i&&i.type===x&&null===i.key&&(i=i.props.children),"object"==typeof i&&null!==i){switch(i.$$typeof){case w:e:{for(var c=i.key,u=o;null!==u;){if(u.key===c){if((c=i.type)===x){if(7===u.tag){n(r,u.sibling),(o=a(u,i.props.children)).return=r,r=o;break e}}else if(u.elementType===c||"object"==typeof c&&null!==c&&c.$$typeof===D&&yo(c)===u.type){n(r,u.sibling),(o=a(u,i.props)).ref=ho(r,u,i),o.return=r,r=o;break e}n(r,u);break}t(r,u),u=u.sibling}i.type===x?((o=Lc(i.props.children,r.mode,s,i.key)).return=r,r=o):((s=Dc(i.type,i.key,i.props,null,r.mode,s)).ref=ho(r,o,i),s.return=r,r=s)}return l(r);case k:e:{for(u=i.key;null!==o;){if(o.key===u){if(4===o.tag&&o.stateNode.containerInfo===i.containerInfo&&o.stateNode.implementation===i.implementation){n(r,o.sibling),(o=a(o,i.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Ac(i,r.mode,s)).return=r,r=o}return l(r);case D:return e(r,o,(u=i._init)(i._payload),s)}if(te(i))return g(r,o,i,s);if(O(i))return h(r,o,i,s);bo(r,i)}return"string"==typeof i&&""!==i||"number"==typeof i?(i=""+i,null!==o&&6===o.tag?(n(r,o.sibling),(o=a(o,i)).return=r,r=o):(n(r,o),(o=Oc(i,r.mode,s)).return=r,r=o),l(r)):n(r,o)}}var _o=vo(!0),wo=vo(!1),ko=xa(null),xo=null,So=null,Co=null;function Eo(){Co=So=xo=null}function To(e){var t=ko.current;Sa(ko),e._currentValue=t}function Po(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Mo(e,t){xo=e,Co=So=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bl=!0),e.firstContext=null)}function Io(e){var t=e._currentValue;if(Co!==e)if(e={context:e,memoizedValue:t,next:null},null===So){if(null===xo)throw Error(o(308));So=e,xo.dependencies={lanes:0,firstContext:e}}else So=So.next=e;return t}var No=null;function Do(e){null===No?No=[e]:No.push(e)}function Lo(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Do(t)):(n.next=a.next,a.next=n),t.interleaved=n,zo(e,r)}function zo(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Oo=!1;function Ao(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ro(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Fo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Bo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&Es){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,zo(e,n)}return null===(a=r.interleaved)?(t.next=t,Do(r)):(t.next=a.next,a.next=t),r.interleaved=t,zo(e,n)}function Ho(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}function Uo(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=i:o=o.next=i,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function jo(e,t,n,r){var a=e.updateQueue;Oo=!1;var o=a.firstBaseUpdate,i=a.lastBaseUpdate,l=a.shared.pending;if(null!==l){a.shared.pending=null;var s=l,c=s.next;s.next=null,null===i?o=c:i.next=c,i=s;var u=e.alternate;null!==u&&((l=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===l?u.firstBaseUpdate=c:l.next=c,u.lastBaseUpdate=s))}if(null!==o){var d=a.baseState;for(i=0,u=c=s=null,l=o;;){var p=l.lane,m=l.eventTime;if((r&p)===p){null!==u&&(u=u.next={eventTime:m,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var f=e,g=l;switch(p=t,m=n,g.tag){case 1:if("function"==typeof(f=g.payload)){d=f.call(m,d,p);break e}d=f;break e;case 3:f.flags=-65537&f.flags|128;case 0:if(null==(p="function"==typeof(f=g.payload)?f.call(m,d,p):f))break e;d=R({},d,p);break e;case 2:Oo=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(p=a.effects)?a.effects=[l]:p.push(l))}else m={eventTime:m,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===u?(c=u=m,s=d):u=u.next=m,i|=p;if(null===(l=l.next)){if(null===(l=a.shared.pending))break;l=(p=l).next,p.next=null,a.lastBaseUpdate=p,a.shared.pending=null}}if(null===u&&(s=d),a.baseState=s,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{i|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);zs|=i,e.lanes=i,e.memoizedState=d}}function Zo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!=typeof a)throw Error(o(191,a));a.call(r)}}}var $o={},Vo=xa($o),Wo=xa($o),Ko=xa($o);function Go(e){if(e===$o)throw Error(o(174));return e}function Yo(e,t){switch(Ca(Ko,t),Ca(Wo,e),Ca(Vo,$o),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Sa(Vo),Ca(Vo,t)}function Qo(){Sa(Vo),Sa(Wo),Sa(Ko)}function qo(e){Go(Ko.current);var t=Go(Vo.current),n=se(t,e.type);t!==n&&(Ca(Wo,e),Ca(Vo,n))}function Xo(e){Wo.current===e&&(Sa(Vo),Sa(Wo))}var Jo=xa(0);function ei(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ti=[];function ni(){for(var e=0;e<ti.length;e++)ti[e]._workInProgressVersionPrimary=null;ti.length=0}var ri=_.ReactCurrentDispatcher,ai=_.ReactCurrentBatchConfig,oi=0,ii=null,li=null,si=null,ci=!1,ui=!1,di=0,pi=0;function mi(){throw Error(o(321))}function fi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function gi(e,t,n,r,a,i){if(oi=i,ii=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ri.current=null===e||null===e.memoizedState?Xi:Ji,e=n(r,a),ui){i=0;do{if(ui=!1,di=0,25<=i)throw Error(o(301));i+=1,si=li=null,t.updateQueue=null,ri.current=el,e=n(r,a)}while(ui)}if(ri.current=qi,t=null!==li&&null!==li.next,oi=0,si=li=ii=null,ci=!1,t)throw Error(o(300));return e}function hi(){var e=0!==di;return di=0,e}function bi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===si?ii.memoizedState=si=e:si=si.next=e,si}function yi(){if(null===li){var e=ii.alternate;e=null!==e?e.memoizedState:null}else e=li.next;var t=null===si?ii.memoizedState:si.next;if(null!==t)si=t,li=e;else{if(null===e)throw Error(o(310));e={memoizedState:(li=e).memoizedState,baseState:li.baseState,baseQueue:li.baseQueue,queue:li.queue,next:null},null===si?ii.memoizedState=si=e:si=si.next=e}return si}function vi(e,t){return"function"==typeof t?t(e):t}function _i(e){var t=yi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=li,a=r.baseQueue,i=n.pending;if(null!==i){if(null!==a){var l=a.next;a.next=i.next,i.next=l}r.baseQueue=a=i,n.pending=null}if(null!==a){i=a.next,r=r.baseState;var s=l=null,c=null,u=i;do{var d=u.lane;if((oi&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var p={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(s=c=p,l=r):c=c.next=p,ii.lanes|=d,zs|=d}u=u.next}while(null!==u&&u!==i);null===c?l=r:c.next=s,ir(r,t.memoizedState)||(bl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{i=a.lane,ii.lanes|=i,zs|=i,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function wi(e){var t=yi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,i=t.memoizedState;if(null!==a){n.pending=null;var l=a=a.next;do{i=e(i,l.action),l=l.next}while(l!==a);ir(i,t.memoizedState)||(bl=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ki(){}function xi(e,t){var n=ii,r=yi(),a=t(),i=!ir(r.memoizedState,a);if(i&&(r.memoizedState=a,bl=!0),r=r.queue,Oi(Ei.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==si&&1&si.memoizedState.tag){if(n.flags|=2048,Ii(9,Ci.bind(null,n,r,a,t),void 0,null),null===Ts)throw Error(o(349));30&oi||Si(n,t,a)}return a}function Si(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ii.updateQueue)?(t={lastEffect:null,stores:null},ii.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ci(e,t,n,r){t.value=n,t.getSnapshot=r,Ti(t)&&Pi(e)}function Ei(e,t,n){return n(function(){Ti(t)&&Pi(e)})}function Ti(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(e){return!0}}function Pi(e){var t=zo(e,1);null!==t&&ec(t,e,1,-1)}function Mi(e){var t=bi();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:vi,lastRenderedState:e},t.queue=e,e=e.dispatch=Ki.bind(null,ii,e),[t.memoizedState,e]}function Ii(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ii.updateQueue)?(t={lastEffect:null,stores:null},ii.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ni(){return yi().memoizedState}function Di(e,t,n,r){var a=bi();ii.flags|=e,a.memoizedState=Ii(1|t,n,void 0,void 0===r?null:r)}function Li(e,t,n,r){var a=yi();r=void 0===r?null:r;var o=void 0;if(null!==li){var i=li.memoizedState;if(o=i.destroy,null!==r&&fi(r,i.deps))return void(a.memoizedState=Ii(t,n,o,r))}ii.flags|=e,a.memoizedState=Ii(1|t,n,o,r)}function zi(e,t){return Di(8390656,8,e,t)}function Oi(e,t){return Li(2048,8,e,t)}function Ai(e,t){return Li(4,2,e,t)}function Ri(e,t){return Li(4,4,e,t)}function Fi(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Bi(e,t,n){return n=null!=n?n.concat([e]):null,Li(4,4,Fi.bind(null,t,e),n)}function Hi(){}function Ui(e,t){var n=yi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&fi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ji(e,t){var n=yi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&fi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Zi(e,t,n){return 21&oi?(ir(n,t)||(n=ft(),ii.lanes|=n,zs|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,bl=!0),e.memoizedState=n)}function $i(e,t){var n=yt;yt=0!==n&&4>n?n:4,e(!0);var r=ai.transition;ai.transition={};try{e(!1),t()}finally{yt=n,ai.transition=r}}function Vi(){return yi().memoizedState}function Wi(e,t,n){var r=Js(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Gi(e))Yi(t,n);else if(null!==(n=Lo(e,t,n,r))){ec(n,e,r,Xs()),Qi(n,t,r)}}function Ki(e,t,n){var r=Js(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Gi(e))Yi(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=o(i,n);if(a.hasEagerState=!0,a.eagerState=l,ir(l,i)){var s=t.interleaved;return null===s?(a.next=a,Do(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(e){}null!==(n=Lo(e,t,a,r))&&(ec(n,e,r,a=Xs()),Qi(n,t,r))}}function Gi(e){var t=e.alternate;return e===ii||null!==t&&t===ii}function Yi(e,t){ui=ci=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qi(e,t,n){if(4194240&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}var qi={readContext:Io,useCallback:mi,useContext:mi,useEffect:mi,useImperativeHandle:mi,useInsertionEffect:mi,useLayoutEffect:mi,useMemo:mi,useReducer:mi,useRef:mi,useState:mi,useDebugValue:mi,useDeferredValue:mi,useTransition:mi,useMutableSource:mi,useSyncExternalStore:mi,useId:mi,unstable_isNewReconciler:!1},Xi={readContext:Io,useCallback:function(e,t){return bi().memoizedState=[e,void 0===t?null:t],e},useContext:Io,useEffect:zi,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Di(4194308,4,Fi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Di(4194308,4,e,t)},useInsertionEffect:function(e,t){return Di(4,2,e,t)},useMemo:function(e,t){var n=bi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=bi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Wi.bind(null,ii,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},bi().memoizedState=e},useState:Mi,useDebugValue:Hi,useDeferredValue:function(e){return bi().memoizedState=e},useTransition:function(){var e=Mi(!1),t=e[0];return e=$i.bind(null,e[1]),bi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ii,a=bi();if(ro){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===Ts)throw Error(o(349));30&oi||Si(r,t,n)}a.memoizedState=n;var i={value:n,getSnapshot:t};return a.queue=i,zi(Ei.bind(null,r,i,e),[e]),r.flags|=2048,Ii(9,Ci.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=bi(),t=Ts.identifierPrefix;if(ro){var n=Qa;t=":"+t+"R"+(n=(Ya&~(1<<32-ot(Ya)-1)).toString(32)+n),0<(n=di++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Ji={readContext:Io,useCallback:Ui,useContext:Io,useEffect:Oi,useImperativeHandle:Bi,useInsertionEffect:Ai,useLayoutEffect:Ri,useMemo:ji,useReducer:_i,useRef:Ni,useState:function(){return _i(vi)},useDebugValue:Hi,useDeferredValue:function(e){return Zi(yi(),li.memoizedState,e)},useTransition:function(){return[_i(vi)[0],yi().memoizedState]},useMutableSource:ki,useSyncExternalStore:xi,useId:Vi,unstable_isNewReconciler:!1},el={readContext:Io,useCallback:Ui,useContext:Io,useEffect:Oi,useImperativeHandle:Bi,useInsertionEffect:Ai,useLayoutEffect:Ri,useMemo:ji,useReducer:wi,useRef:Ni,useState:function(){return wi(vi)},useDebugValue:Hi,useDeferredValue:function(e){var t=yi();return null===li?t.memoizedState=e:Zi(t,li.memoizedState,e)},useTransition:function(){return[wi(vi)[0],yi().memoizedState]},useMutableSource:ki,useSyncExternalStore:xi,useId:Vi,unstable_isNewReconciler:!1};function tl(e,t){if(e&&e.defaultProps){for(var n in t=R({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function nl(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:R({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var rl={isMounted:function(e){return!!(e=e._reactInternals)&&Ue(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Xs(),a=Js(e),o=Fo(r,a);o.payload=t,null!=n&&(o.callback=n),null!==(t=Bo(e,o,a))&&(ec(t,e,a,r),Ho(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Xs(),a=Js(e),o=Fo(r,a);o.tag=1,o.payload=t,null!=n&&(o.callback=n),null!==(t=Bo(e,o,a))&&(ec(t,e,a,r),Ho(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Xs(),r=Js(e),a=Fo(n,r);a.tag=2,null!=t&&(a.callback=t),null!==(t=Bo(e,a,r))&&(ec(t,e,r,n),Ho(t,e,r))}};function al(e,t,n,r,a,o,i){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,i):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(a,o))}function ol(e,t,n){var r=!1,a=Ea,o=t.contextType;return"object"==typeof o&&null!==o?o=Io(o):(a=Na(t)?Ma:Ta.current,o=(r=null!=(r=t.contextTypes))?Ia(e,a):Ea),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=rl,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function il(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&rl.enqueueReplaceState(t,t.state,null)}function ll(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Ao(e);var o=t.contextType;"object"==typeof o&&null!==o?a.context=Io(o):(o=Na(t)?Ma:Ta.current,a.context=Ia(e,o)),a.state=e.memoizedState,"function"==typeof(o=t.getDerivedStateFromProps)&&(nl(e,t,o,n),a.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(t=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&rl.enqueueReplaceState(a,a.state,null),jo(e,n,a,r),a.state=e.memoizedState),"function"==typeof a.componentDidMount&&(e.flags|=4194308)}function sl(e,t){try{var n="",r=t;do{n+=U(r),r=r.return}while(r);var a=n}catch(e){a="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:a,digest:null}}function cl(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}var ul="function"==typeof WeakMap?WeakMap:Map;function dl(e,t,n){(n=Fo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){js||(js=!0,Zs=r)},n}function pl(e,t,n){(n=Fo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){}}var o=e.stateNode;return null!==o&&"function"==typeof o.componentDidCatch&&(n.callback=function(){"function"!=typeof r&&(null===$s?$s=new Set([this]):$s.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ml(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new ul;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=xc.bind(null,e,t,n),t.then(e,e))}function fl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function gl(e,t,n,r,a){return 1&e.mode?(e.flags|=65536,e.lanes=a,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Fo(-1,1)).tag=2,Bo(n,t,1))),n.lanes|=1),e)}var hl=_.ReactCurrentOwner,bl=!1;function yl(e,t,n,r){t.child=null===e?wo(t,null,n,r):_o(t,e.child,n,r)}function vl(e,t,n,r,a){n=n.render;var o=t.ref;return Mo(t,a),r=gi(e,t,n,r,o,a),n=hi(),null===e||bl?(ro&&n&&Ja(t),t.flags|=1,yl(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,jl(e,t,a))}function _l(e,t,n,r,a){if(null===e){var o=n.type;return"function"!=typeof o||Ic(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Dc(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,wl(e,t,o,r,a))}if(o=e.child,0===(e.lanes&a)){var i=o.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(i,r)&&e.ref===t.ref)return jl(e,t,a)}return t.flags|=1,(e=Nc(o,r)).ref=t.ref,e.return=t,t.child=e}function wl(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(lr(o,r)&&e.ref===t.ref){if(bl=!1,t.pendingProps=r=o,0===(e.lanes&a))return t.lanes=e.lanes,jl(e,t,a);131072&e.flags&&(bl=!0)}}return Sl(e,t,n,r,a)}function kl(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ca(Ns,Is),Is|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Ca(Ns,Is),Is|=r}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ca(Ns,Is),Is|=n;else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ca(Ns,Is),Is|=r;return yl(e,t,a,n),t.child}function xl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Sl(e,t,n,r,a){var o=Na(n)?Ma:Ta.current;return o=Ia(t,o),Mo(t,a),n=gi(e,t,n,r,o,a),r=hi(),null===e||bl?(ro&&r&&Ja(t),t.flags|=1,yl(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,jl(e,t,a))}function Cl(e,t,n,r,a){if(Na(n)){var o=!0;Oa(t)}else o=!1;if(Mo(t,a),null===t.stateNode)Ul(e,t),ol(t,n,r),ll(t,n,r,a),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var s=i.context,c=n.contextType;"object"==typeof c&&null!==c?c=Io(c):c=Ia(t,c=Na(n)?Ma:Ta.current);var u=n.getDerivedStateFromProps,d="function"==typeof u||"function"==typeof i.getSnapshotBeforeUpdate;d||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l!==r||s!==c)&&il(t,i,r,c),Oo=!1;var p=t.memoizedState;i.state=p,jo(t,r,i,a),s=t.memoizedState,l!==r||p!==s||Pa.current||Oo?("function"==typeof u&&(nl(t,n,u,r),s=t.memoizedState),(l=Oo||al(t,n,l,r,p,s,c))?(d||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=c,r=l):("function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Ro(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:tl(t.type,l),i.props=c,d=t.pendingProps,p=i.context,"object"==typeof(s=n.contextType)&&null!==s?s=Io(s):s=Ia(t,s=Na(n)?Ma:Ta.current);var m=n.getDerivedStateFromProps;(u="function"==typeof m||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l!==d||p!==s)&&il(t,i,r,s),Oo=!1,p=t.memoizedState,i.state=p,jo(t,r,i,a);var f=t.memoizedState;l!==d||p!==f||Pa.current||Oo?("function"==typeof m&&(nl(t,n,m,r),f=t.memoizedState),(c=Oo||al(t,n,c,r,p,f,s)||!1)?(u||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,f,s),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,f,s)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=f),i.props=r,i.state=f,i.context=s,r=c):("function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return El(e,t,n,r,o,a)}function El(e,t,n,r,a,o){xl(e,t);var i=!!(128&t.flags);if(!r&&!i)return a&&Aa(t,n,!1),jl(e,t,o);r=t.stateNode,hl.current=t;var l=i&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=_o(t,e.child,null,o),t.child=_o(t,null,l,o)):yl(e,t,l,o),t.memoizedState=r.state,a&&Aa(t,n,!0),t.child}function Tl(e){var t=e.stateNode;t.pendingContext?La(0,t.pendingContext,t.pendingContext!==t.context):t.context&&La(0,t.context,!1),Yo(e,t.containerInfo)}function Pl(e,t,n,r,a){return mo(),fo(a),t.flags|=256,yl(e,t,n,r),t.child}var Ml,Il,Nl,Dl,Ll={dehydrated:null,treeContext:null,retryLane:0};function zl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ol(e,t,n){var r,a=t.pendingProps,i=Jo.current,l=!1,s=!!(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&!!(2&i)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Ca(Jo,1&i),null===e)return so(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=a.children,e=a.fallback,l?(a=t.mode,l=t.child,s={mode:"hidden",children:s},1&a||null===l?l=zc(s,a,0,null):(l.childLanes=0,l.pendingProps=s),e=Lc(e,a,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=zl(n),t.memoizedState=Ll,e):Al(t,s));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,a,i,l){if(n)return 256&t.flags?(t.flags&=-257,Rl(e,t,l,r=cl(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,a=t.mode,r=zc({mode:"visible",children:r.children},a,0,null),(i=Lc(i,a,l,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,1&t.mode&&_o(t,e.child,null,l),t.child.memoizedState=zl(l),t.memoizedState=Ll,i);if(!(1&t.mode))return Rl(e,t,l,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Rl(e,t,l,r=cl(i=Error(o(419)),r,void 0))}if(s=0!==(l&e.childLanes),bl||s){if(null!==(r=Ts)){switch(l&-l){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|l))?0:a)&&a!==i.retryLane&&(i.retryLane=a,zo(e,a),ec(r,e,a,-1))}return mc(),Rl(e,t,l,r=cl(Error(o(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Cc.bind(null,e),a._reactRetry=t,null):(e=i.treeContext,no=sa(a.nextSibling),to=t,ro=!0,ao=null,null!==e&&(Wa[Ka++]=Ya,Wa[Ka++]=Qa,Wa[Ka++]=Ga,Ya=e.id,Qa=e.overflow,Ga=t),t=Al(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,i,n);if(l){l=a.fallback,s=t.mode,r=(i=e.child).sibling;var c={mode:"hidden",children:a.children};return 1&s||t.child===i?(a=Nc(i,c)).subtreeFlags=14680064&i.subtreeFlags:((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null),null!==r?l=Nc(r,l):(l=Lc(l,s,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,s=null===(s=e.child.memoizedState)?zl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=Ll,a}return e=(l=e.child).sibling,a=Nc(l,{mode:"visible",children:a.children}),!(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Al(e,t){return(t=zc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Rl(e,t,n,r){return null!==r&&fo(r),_o(t,e.child,null,n),(e=Al(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Fl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Po(e.return,t,n)}function Bl(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Hl(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(yl(e,t,r.children,n),2&(r=Jo.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Fl(e,n,t);else if(19===e.tag)Fl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ca(Jo,r),1&t.mode)switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ei(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bl(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ei(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bl(t,!0,n,null,o);break;case"together":Bl(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Ul(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function jl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),zs|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Nc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Nc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Zl(e,t){if(!ro)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function $l(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Vl(e,t,n){var r=t.pendingProps;switch(eo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return $l(t),null;case 1:case 17:return Na(t.type)&&Da(),$l(t),null;case 3:return r=t.stateNode,Qo(),Sa(Pa),Sa(Ta),ni(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(uo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==ao&&(ac(ao),ao=null))),Il(e,t),$l(t),null;case 5:Xo(t);var a=Go(Ko.current);if(n=t.type,null!==e&&null!=t.stateNode)Nl(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return $l(t),null}if(e=Go(Vo.current),uo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[da]=t,r[pa]=i,e=!!(1&t.mode),n){case"dialog":Fr("cancel",r),Fr("close",r);break;case"iframe":case"object":case"embed":Fr("load",r);break;case"video":case"audio":for(a=0;a<zr.length;a++)Fr(zr[a],r);break;case"source":Fr("error",r);break;case"img":case"image":case"link":Fr("error",r),Fr("load",r);break;case"details":Fr("toggle",r);break;case"input":Q(r,i),Fr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Fr("invalid",r);break;case"textarea":ae(r,i),Fr("invalid",r)}for(var s in be(n,i),a=null,i)if(i.hasOwnProperty(s)){var c=i[s];"children"===s?"string"==typeof c?r.textContent!==c&&(!0!==i.suppressHydrationWarning&&qr(r.textContent,c,e),a=["children",c]):"number"==typeof c&&r.textContent!==""+c&&(!0!==i.suppressHydrationWarning&&qr(r.textContent,c,e),a=["children",""+c]):l.hasOwnProperty(s)&&null!=c&&"onScroll"===s&&Fr("scroll",r)}switch(n){case"input":W(r),J(r,i,!0);break;case"textarea":W(r),ie(r);break;case"select":case"option":break;default:"function"==typeof i.onClick&&(r.onclick=Xr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[da]=t,e[pa]=r,Ml(e,t,!1,!1),t.stateNode=e;e:{switch(s=ye(n,r),n){case"dialog":Fr("cancel",e),Fr("close",e),a=r;break;case"iframe":case"object":case"embed":Fr("load",e),a=r;break;case"video":case"audio":for(a=0;a<zr.length;a++)Fr(zr[a],e);a=r;break;case"source":Fr("error",e),a=r;break;case"img":case"image":case"link":Fr("error",e),Fr("load",e),a=r;break;case"details":Fr("toggle",e),a=r;break;case"input":Q(e,r),a=Y(e,r),Fr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=R({},r,{value:void 0}),Fr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Fr("invalid",e)}for(i in be(n,a),c=a)if(c.hasOwnProperty(i)){var u=c[i];"style"===i?ge(e,u):"dangerouslySetInnerHTML"===i?null!=(u=u?u.__html:void 0)&&ue(e,u):"children"===i?"string"==typeof u?("textarea"!==n||""!==u)&&de(e,u):"number"==typeof u&&de(e,""+u):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(l.hasOwnProperty(i)?null!=u&&"onScroll"===i&&Fr("scroll",e):null!=u&&v(e,i,u,s))}switch(n){case"input":W(e),J(e,r,!1);break;case"textarea":W(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+$(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof a.onClick&&(e.onclick=Xr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return $l(t),null;case 6:if(e&&null!=t.stateNode)Dl(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(o(166));if(n=Go(Ko.current),Go(Vo.current),uo(t)){if(r=t.stateNode,n=t.memoizedProps,r[da]=t,(i=r.nodeValue!==n)&&null!==(e=to))switch(e.tag){case 3:qr(r.nodeValue,n,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&qr(r.nodeValue,n,!!(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[da]=t,t.stateNode=r}return $l(t),null;case 13:if(Sa(Jo),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ro&&null!==no&&1&t.mode&&!(128&t.flags))po(),mo(),t.flags|=98560,i=!1;else if(i=uo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(o(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(o(317));i[da]=t}else mo(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;$l(t),i=!1}else null!==ao&&(ac(ao),ao=null),i=!0;if(!i)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,1&t.mode&&(null===e||1&Jo.current?0===Ds&&(Ds=3):mc())),null!==t.updateQueue&&(t.flags|=4),$l(t),null);case 4:return Qo(),Il(e,t),null===e&&Ur(t.stateNode.containerInfo),$l(t),null;case 10:return To(t.type._context),$l(t),null;case 19:if(Sa(Jo),null===(i=t.memoizedState))return $l(t),null;if(r=!!(128&t.flags),null===(s=i.rendering))if(r)Zl(i,!1);else{if(0!==Ds||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(s=ei(e))){for(t.flags|=128,Zl(i,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(s=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ca(Jo,1&Jo.current|2),t.child}e=e.sibling}null!==i.tail&&Qe()>Hs&&(t.flags|=128,r=!0,Zl(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ei(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Zl(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!ro)return $l(t),null}else 2*Qe()-i.renderingStartTime>Hs&&1073741824!==n&&(t.flags|=128,r=!0,Zl(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=i.last)?n.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Qe(),t.sibling=null,n=Jo.current,Ca(Jo,r?1&n|2:1&n),t):($l(t),null);case 22:case 23:return cc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&1&t.mode?!!(1073741824&Is)&&($l(t),6&t.subtreeFlags&&(t.flags|=8192)):$l(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function Wl(e,t){switch(eo(t),t.tag){case 1:return Na(t.type)&&Da(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Qo(),Sa(Pa),Sa(Ta),ni(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Xo(t),null;case 13:if(Sa(Jo),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));mo()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Sa(Jo),null;case 4:return Qo(),null;case 10:return To(t.type._context),null;case 22:case 23:return cc(),null;default:return null}}Ml=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Il=function(){},Nl=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Go(Vo.current);var o,i=null;switch(n){case"input":a=Y(e,a),r=Y(e,r),i=[];break;case"select":a=R({},a,{value:void 0}),r=R({},r,{value:void 0}),i=[];break;case"textarea":a=re(e,a),r=re(e,r),i=[];break;default:"function"!=typeof a.onClick&&"function"==typeof r.onClick&&(e.onclick=Xr)}for(u in be(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var s=a[u];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(l.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var c=r[u];if(s=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&c!==s&&(null!=c||null!=s))if("style"===u)if(s){for(o in s)!s.hasOwnProperty(o)||c&&c.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in c)c.hasOwnProperty(o)&&s[o]!==c[o]&&(n||(n={}),n[o]=c[o])}else n||(i||(i=[]),i.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(i=i||[]).push(u,c)):"children"===u?"string"!=typeof c&&"number"!=typeof c||(i=i||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(l.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Fr("scroll",e),i||s===c||(i=[])):(i=i||[]).push(u,c))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}},Dl=function(e,t,n,r){n!==r&&(t.flags|=4)};var Kl=!1,Gl=!1,Yl="function"==typeof WeakSet?WeakSet:Set,Ql=null;function ql(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(n){kc(e,t,n)}else n.current=null}function Xl(e,t,n){try{n()}catch(n){kc(e,t,n)}}var Jl=!1;function es(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&Xl(t,n,o)}a=a.next}while(a!==r)}}function ts(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ns(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function rs(e){var t=e.alternate;null!==t&&(e.alternate=null,rs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[da],delete t[pa],delete t[fa],delete t[ga],delete t[ha])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function as(e){return 5===e.tag||3===e.tag||4===e.tag}function os(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||as(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function is(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Xr));else if(4!==r&&null!==(e=e.child))for(is(e,t,n),e=e.sibling;null!==e;)is(e,t,n),e=e.sibling}function ls(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ls(e,t,n),e=e.sibling;null!==e;)ls(e,t,n),e=e.sibling}var ss=null,cs=!1;function us(e,t,n){for(n=n.child;null!==n;)ds(e,t,n),n=n.sibling}function ds(e,t,n){if(at&&"function"==typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(rt,n)}catch(e){}switch(n.tag){case 5:Gl||ql(n,t);case 6:var r=ss,a=cs;ss=null,us(e,t,n),cs=a,null!==(ss=r)&&(cs?(e=ss,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ss.removeChild(n.stateNode));break;case 18:null!==ss&&(cs?(e=ss,n=n.stateNode,8===e.nodeType?la(e.parentNode,n):1===e.nodeType&&la(e,n),Ut(e)):la(ss,n.stateNode));break;case 4:r=ss,a=cs,ss=n.stateNode.containerInfo,cs=!0,us(e,t,n),ss=r,cs=a;break;case 0:case 11:case 14:case 15:if(!Gl&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,i=o.destroy;o=o.tag,void 0!==i&&(2&o||4&o)&&Xl(n,t,i),a=a.next}while(a!==r)}us(e,t,n);break;case 1:if(!Gl&&(ql(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(e){kc(n,t,e)}us(e,t,n);break;case 21:us(e,t,n);break;case 22:1&n.mode?(Gl=(r=Gl)||null!==n.memoizedState,us(e,t,n),Gl=r):us(e,t,n);break;default:us(e,t,n)}}function ps(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Yl),t.forEach(function(t){var r=Ec.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ms(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var i=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:ss=s.stateNode,cs=!1;break e;case 3:case 4:ss=s.stateNode.containerInfo,cs=!0;break e}s=s.return}if(null===ss)throw Error(o(160));ds(i,l,a),ss=null,cs=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(e){kc(a,t,e)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)fs(t,e),t=t.sibling}function fs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ms(t,e),gs(e),4&r){try{es(3,e,e.return),ts(3,e)}catch(t){kc(e,e.return,t)}try{es(5,e,e.return)}catch(t){kc(e,e.return,t)}}break;case 1:ms(t,e),gs(e),512&r&&null!==n&&ql(n,n.return);break;case 5:if(ms(t,e),gs(e),512&r&&null!==n&&ql(n,n.return),32&e.flags){var a=e.stateNode;try{de(a,"")}catch(t){kc(e,e.return,t)}}if(4&r&&null!=(a=e.stateNode)){var i=e.memoizedProps,l=null!==n?n.memoizedProps:i,s=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===s&&"radio"===i.type&&null!=i.name&&q(a,i),ye(s,l);var u=ye(s,i);for(l=0;l<c.length;l+=2){var d=c[l],p=c[l+1];"style"===d?ge(a,p):"dangerouslySetInnerHTML"===d?ue(a,p):"children"===d?de(a,p):v(a,d,p,u)}switch(s){case"input":X(a,i);break;case"textarea":oe(a,i);break;case"select":var m=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!i.multiple;var f=i.value;null!=f?ne(a,!!i.multiple,f,!1):m!==!!i.multiple&&(null!=i.defaultValue?ne(a,!!i.multiple,i.defaultValue,!0):ne(a,!!i.multiple,i.multiple?[]:"",!1))}a[pa]=i}catch(t){kc(e,e.return,t)}}break;case 6:if(ms(t,e),gs(e),4&r){if(null===e.stateNode)throw Error(o(162));a=e.stateNode,i=e.memoizedProps;try{a.nodeValue=i}catch(t){kc(e,e.return,t)}}break;case 3:if(ms(t,e),gs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ut(t.containerInfo)}catch(t){kc(e,e.return,t)}break;case 4:default:ms(t,e),gs(e);break;case 13:ms(t,e),gs(e),8192&(a=e.child).flags&&(i=null!==a.memoizedState,a.stateNode.isHidden=i,!i||null!==a.alternate&&null!==a.alternate.memoizedState||(Bs=Qe())),4&r&&ps(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Gl=(u=Gl)||d,ms(t,e),Gl=u):ms(t,e),gs(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&1&e.mode)for(Ql=e,d=e.child;null!==d;){for(p=Ql=d;null!==Ql;){switch(f=(m=Ql).child,m.tag){case 0:case 11:case 14:case 15:es(4,m,m.return);break;case 1:ql(m,m.return);var g=m.stateNode;if("function"==typeof g.componentWillUnmount){r=m,n=m.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(e){kc(r,n,e)}}break;case 5:ql(m,m.return);break;case 22:if(null!==m.memoizedState){vs(p);continue}}null!==f?(f.return=m,Ql=f):vs(p)}d=d.sibling}e:for(d=null,p=e;;){if(5===p.tag){if(null===d){d=p;try{a=p.stateNode,u?"function"==typeof(i=a.style).setProperty?i.setProperty("display","none","important"):i.display="none":(s=p.stateNode,l=null!=(c=p.memoizedProps.style)&&c.hasOwnProperty("display")?c.display:null,s.style.display=fe("display",l))}catch(t){kc(e,e.return,t)}}}else if(6===p.tag){if(null===d)try{p.stateNode.nodeValue=u?"":p.memoizedProps}catch(t){kc(e,e.return,t)}}else if((22!==p.tag&&23!==p.tag||null===p.memoizedState||p===e)&&null!==p.child){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;null===p.sibling;){if(null===p.return||p.return===e)break e;d===p&&(d=null),p=p.return}d===p&&(d=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:ms(t,e),gs(e),4&r&&ps(e);case 21:}}function gs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(as(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(de(a,""),r.flags&=-33),ls(e,os(e),a);break;case 3:case 4:var i=r.stateNode.containerInfo;is(e,os(e),i);break;default:throw Error(o(161))}}catch(t){kc(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function hs(e,t,n){Ql=e,bs(e,t,n)}function bs(e,t,n){for(var r=!!(1&e.mode);null!==Ql;){var a=Ql,o=a.child;if(22===a.tag&&r){var i=null!==a.memoizedState||Kl;if(!i){var l=a.alternate,s=null!==l&&null!==l.memoizedState||Gl;l=Kl;var c=Gl;if(Kl=i,(Gl=s)&&!c)for(Ql=a;null!==Ql;)s=(i=Ql).child,22===i.tag&&null!==i.memoizedState?_s(a):null!==s?(s.return=i,Ql=s):_s(a);for(;null!==o;)Ql=o,bs(o,t,n),o=o.sibling;Ql=a,Kl=l,Gl=c}ys(e)}else 8772&a.subtreeFlags&&null!==o?(o.return=a,Ql=o):ys(e)}}function ys(e){for(;null!==Ql;){var t=Ql;if(8772&t.flags){var n=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:Gl||ts(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Gl)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:tl(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Zo(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Zo(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var p=d.dehydrated;null!==p&&Ut(p)}}}break;default:throw Error(o(163))}Gl||512&t.flags&&ns(t)}catch(e){kc(t,t.return,e)}}if(t===e){Ql=null;break}if(null!==(n=t.sibling)){n.return=t.return,Ql=n;break}Ql=t.return}}function vs(e){for(;null!==Ql;){var t=Ql;if(t===e){Ql=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Ql=n;break}Ql=t.return}}function _s(e){for(;null!==Ql;){var t=Ql;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ts(4,t)}catch(e){kc(t,n,e)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(e){kc(t,a,e)}}var o=t.return;try{ns(t)}catch(e){kc(t,o,e)}break;case 5:var i=t.return;try{ns(t)}catch(e){kc(t,i,e)}}}catch(e){kc(t,t.return,e)}if(t===e){Ql=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Ql=l;break}Ql=t.return}}var ws,ks=Math.ceil,xs=_.ReactCurrentDispatcher,Ss=_.ReactCurrentOwner,Cs=_.ReactCurrentBatchConfig,Es=0,Ts=null,Ps=null,Ms=0,Is=0,Ns=xa(0),Ds=0,Ls=null,zs=0,Os=0,As=0,Rs=null,Fs=null,Bs=0,Hs=1/0,Us=null,js=!1,Zs=null,$s=null,Vs=!1,Ws=null,Ks=0,Gs=0,Ys=null,Qs=-1,qs=0;function Xs(){return 6&Es?Qe():-1!==Qs?Qs:Qs=Qe()}function Js(e){return 1&e.mode?2&Es&&0!==Ms?Ms&-Ms:null!==go.transition?(0===qs&&(qs=ft()),qs):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Yt(e.type):1}function ec(e,t,n,r){if(50<Gs)throw Gs=0,Ys=null,Error(o(185));ht(e,n,r),2&Es&&e===Ts||(e===Ts&&(!(2&Es)&&(Os|=n),4===Ds&&oc(e,Ms)),tc(e,r),1===n&&0===Es&&!(1&t.mode)&&(Hs=Qe()+500,Fa&&Ua()))}function tc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-ot(o),l=1<<i,s=a[i];-1===s?0!==(l&n)&&0===(l&r)||(a[i]=pt(l,t)):s<=t&&(e.expiredLanes|=l),o&=~l}}(e,t);var r=dt(e,e===Ts?Ms:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Fa=!0,Ha(e)}(ic.bind(null,e)):Ha(ic.bind(null,e)),oa(function(){!(6&Es)&&Ua()}),n=null;else{switch(vt(r)){case 1:n=Xe;break;case 4:n=Je;break;case 16:default:n=et;break;case 536870912:n=nt}n=Tc(n,nc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function nc(e,t){if(Qs=-1,qs=0,6&Es)throw Error(o(327));var n=e.callbackNode;if(_c()&&e.callbackNode!==n)return null;var r=dt(e,e===Ts?Ms:0);if(0===r)return null;if(30&r||0!==(r&e.expiredLanes)||t)t=fc(e,r);else{t=r;var a=Es;Es|=2;var i=pc();for(Ts===e&&Ms===t||(Us=null,Hs=Qe()+500,uc(e,t));;)try{hc();break}catch(t){dc(e,t)}Eo(),xs.current=i,Es=a,null!==Ps?t=0:(Ts=null,Ms=0,t=Ds)}if(0!==t){if(2===t&&(0!==(a=mt(e))&&(r=a,t=rc(e,a))),1===t)throw n=Ls,uc(e,0),oc(e,r),tc(e,Qe()),n;if(6===t)oc(e,r);else{if(a=e.current.alternate,!(30&r||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!ir(o(),a))return!1}catch(e){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)||(t=fc(e,r),2===t&&(i=mt(e),0!==i&&(r=i,t=rc(e,i))),1!==t)))throw n=Ls,uc(e,0),oc(e,r),tc(e,Qe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:vc(e,Fs,Us);break;case 3:if(oc(e,r),(130023424&r)===r&&10<(t=Bs+500-Qe())){if(0!==dt(e,0))break;if(((a=e.suspendedLanes)&r)!==r){Xs(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=na(vc.bind(null,e,Fs,Us),t);break}vc(e,Fs,Us);break;case 4:if(oc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var l=31-ot(r);i=1<<l,(l=t[l])>a&&(a=l),r&=~i}if(r=a,10<(r=(120>(r=Qe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*ks(r/1960))-r)){e.timeoutHandle=na(vc.bind(null,e,Fs,Us),r);break}vc(e,Fs,Us);break;default:throw Error(o(329))}}}return tc(e,Qe()),e.callbackNode===n?nc.bind(null,e):null}function rc(e,t){var n=Rs;return e.current.memoizedState.isDehydrated&&(uc(e,t).flags|=256),2!==(e=fc(e,t))&&(t=Fs,Fs=n,null!==t&&ac(t)),e}function ac(e){null===Fs?Fs=e:Fs.push.apply(Fs,e)}function oc(e,t){for(t&=~As,t&=~Os,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function ic(e){if(6&Es)throw Error(o(327));_c();var t=dt(e,0);if(!(1&t))return tc(e,Qe()),null;var n=fc(e,t);if(0!==e.tag&&2===n){var r=mt(e);0!==r&&(t=r,n=rc(e,r))}if(1===n)throw n=Ls,uc(e,0),oc(e,t),tc(e,Qe()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,vc(e,Fs,Us),tc(e,Qe()),null}function lc(e,t){var n=Es;Es|=1;try{return e(t)}finally{0===(Es=n)&&(Hs=Qe()+500,Fa&&Ua())}}function sc(e){null!==Ws&&0===Ws.tag&&!(6&Es)&&_c();var t=Es;Es|=1;var n=Cs.transition,r=yt;try{if(Cs.transition=null,yt=1,e)return e()}finally{yt=r,Cs.transition=n,!(6&(Es=t))&&Ua()}}function cc(){Is=Ns.current,Sa(Ns)}function uc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ra(n)),null!==Ps)for(n=Ps.return;null!==n;){var r=n;switch(eo(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&Da();break;case 3:Qo(),Sa(Pa),Sa(Ta),ni();break;case 5:Xo(r);break;case 4:Qo();break;case 13:case 19:Sa(Jo);break;case 10:To(r.type._context);break;case 22:case 23:cc()}n=n.return}if(Ts=e,Ps=e=Nc(e.current,null),Ms=Is=t,Ds=0,Ls=null,As=Os=zs=0,Fs=Rs=null,null!==No){for(t=0;t<No.length;t++)if(null!==(r=(n=No[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var i=o.next;o.next=a,r.next=i}n.pending=r}No=null}return e}function dc(e,t){for(;;){var n=Ps;try{if(Eo(),ri.current=qi,ci){for(var r=ii.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}ci=!1}if(oi=0,si=li=ii=null,ui=!1,di=0,Ss.current=null,null===n||null===n.return){Ds=1,Ls=t,Ps=null;break}e:{var i=e,l=n.return,s=n,c=t;if(t=Ms,s.flags|=32768,null!==c&&"object"==typeof c&&"function"==typeof c.then){var u=c,d=s,p=d.tag;if(!(1&d.mode||0!==p&&11!==p&&15!==p)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var f=fl(l);if(null!==f){f.flags&=-257,gl(f,l,s,0,t),1&f.mode&&ml(i,u,t),c=u;var g=(t=f).updateQueue;if(null===g){var h=new Set;h.add(c),t.updateQueue=h}else g.add(c);break e}if(!(1&t)){ml(i,u,t),mc();break e}c=Error(o(426))}else if(ro&&1&s.mode){var b=fl(l);if(null!==b){!(65536&b.flags)&&(b.flags|=256),gl(b,l,s,0,t),fo(sl(c,s));break e}}i=c=sl(c,s),4!==Ds&&(Ds=2),null===Rs?Rs=[i]:Rs.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Uo(i,dl(0,c,t));break e;case 1:s=c;var y=i.type,v=i.stateNode;if(!(128&i.flags||"function"!=typeof y.getDerivedStateFromError&&(null===v||"function"!=typeof v.componentDidCatch||null!==$s&&$s.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t,Uo(i,pl(i,s,t));break e}}i=i.return}while(null!==i)}yc(n)}catch(e){t=e,Ps===n&&null!==n&&(Ps=n=n.return);continue}break}}function pc(){var e=xs.current;return xs.current=qi,null===e?qi:e}function mc(){0!==Ds&&3!==Ds&&2!==Ds||(Ds=4),null===Ts||!(268435455&zs)&&!(268435455&Os)||oc(Ts,Ms)}function fc(e,t){var n=Es;Es|=2;var r=pc();for(Ts===e&&Ms===t||(Us=null,uc(e,t));;)try{gc();break}catch(t){dc(e,t)}if(Eo(),Es=n,xs.current=r,null!==Ps)throw Error(o(261));return Ts=null,Ms=0,Ds}function gc(){for(;null!==Ps;)bc(Ps)}function hc(){for(;null!==Ps&&!Ge();)bc(Ps)}function bc(e){var t=ws(e.alternate,e,Is);e.memoizedProps=e.pendingProps,null===t?yc(e):Ps=t,Ss.current=null}function yc(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=Wl(n,t)))return n.flags&=32767,void(Ps=n);if(null===e)return Ds=6,void(Ps=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=Vl(n,t,Is)))return void(Ps=n);if(null!==(t=t.sibling))return void(Ps=t);Ps=t=e}while(null!==t);0===Ds&&(Ds=5)}function vc(e,t,n){var r=yt,a=Cs.transition;try{Cs.transition=null,yt=1,function(e,t,n,r){do{_c()}while(null!==Ws);if(6&Es)throw Error(o(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,i),e===Ts&&(Ps=Ts=null,Ms=0),!(2064&n.subtreeFlags)&&!(2064&n.flags)||Vs||(Vs=!0,Tc(et,function(){return _c(),null})),i=!!(15990&n.flags),!!(15990&n.subtreeFlags)||i){i=Cs.transition,Cs.transition=null;var l=yt;yt=1;var s=Es;Es|=4,Ss.current=null,function(e,t){if(Jr=Zt,pr(e=dr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(e){n=null;break e}var l=0,s=-1,c=-1,u=0,d=0,p=e,m=null;t:for(;;){for(var f;p!==n||0!==a&&3!==p.nodeType||(s=l+a),p!==i||0!==r&&3!==p.nodeType||(c=l+r),3===p.nodeType&&(l+=p.nodeValue.length),null!==(f=p.firstChild);)m=p,p=f;for(;;){if(p===e)break t;if(m===n&&++u===a&&(s=l),m===i&&++d===r&&(c=l),null!==(f=p.nextSibling))break;m=(p=m).parentNode}p=f}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ea={focusedElem:e,selectionRange:n},Zt=!1,Ql=t;null!==Ql;)if(e=(t=Ql).child,1028&t.subtreeFlags&&null!==e)e.return=t,Ql=e;else for(;null!==Ql;){t=Ql;try{var g=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==g){var h=g.memoizedProps,b=g.memoizedState,y=t.stateNode,v=y.getSnapshotBeforeUpdate(t.elementType===t.type?h:tl(t.type,h),b);y.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var _=t.stateNode.containerInfo;1===_.nodeType?_.textContent="":9===_.nodeType&&_.documentElement&&_.removeChild(_.documentElement);break;default:throw Error(o(163))}}catch(e){kc(t,t.return,e)}if(null!==(e=t.sibling)){e.return=t.return,Ql=e;break}Ql=t.return}g=Jl,Jl=!1}(e,n),fs(n,e),mr(ea),Zt=!!Jr,ea=Jr=null,e.current=n,hs(n,e,a),Ye(),Es=s,yt=l,Cs.transition=i}else e.current=n;if(Vs&&(Vs=!1,Ws=e,Ks=a),i=e.pendingLanes,0===i&&($s=null),function(e){if(at&&"function"==typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(rt,e,void 0,!(128&~e.current.flags))}catch(e){}}(n.stateNode),tc(e,Qe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(js)throw js=!1,e=Zs,Zs=null,e;!!(1&Ks)&&0!==e.tag&&_c(),i=e.pendingLanes,1&i?e===Ys?Gs++:(Gs=0,Ys=e):Gs=0,Ua()}(e,t,n,r)}finally{Cs.transition=a,yt=r}return null}function _c(){if(null!==Ws){var e=vt(Ks),t=Cs.transition,n=yt;try{if(Cs.transition=null,yt=16>e?16:e,null===Ws)var r=!1;else{if(e=Ws,Ws=null,Ks=0,6&Es)throw Error(o(331));var a=Es;for(Es|=4,Ql=e.current;null!==Ql;){var i=Ql,l=i.child;if(16&Ql.flags){var s=i.deletions;if(null!==s){for(var c=0;c<s.length;c++){var u=s[c];for(Ql=u;null!==Ql;){var d=Ql;switch(d.tag){case 0:case 11:case 15:es(8,d,i)}var p=d.child;if(null!==p)p.return=d,Ql=p;else for(;null!==Ql;){var m=(d=Ql).sibling,f=d.return;if(rs(d),d===u){Ql=null;break}if(null!==m){m.return=f,Ql=m;break}Ql=f}}}var g=i.alternate;if(null!==g){var h=g.child;if(null!==h){g.child=null;do{var b=h.sibling;h.sibling=null,h=b}while(null!==h)}}Ql=i}}if(2064&i.subtreeFlags&&null!==l)l.return=i,Ql=l;else e:for(;null!==Ql;){if(2048&(i=Ql).flags)switch(i.tag){case 0:case 11:case 15:es(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Ql=y;break e}Ql=i.return}}var v=e.current;for(Ql=v;null!==Ql;){var _=(l=Ql).child;if(2064&l.subtreeFlags&&null!==_)_.return=l,Ql=_;else e:for(l=v;null!==Ql;){if(2048&(s=Ql).flags)try{switch(s.tag){case 0:case 11:case 15:ts(9,s)}}catch(e){kc(s,s.return,e)}if(s===l){Ql=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,Ql=w;break e}Ql=s.return}}if(Es=a,Ua(),at&&"function"==typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(rt,e)}catch(e){}r=!0}return r}finally{yt=n,Cs.transition=t}}return!1}function wc(e,t,n){e=Bo(e,t=dl(0,t=sl(n,t),1),1),t=Xs(),null!==e&&(ht(e,1,t),tc(e,t))}function kc(e,t,n){if(3===e.tag)wc(e,e,n);else for(;null!==t;){if(3===t.tag){wc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===$s||!$s.has(r))){t=Bo(t,e=pl(t,e=sl(n,e),1),1),e=Xs(),null!==t&&(ht(t,1,e),tc(t,e));break}}t=t.return}}function xc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=Xs(),e.pingedLanes|=e.suspendedLanes&n,Ts===e&&(Ms&n)===n&&(4===Ds||3===Ds&&(130023424&Ms)===Ms&&500>Qe()-Bs?uc(e,0):As|=n),tc(e,t)}function Sc(e,t){0===t&&(1&e.mode?(t=ct,!(130023424&(ct<<=1))&&(ct=4194304)):t=1);var n=Xs();null!==(e=zo(e,t))&&(ht(e,t,n),tc(e,n))}function Cc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Sc(e,n)}function Ec(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Sc(e,n)}function Tc(e,t){return We(e,t)}function Pc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Mc(e,t,n,r){return new Pc(e,t,n,r)}function Ic(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Nc(e,t){var n=e.alternate;return null===n?((n=Mc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Dc(e,t,n,r,a,i){var l=2;if(r=e,"function"==typeof e)Ic(e)&&(l=1);else if("string"==typeof e)l=5;else e:switch(e){case x:return Lc(n.children,a,i,t);case S:l=8,a|=8;break;case C:return(e=Mc(12,n,t,2|a)).elementType=C,e.lanes=i,e;case M:return(e=Mc(13,n,t,a)).elementType=M,e.lanes=i,e;case I:return(e=Mc(19,n,t,a)).elementType=I,e.lanes=i,e;case L:return zc(n,a,i,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case E:l=10;break e;case T:l=9;break e;case P:l=11;break e;case N:l=14;break e;case D:l=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Mc(l,n,t,a)).elementType=e,t.type=r,t.lanes=i,t}function Lc(e,t,n,r){return(e=Mc(7,e,r,t)).lanes=n,e}function zc(e,t,n,r){return(e=Mc(22,e,r,t)).elementType=L,e.lanes=n,e.stateNode={isHidden:!1},e}function Oc(e,t,n){return(e=Mc(6,e,null,t)).lanes=n,e}function Ac(e,t,n){return(t=Mc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Rc(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Fc(e,t,n,r,a,o,i,l,s){return e=new Rc(e,t,n,l,s),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Mc(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ao(o),e}function Bc(e){if(!e)return Ea;e:{if(Ue(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Na(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Na(n))return za(e,n,t)}return t}function Hc(e,t,n,r,a,o,i,l,s){return(e=Fc(n,r,!0,e,0,o,0,l,s)).context=Bc(null),n=e.current,(o=Fo(r=Xs(),a=Js(n))).callback=null!=t?t:null,Bo(n,o,a),e.current.lanes=a,ht(e,a,r),tc(e,r),e}function Uc(e,t,n,r){var a=t.current,o=Xs(),i=Js(a);return n=Bc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Fo(o,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Bo(a,t,i))&&(ec(e,a,i,o),Ho(e,a,i)),i}function jc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Zc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function $c(e,t){Zc(e,t),(e=e.alternate)&&Zc(e,t)}ws=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Pa.current)bl=!0;else{if(0===(e.lanes&n)&&!(128&t.flags))return bl=!1,function(e,t,n){switch(t.tag){case 3:Tl(t),mo();break;case 5:qo(t);break;case 1:Na(t.type)&&Oa(t);break;case 4:Yo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ca(ko,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ca(Jo,1&Jo.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ol(e,t,n):(Ca(Jo,1&Jo.current),null!==(e=jl(e,t,n))?e.sibling:null);Ca(Jo,1&Jo.current);break;case 19:if(r=0!==(n&t.childLanes),128&e.flags){if(r)return Hl(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ca(Jo,Jo.current),r)break;return null;case 22:case 23:return t.lanes=0,kl(e,t,n)}return jl(e,t,n)}(e,t,n);bl=!!(131072&e.flags)}else bl=!1,ro&&1048576&t.flags&&Xa(t,Va,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ul(e,t),e=t.pendingProps;var a=Ia(t,Ta.current);Mo(t,n),a=gi(null,t,r,e,a,n);var i=hi();return t.flags|=1,"object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Na(r)?(i=!0,Oa(t)):i=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Ao(t),a.updater=rl,t.stateNode=a,a._reactInternals=t,ll(t,r,e,n),t=El(null,t,r,!0,i,n)):(t.tag=0,ro&&i&&Ja(t),yl(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ul(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"==typeof e)return Ic(e)?1:0;if(null!=e){if((e=e.$$typeof)===P)return 11;if(e===N)return 14}return 2}(r),e=tl(r,e),a){case 0:t=Sl(null,t,r,e,n);break e;case 1:t=Cl(null,t,r,e,n);break e;case 11:t=vl(null,t,r,e,n);break e;case 14:t=_l(null,t,r,tl(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Sl(e,t,r,a=t.elementType===r?a:tl(r,a),n);case 1:return r=t.type,a=t.pendingProps,Cl(e,t,r,a=t.elementType===r?a:tl(r,a),n);case 3:e:{if(Tl(t),null===e)throw Error(o(387));r=t.pendingProps,a=(i=t.memoizedState).element,Ro(e,t),jo(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Pl(e,t,r,n,a=sl(Error(o(423)),t));break e}if(r!==a){t=Pl(e,t,r,n,a=sl(Error(o(424)),t));break e}for(no=sa(t.stateNode.containerInfo.firstChild),to=t,ro=!0,ao=null,n=wo(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(mo(),r===a){t=jl(e,t,n);break e}yl(e,t,r,n)}t=t.child}return t;case 5:return qo(t),null===e&&so(t),r=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,l=a.children,ta(r,a)?l=null:null!==i&&ta(r,i)&&(t.flags|=32),xl(e,t),yl(e,t,l,n),t.child;case 6:return null===e&&so(t),null;case 13:return Ol(e,t,n);case 4:return Yo(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=_o(t,null,r,n):yl(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,vl(e,t,r,a=t.elementType===r?a:tl(r,a),n);case 7:return yl(e,t,t.pendingProps,n),t.child;case 8:case 12:return yl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,i=t.memoizedProps,l=a.value,Ca(ko,r._currentValue),r._currentValue=l,null!==i)if(ir(i.value,l)){if(i.children===a.children&&!Pa.current){t=jl(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var s=i.dependencies;if(null!==s){l=i.child;for(var c=s.firstContext;null!==c;){if(c.context===r){if(1===i.tag){(c=Fo(-1,n&-n)).tag=2;var u=i.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=n,null!==(c=i.alternate)&&(c.lanes|=n),Po(i.return,n,t),s.lanes|=n;break}c=c.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(o(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Po(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}yl(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Mo(t,n),r=r(a=Io(a)),t.flags|=1,yl(e,t,r,n),t.child;case 14:return a=tl(r=t.type,t.pendingProps),_l(e,t,r,a=tl(r.type,a),n);case 15:return wl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:tl(r,a),Ul(e,t),t.tag=1,Na(r)?(e=!0,Oa(t)):e=!1,Mo(t,n),ol(t,r,a),ll(t,r,a,n),El(null,t,r,!0,e,n);case 19:return Hl(e,t,n);case 22:return kl(e,t,n)}throw Error(o(156,t.tag))};var Vc="function"==typeof reportError?reportError:function(e){};function Wc(e){this._internalRoot=e}function Kc(e){this._internalRoot=e}function Gc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Yc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Qc(){}function qc(e,t,n,r,a){var o=n._reactRootContainer;if(o){var i=o;if("function"==typeof a){var l=a;a=function(){var e=jc(i);l.call(e)}}Uc(t,i,e,a)}else i=function(e,t,n,r,a){if(a){if("function"==typeof r){var o=r;r=function(){var e=jc(i);o.call(e)}}var i=Hc(t,r,e,0,null,!1,0,"",Qc);return e._reactRootContainer=i,e[ma]=i.current,Ur(8===e.nodeType?e.parentNode:e),sc(),i}for(;a=e.lastChild;)e.removeChild(a);if("function"==typeof r){var l=r;r=function(){var e=jc(s);l.call(e)}}var s=Fc(e,0,!1,null,0,!1,0,"",Qc);return e._reactRootContainer=s,e[ma]=s.current,Ur(8===e.nodeType?e.parentNode:e),sc(function(){Uc(t,s,n,r)}),s}(n,t,e,a,r);return jc(i)}Kc.prototype.render=Wc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Uc(e,t,null,null)},Kc.prototype.unmount=Wc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;sc(function(){Uc(null,e,null,null)}),t[ma]=null}},Kc.prototype.unstable_scheduleHydration=function(e){if(e){var t=xt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Dt.length&&0!==t&&t<Dt[n].priority;n++);Dt.splice(n,0,e),0===n&&At(e)}},_t=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ut(t.pendingLanes);0!==n&&(bt(t,1|n),tc(t,Qe()),!(6&Es)&&(Hs=Qe()+500,Ua()))}break;case 13:sc(function(){var t=zo(e,1);if(null!==t){var n=Xs();ec(t,e,1,n)}}),$c(e,1)}},wt=function(e){if(13===e.tag){var t=zo(e,134217728);if(null!==t)ec(t,e,134217728,Xs());$c(e,134217728)}},kt=function(e){if(13===e.tag){var t=Js(e),n=zo(e,t);if(null!==n)ec(n,e,t,Xs());$c(e,t)}},xt=function(){return yt},St=function(e,t){var n=yt;try{return yt=e,t()}finally{yt=n}},we=function(e,t,n){switch(t){case"input":if(X(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=_a(r);if(!a)throw Error(o(90));K(r),X(r,a)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Te=lc,Pe=sc;var Xc={usingClientEntryPoint:!1,Events:[ya,va,_a,Ce,Ee,lc]},Jc={findFiberByHostInstance:ba,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},eu={bundleType:Jc.bundleType,version:Jc.version,rendererPackageName:Jc.rendererPackageName,rendererConfig:Jc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:_.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:Jc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var tu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!tu.isDisabled&&tu.supportsFiber)try{rt=tu.inject(eu),at=tu}catch(e){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Xc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gc(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Gc(e))throw Error(o(299));var n=!1,r="",a=Vc;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Fc(e,1,!1,null,0,n,0,r,a),e[ma]=t.current,Ur(8===e.nodeType?e.parentNode:e),new Wc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return sc(e)},t.hydrate=function(e,t,n){if(!Yc(t))throw Error(o(200));return qc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Gc(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,a=!1,i="",l=Vc;if(null!=n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Hc(t,null,e,1,null!=n?n:null,a,0,i,l),e[ma]=t.current,Ur(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Kc(t)},t.render=function(e,t,n){if(!Yc(t))throw Error(o(200));return qc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Yc(e))throw Error(o(40));return!!e._reactRootContainer&&(sc(function(){qc(null,null,e,!1,function(){e._reactRootContainer=null,e[ma]=null})}),!0)},t.unstable_batchedUpdates=lc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Yc(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return qc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},5056:(e,t,n)=>{e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},5072:e=>{var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var o={},i=[],l=0;l<e.length;l++){var s=e[l],c=r.base?s[0]+r.base:s[0],u=o[c]||0,d="".concat(c," ").concat(u);o[c]=u+1;var p=n(d),m={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==p)t[p].references++,t[p].updater(m);else{var f=a(m,r);r.byIndex=l,t.splice(l,0,{identifier:d,updater:f,references:1})}i.push(d)}return i}function a(e,t){var n=t.domAPI(t);n.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,a){var o=r(e=e||[],a=a||{});return function(e){e=e||[];for(var i=0;i<o.length;i++){var l=n(o[i]);t[l].references--}for(var s=r(e,a),c=0;c<o.length;c++){var u=n(o[c]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}o=s}}},5233:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(1601),a=n.n(r),o=n(6314),i=n.n(o)()(a());i.push([e.id,':root{--background-primary: #222222;--background-secondary: #2a2a2b;--surface-primary: #333334;--surface-secondary: #3a3a3b;--surface-hover: #404041;--background-secondary-alpha-50: rgba(42, 42, 43, 0.5);--background-secondary-alpha-30: rgba(42, 42, 43, 0.3);--surface-primary-alpha-30: rgba(51, 51, 52, 0.3);--text-primary: #fafafa;--text-secondary: #a1a1aa;--text-muted: #71717a;--text-disabled: #52525b;--primary: #3b82f6;--primary-rgb: 59, 130, 246;--primary-hover: #2563eb;--primary-foreground: #fafafa;--primary-alpha-05: rgba(59, 130, 246, 0.05);--primary-alpha-10: rgba(59, 130, 246, 0.1);--primary-alpha-15: rgba(59, 130, 246, 0.15);--primary-alpha-20: rgba(59, 130, 246, 0.2);--primary-alpha-30: rgba(59, 130, 246, 0.3);--accent: #06b6d4;--accent-hover: #0891b2;--success: #22c55e;--success-alpha-05: rgba(34, 197, 94, 0.05);--success-alpha-10: rgba(34, 197, 94, 0.1);--success-alpha-20: rgba(34, 197, 94, 0.2);--success-alpha-30: rgba(34, 197, 94, 0.3);--error: #ef4444;--error-alpha-05: rgba(239, 68, 68, 0.05);--error-alpha-10: rgba(239, 68, 68, 0.1);--error-alpha-20: rgba(239, 68, 68, 0.2);--error-alpha-30: rgba(239, 68, 68, 0.3);--error-alpha-50: rgba(239, 68, 68, 0.5);--warning: #f59e0b;--warning-alpha-20: rgba(245, 158, 11, 0.2);--warning-alpha-30: rgba(245, 158, 11, 0.3);--message-bg: #1E1E22;--message-border: rgba(255, 255, 255, 0.1);--border-color: rgba(255, 255, 255, 0.1);--border-hover: rgba(255, 255, 255, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.5);--font-mono: "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Fira Code", monospace;--scrollbar-track: #222222;--scrollbar-thumb: #3a3a3b;--scrollbar-thumb-hover: #404041}@media(prefers-color-scheme: light){:root{--background-primary: #ffffff;--background-secondary: #f8f9fa;--surface-primary: #f1f3f5;--surface-secondary: #e9ecef;--surface-hover: #dee2e6;--background-secondary-alpha-50: rgba(248, 249, 250, 0.5);--background-secondary-alpha-30: rgba(248, 249, 250, 0.3);--surface-primary-alpha-30: rgba(241, 243, 245, 0.3);--text-primary: #0a0a0a;--text-secondary: #52525b;--text-muted: #71717a;--text-disabled: #a1a1aa;--primary: #2563eb;--primary-rgb: 37, 99, 235;--primary-hover: #1d4ed8;--primary-foreground: #ffffff;--primary-alpha-05: rgba(37, 99, 235, 0.05);--primary-alpha-10: rgba(37, 99, 235, 0.1);--primary-alpha-15: rgba(37, 99, 235, 0.15);--primary-alpha-20: rgba(37, 99, 235, 0.2);--primary-alpha-30: rgba(37, 99, 235, 0.3);--accent: #0891b2;--accent-hover: #0e7490;--success: #16a34a;--success-alpha-05: rgba(22, 163, 74, 0.05);--success-alpha-10: rgba(22, 163, 74, 0.1);--success-alpha-20: rgba(22, 163, 74, 0.2);--success-alpha-30: rgba(22, 163, 74, 0.3);--error: #dc2626;--error-alpha-05: rgba(220, 38, 38, 0.05);--error-alpha-10: rgba(220, 38, 38, 0.1);--error-alpha-20: rgba(220, 38, 38, 0.2);--error-alpha-30: rgba(220, 38, 38, 0.3);--error-alpha-50: rgba(220, 38, 38, 0.5);--warning: #d97706;--warning-alpha-20: rgba(217, 119, 6, 0.2);--warning-alpha-30: rgba(217, 119, 6, 0.3);--message-bg: #F4F4F5;--message-border: rgba(0, 0, 0, 0.1);--border-color: rgba(0, 0, 0, 0.1);--border-hover: rgba(0, 0, 0, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.1);--scrollbar-track: #f8f9fa;--scrollbar-thumb: #e9ecef;--scrollbar-thumb-hover: #dee2e6}}*{margin:0;padding:0;box-sizing:border-box}@keyframes HelpSection-module__spin--vs7PK{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes HelpSection-module__pulse--wPkYZ{0%,100%{opacity:1}50%{opacity:.5}}@keyframes HelpSection-module__slideIn--OPEMs{0%{transform:translateX(100%)}100%{transform:translateX(0)}}.HelpSection-module__nxtscape-sidepanel--bXYhx{font-family:"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;background-color:var(--background-primary);color:var(--text-primary)}.HelpSection-module__scrollable--f1wCt::-webkit-scrollbar{width:6px}.HelpSection-module__scrollable--f1wCt::-webkit-scrollbar-track{background:var(--scrollbar-track)}.HelpSection-module__scrollable--f1wCt::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.HelpSection-module__scrollable--f1wCt::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.HelpSection-module__processing-spinner--JplA4{animation:HelpSection-module__spin--vs7PK 1s linear infinite}.HelpSection-module__status-indicator--connected--K6YwL{color:var(--success)}.HelpSection-module__status-indicator--disconnected--VHZ8V{color:var(--error)}.HelpSection-module__status-indicator--processing--G7SKv{color:var(--primary)}.HelpSection-module__overlay--LhUNt{position:fixed;top:0;left:0;right:0;bottom:0;background:var(--background-secondary-alpha-50);backdrop-filter:blur(4px);display:flex;align-items:center;justify-content:center;z-index:1000;animation:HelpSection-module__fadeIn--qvi61 .2s ease-out}.HelpSection-module__modal--OMl6W{background:var(--background-primary);border-radius:16px;width:90%;max-width:800px;max-height:90vh;overflow:hidden;display:flex;flex-direction:column;box-shadow:var(--shadow-lg);animation:HelpSection-module__slideUp--V3C46 .3s ease-out}.HelpSection-module__header--YJSdZ{display:flex;align-items:center;justify-content:space-between;padding:24px;border-bottom:1px solid var(--border-color);background:linear-gradient(135deg, var(--primary-alpha-05) 0%, transparent 100%)}.HelpSection-module__headerContent--fCEbM{display:flex;align-items:center;gap:12px}.HelpSection-module__headerContent--fCEbM svg{width:28px;height:28px;color:var(--primary)}.HelpSection-module__title--SUYfh{margin:0;font-size:20px;font-weight:600;color:var(--text-primary)}.HelpSection-module__version--eOmYa{font-size:12px;font-weight:400;color:var(--text-secondary);margin-left:8px;opacity:.7}.HelpSection-module__closeButton--arDRS{cursor:pointer;border:none;border-radius:8px;font-weight:500;transition:all .2s ease;padding:8px;border-radius:8px;transition:all .2s ease;color:var(--text-secondary)}.HelpSection-module__closeButton--arDRS:disabled{opacity:.6;cursor:not-allowed}.HelpSection-module__closeButton--arDRS:hover{background:var(--surface-hover);color:var(--text-primary)}.HelpSection-module__intro--SaFdz{padding:24px;border-bottom:1px solid var(--border-color)}.HelpSection-module__introText--AZSda{margin:0;font-size:15px;line-height:1.6;color:var(--text-secondary)}.HelpSection-module__controlsSection--H1QUT{padding:24px;border-bottom:1px solid var(--border-color)}.HelpSection-module__sectionTitle--f1916{margin:0 0 16px 0;font-size:16px;font-weight:600;color:var(--text-primary)}.HelpSection-module__controlsGrid--_14eU{display:grid;grid-template-columns:repeat(2, 1fr);gap:16px;margin-bottom:16px}.HelpSection-module__controlItem--C4Ji0{display:flex;align-items:center;gap:12px;padding:12px;background:var(--surface-primary);border-radius:12px;border:1px solid var(--border-color)}.HelpSection-module__controlButton--w309E{cursor:pointer;border:none;border-radius:8px;font-weight:500;transition:all .2s ease;width:40px;height:40px;display:flex;align-items:center;justify-content:center;background:var(--surface-secondary);border-radius:8px;color:var(--text-primary);cursor:default}.HelpSection-module__controlButton--w309E:disabled{opacity:.6;cursor:not-allowed}.HelpSection-module__controlButton--w309E svg{width:18px;height:18px}.HelpSection-module__controlInfo--FdRr1{display:flex;flex-direction:column;gap:2px}.HelpSection-module__controlLabel--whBDe{font-size:14px;font-weight:600;color:var(--text-primary)}.HelpSection-module__controlDesc--zBB2u{font-size:12px;color:var(--text-secondary)}.HelpSection-module__interruptNote--E10IK{padding:12px 16px;background:var(--warning-alpha-10);border:1px solid var(--warning-alpha-20);border-radius:8px;font-size:14px;line-height:1.5;color:var(--text-primary)}.HelpSection-module__interruptNote--E10IK strong{font-weight:600}.HelpSection-module__capabilitiesSection--eLnGw{padding:24px;overflow-y:auto;flex:1}.HelpSection-module__agentSection--YGQMD{margin-bottom:32px}.HelpSection-module__agentSection--YGQMD:last-child{margin-bottom:0}.HelpSection-module__agentTitle--Q_lU2{margin:0 0 8px 0;font-size:16px;font-weight:600;color:var(--text-primary);display:flex;align-items:center;gap:8px}.HelpSection-module__agentDescription--kDkB3{margin:0 0 12px 0;font-size:14px;line-height:1.5;color:var(--text-secondary)}.HelpSection-module__examplesGrid--qzf9A{display:flex;flex-wrap:wrap;gap:8px}.HelpSection-module__exampleChip--iDF4A{padding:8px 12px;background:var(--surface-primary);border:1px solid var(--border-color);border-radius:20px;font-size:13px;color:var(--text-secondary);transition:all .2s ease}.HelpSection-module__exampleChip--iDF4A:hover{background:var(--surface-secondary);border-color:var(--border-hover);color:var(--text-primary)}.HelpSection-module__learnMore--flwm7{padding:20px 24px;border-top:1px solid var(--border-color);background:var(--surface-primary)}.HelpSection-module__learnMoreLink--Gt0XR{display:inline-flex;align-items:center;gap:8px;color:var(--primary);text-decoration:none;font-size:14px;font-weight:500;transition:opacity .2s ease}.HelpSection-module__learnMoreLink--Gt0XR:hover{opacity:.8}.HelpSection-module__learnMoreLink--Gt0XR svg{width:16px;height:16px}@keyframes HelpSection-module__fadeIn--qvi61{from{opacity:0}to{opacity:1}}@keyframes HelpSection-module__slideUp--V3C46{from{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}@media(max-width: 640px){.HelpSection-module__modal--OMl6W{width:95%;max-height:95vh}.HelpSection-module__controlsGrid--_14eU{grid-template-columns:1fr}.HelpSection-module__header--YJSdZ{padding:20px}.HelpSection-module__intro--SaFdz,.HelpSection-module__controlsSection--H1QUT,.HelpSection-module__capabilitiesSection--eLnGw{padding:20px}}',""]),i.locals={"nxtscape-sidepanel":"HelpSection-module__nxtscape-sidepanel--bXYhx",scrollable:"HelpSection-module__scrollable--f1wCt","processing-spinner":"HelpSection-module__processing-spinner--JplA4",spin:"HelpSection-module__spin--vs7PK","status-indicator--connected":"HelpSection-module__status-indicator--connected--K6YwL","status-indicator--disconnected":"HelpSection-module__status-indicator--disconnected--VHZ8V","status-indicator--processing":"HelpSection-module__status-indicator--processing--G7SKv",overlay:"HelpSection-module__overlay--LhUNt",fadeIn:"HelpSection-module__fadeIn--qvi61",modal:"HelpSection-module__modal--OMl6W",slideUp:"HelpSection-module__slideUp--V3C46",header:"HelpSection-module__header--YJSdZ",headerContent:"HelpSection-module__headerContent--fCEbM",title:"HelpSection-module__title--SUYfh",version:"HelpSection-module__version--eOmYa",closeButton:"HelpSection-module__closeButton--arDRS",intro:"HelpSection-module__intro--SaFdz",introText:"HelpSection-module__introText--AZSda",controlsSection:"HelpSection-module__controlsSection--H1QUT",sectionTitle:"HelpSection-module__sectionTitle--f1916",controlsGrid:"HelpSection-module__controlsGrid--_14eU",controlItem:"HelpSection-module__controlItem--C4Ji0",controlButton:"HelpSection-module__controlButton--w309E",controlInfo:"HelpSection-module__controlInfo--FdRr1",controlLabel:"HelpSection-module__controlLabel--whBDe",controlDesc:"HelpSection-module__controlDesc--zBB2u",interruptNote:"HelpSection-module__interruptNote--E10IK",capabilitiesSection:"HelpSection-module__capabilitiesSection--eLnGw",agentSection:"HelpSection-module__agentSection--YGQMD",agentTitle:"HelpSection-module__agentTitle--Q_lU2",agentDescription:"HelpSection-module__agentDescription--kDkB3",examplesGrid:"HelpSection-module__examplesGrid--qzf9A",exampleChip:"HelpSection-module__exampleChip--iDF4A",learnMore:"HelpSection-module__learnMore--flwm7",learnMoreLink:"HelpSection-module__learnMoreLink--Gt0XR",pulse:"HelpSection-module__pulse--wPkYZ",slideIn:"HelpSection-module__slideIn--OPEMs"};const l=i},5287:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),m=Symbol.iterator;var f={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g=Object.assign,h={};function b(e,t,n){this.props=e,this.context=t,this.refs=h,this.updater=n||f}function y(){}function v(e,t,n){this.props=e,this.context=t,this.refs=h,this.updater=n||f}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=b.prototype;var _=v.prototype=new y;_.constructor=v,g(_,b.prototype),_.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,x={current:null},S={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var a,o={},i=null,l=null;if(null!=t)for(a in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)k.call(t,a)&&!S.hasOwnProperty(a)&&(o[a]=t[a]);var s=arguments.length-2;if(1===s)o.children=r;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];o.children=c}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===o[a]&&(o[a]=s[a]);return{$$typeof:n,type:e,key:i,ref:l,props:o,_owner:x.current}}function E(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var T=/\/+/g;function P(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function M(e,t,a,o,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return i=i(s=e),e=""===o?"."+P(s,0):o,w(i)?(a="",null!=e&&(a=e.replace(T,"$&/")+"/"),M(i,t,a,"",function(e){return e})):null!=i&&(E(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,a+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(T,"$&/")+"/")+e)),t.push(i)),1;if(s=0,o=""===o?".":o+":",w(e))for(var c=0;c<e.length;c++){var u=o+P(l=e[c],c);s+=M(l,t,a,u,i)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=m&&e[m]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),c=0;!(l=e.next()).done;)s+=M(l=l.value,t,a,u=o+P(l,c++),i);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function I(e,t,n){if(null==e)return e;var r=[],a=0;return M(e,r,"","",function(e){return t.call(n,e,a++)}),r}function N(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var D={current:null},L={transition:null},z={ReactCurrentDispatcher:D,ReactCurrentBatchConfig:L,ReactCurrentOwner:x};function O(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:I,forEach:function(e,t,n){I(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return I(e,function(){t++}),t},toArray:function(e){return I(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=a,t.Profiler=i,t.PureComponent=v,t.StrictMode=o,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=z,t.act=O,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=g({},e.props),o=e.key,i=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,l=x.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)k.call(t,c)&&!S.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=r;else if(1<c){s=Array(c);for(var u=0;u<c;u++)s[u]=arguments[u+2];a.children=s}return{$$typeof:n,type:e.type,key:o,ref:i,props:a,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:N}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=L.transition;L.transition={};try{e()}finally{L.transition=t}},t.unstable_act=O,t.useCallback=function(e,t){return D.current.useCallback(e,t)},t.useContext=function(e){return D.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return D.current.useDeferredValue(e)},t.useEffect=function(e,t){return D.current.useEffect(e,t)},t.useId=function(){return D.current.useId()},t.useImperativeHandle=function(e,t,n){return D.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return D.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return D.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return D.current.useMemo(e,t)},t.useReducer=function(e,t,n){return D.current.useReducer(e,t,n)},t.useRef=function(e){return D.current.useRef(e)},t.useState=function(e){return D.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return D.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return D.current.useTransition()},t.version="18.3.1"},5338:(e,t,n)=>{var r=n(961);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},6314:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n}).join("")},t.i=function(e,n,r,a,o){"string"==typeof e&&(e=[[null,e,void 0]]);var i={};if(r)for(var l=0;l<this.length;l++){var s=this[l][0];null!=s&&(i[s]=!0)}for(var c=0;c<e.length;c++){var u=[].concat(e[c]);r&&i[u[0]]||(void 0!==o&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=o),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),a&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=a):u[4]="".concat(a)),t.push(u))}},t}},6540:(e,t,n)=>{e.exports=n(5287)},7170:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(1601),a=n.n(r),o=n(6314),i=n.n(o)()(a());i.push([e.id,':root{--background-primary: #222222;--background-secondary: #2a2a2b;--surface-primary: #333334;--surface-secondary: #3a3a3b;--surface-hover: #404041;--background-secondary-alpha-50: rgba(42, 42, 43, 0.5);--background-secondary-alpha-30: rgba(42, 42, 43, 0.3);--surface-primary-alpha-30: rgba(51, 51, 52, 0.3);--text-primary: #fafafa;--text-secondary: #a1a1aa;--text-muted: #71717a;--text-disabled: #52525b;--primary: #3b82f6;--primary-rgb: 59, 130, 246;--primary-hover: #2563eb;--primary-foreground: #fafafa;--primary-alpha-05: rgba(59, 130, 246, 0.05);--primary-alpha-10: rgba(59, 130, 246, 0.1);--primary-alpha-15: rgba(59, 130, 246, 0.15);--primary-alpha-20: rgba(59, 130, 246, 0.2);--primary-alpha-30: rgba(59, 130, 246, 0.3);--accent: #06b6d4;--accent-hover: #0891b2;--success: #22c55e;--success-alpha-05: rgba(34, 197, 94, 0.05);--success-alpha-10: rgba(34, 197, 94, 0.1);--success-alpha-20: rgba(34, 197, 94, 0.2);--success-alpha-30: rgba(34, 197, 94, 0.3);--error: #ef4444;--error-alpha-05: rgba(239, 68, 68, 0.05);--error-alpha-10: rgba(239, 68, 68, 0.1);--error-alpha-20: rgba(239, 68, 68, 0.2);--error-alpha-30: rgba(239, 68, 68, 0.3);--error-alpha-50: rgba(239, 68, 68, 0.5);--warning: #f59e0b;--warning-alpha-20: rgba(245, 158, 11, 0.2);--warning-alpha-30: rgba(245, 158, 11, 0.3);--message-bg: #1E1E22;--message-border: rgba(255, 255, 255, 0.1);--border-color: rgba(255, 255, 255, 0.1);--border-hover: rgba(255, 255, 255, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.5);--font-mono: "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Fira Code", monospace;--scrollbar-track: #222222;--scrollbar-thumb: #3a3a3b;--scrollbar-thumb-hover: #404041}@media(prefers-color-scheme: light){:root{--background-primary: #ffffff;--background-secondary: #f8f9fa;--surface-primary: #f1f3f5;--surface-secondary: #e9ecef;--surface-hover: #dee2e6;--background-secondary-alpha-50: rgba(248, 249, 250, 0.5);--background-secondary-alpha-30: rgba(248, 249, 250, 0.3);--surface-primary-alpha-30: rgba(241, 243, 245, 0.3);--text-primary: #0a0a0a;--text-secondary: #52525b;--text-muted: #71717a;--text-disabled: #a1a1aa;--primary: #2563eb;--primary-rgb: 37, 99, 235;--primary-hover: #1d4ed8;--primary-foreground: #ffffff;--primary-alpha-05: rgba(37, 99, 235, 0.05);--primary-alpha-10: rgba(37, 99, 235, 0.1);--primary-alpha-15: rgba(37, 99, 235, 0.15);--primary-alpha-20: rgba(37, 99, 235, 0.2);--primary-alpha-30: rgba(37, 99, 235, 0.3);--accent: #0891b2;--accent-hover: #0e7490;--success: #16a34a;--success-alpha-05: rgba(22, 163, 74, 0.05);--success-alpha-10: rgba(22, 163, 74, 0.1);--success-alpha-20: rgba(22, 163, 74, 0.2);--success-alpha-30: rgba(22, 163, 74, 0.3);--error: #dc2626;--error-alpha-05: rgba(220, 38, 38, 0.05);--error-alpha-10: rgba(220, 38, 38, 0.1);--error-alpha-20: rgba(220, 38, 38, 0.2);--error-alpha-30: rgba(220, 38, 38, 0.3);--error-alpha-50: rgba(220, 38, 38, 0.5);--warning: #d97706;--warning-alpha-20: rgba(217, 119, 6, 0.2);--warning-alpha-30: rgba(217, 119, 6, 0.3);--message-bg: #F4F4F5;--message-border: rgba(0, 0, 0, 0.1);--border-color: rgba(0, 0, 0, 0.1);--border-hover: rgba(0, 0, 0, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.1);--scrollbar-track: #f8f9fa;--scrollbar-thumb: #e9ecef;--scrollbar-thumb-hover: #dee2e6}}*{margin:0;padding:0;box-sizing:border-box}@keyframes StreamingMessageDisplay-module__spin--BhqdG{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes StreamingMessageDisplay-module__pulse--TvM_a{0%,100%{opacity:1}50%{opacity:.5}}@keyframes StreamingMessageDisplay-module__slideIn--vh_e2{0%{transform:translateX(100%)}100%{transform:translateX(0)}}.StreamingMessageDisplay-module__nxtscape-sidepanel--ROZfw{font-family:"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;background-color:var(--background-primary);color:var(--text-primary)}.StreamingMessageDisplay-module__scrollable--xfqHN::-webkit-scrollbar{width:6px}.StreamingMessageDisplay-module__scrollable--xfqHN::-webkit-scrollbar-track{background:var(--scrollbar-track)}.StreamingMessageDisplay-module__scrollable--xfqHN::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.StreamingMessageDisplay-module__scrollable--xfqHN::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.StreamingMessageDisplay-module__processing-spinner--vYrWB{animation:StreamingMessageDisplay-module__spin--BhqdG 1s linear infinite}.StreamingMessageDisplay-module__status-indicator--connected--uA4XE{color:var(--success)}.StreamingMessageDisplay-module__status-indicator--disconnected--F2GwV{color:var(--error)}.StreamingMessageDisplay-module__status-indicator--processing--v0D4N{color:var(--primary)}.StreamingMessageDisplay-module__container--tWZjd{display:flex;flex-direction:column;gap:.75rem;padding:0;height:100%}.StreamingMessageDisplay-module__message--inzfX{display:flex;gap:.75rem;align-items:flex-start;animation:StreamingMessageDisplay-module__fadeIn--KwkpG .3s ease-out}.StreamingMessageDisplay-module__message--user--GTI3L .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--system--mG9J6 .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--llm--wFCFA .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--tool--LN07H .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--error--fwgpb .StreamingMessageDisplay-module__messageContent--h5BzP{background:#1e1e22;border:1px solid hsla(0,0%,100%,.1)}@media(prefers-color-scheme: dark){.StreamingMessageDisplay-module__message--user--GTI3L .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--system--mG9J6 .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--llm--wFCFA .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--tool--LN07H .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--error--fwgpb .StreamingMessageDisplay-module__messageContent--h5BzP{background:#1e1e22;border:1px solid var(--border-color)}}@media(prefers-color-scheme: light){.StreamingMessageDisplay-module__message--user--GTI3L .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--system--mG9J6 .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--llm--wFCFA .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--tool--LN07H .StreamingMessageDisplay-module__messageContent--h5BzP,.StreamingMessageDisplay-module__message--error--fwgpb .StreamingMessageDisplay-module__messageContent--h5BzP{background:#f4f4f5;border:1px solid var(--border-color)}}.StreamingMessageDisplay-module__message--streaming--z8urh .StreamingMessageDisplay-module__messageContent--h5BzP{animation:StreamingMessageDisplay-module__pulse--TvM_a 2s ease-in-out infinite}.StreamingMessageDisplay-module__messageIcon--XLF6T{flex-shrink:0;width:32px;height:32px;display:flex;align-items:center;justify-content:center;border-radius:50%;font-size:16px;background:var(--surface-primary);border:1px solid var(--surface-secondary)}.StreamingMessageDisplay-module__messageIcon--XLF6T .StreamingMessageDisplay-module__userIcon--IQgfx,.StreamingMessageDisplay-module__messageIcon--XLF6T .StreamingMessageDisplay-module__systemIcon--aqC8D,.StreamingMessageDisplay-module__messageIcon--XLF6T .StreamingMessageDisplay-module__llmIcon--Pj9WB,.StreamingMessageDisplay-module__messageIcon--XLF6T .StreamingMessageDisplay-module__aiIcon--KRmjD,.StreamingMessageDisplay-module__messageIcon--XLF6T .StreamingMessageDisplay-module__toolIcon--NmLAX,.StreamingMessageDisplay-module__messageIcon--XLF6T .StreamingMessageDisplay-module__errorIcon--lSlF6{background:none}.StreamingMessageDisplay-module__messageContent--h5BzP{flex:1;padding:.75rem 1rem;border-radius:12px;transition:all .2s ease}.StreamingMessageDisplay-module__messageText--PI5mU{color:var(--text-primary) !important;white-space:pre-wrap;word-break:break-word}.StreamingMessageDisplay-module__messageText--PI5mU *{color:inherit !important}.StreamingMessageDisplay-module__toolMessage--MIYt_{display:flex;flex-direction:column;gap:.5rem}.StreamingMessageDisplay-module__toolHeader--pgnSO{display:flex;gap:.5rem;align-items:center}.StreamingMessageDisplay-module__toolName--Dyrmi{font-weight:600;color:var(--success) !important;font-size:.875rem}.StreamingMessageDisplay-module__toolArgs--TgNq4{color:var(--text-secondary) !important;font-size:.8125rem;font-family:var(--font-mono)}.StreamingMessageDisplay-module__toolResult--dxzKM{margin-top:.25rem;padding:.5rem .75rem;background:var(--surface-secondary);border-radius:8px;font-size:.875rem;color:var(--text-secondary) !important}.StreamingMessageDisplay-module__toolResult--dxzKM *{color:inherit !important}.StreamingMessageDisplay-module__cursor--GYVBp{display:inline-block;width:2px;height:1.2em;background-color:var(--text-primary);animation:StreamingMessageDisplay-module__blink--DL6oS 1s infinite;margin-left:2px;vertical-align:text-bottom}@keyframes StreamingMessageDisplay-module__fadeIn--KwkpG{from{opacity:0;transform:translateY(8px)}to{opacity:1;transform:translateY(0)}}@keyframes StreamingMessageDisplay-module__pulse--TvM_a{0%,100%{opacity:1}50%{opacity:.8}}@keyframes StreamingMessageDisplay-module__blink--DL6oS{0%,50%{opacity:1}51%,100%{opacity:0}}.StreamingMessageDisplay-module__message--streaming--z8urh .StreamingMessageDisplay-module__messageContent--h5BzP{opacity:.9}.StreamingMessageDisplay-module__toolStatus--bEb0f{margin-left:8px;font-size:12px;color:var(--text-secondary);display:inline-flex;align-items:center;gap:4px}@media(max-width: 480px){.StreamingMessageDisplay-module__container--tWZjd{padding:12px;gap:8px}.StreamingMessageDisplay-module__messageContent--h5BzP{padding:10px 14px}.StreamingMessageDisplay-module__messageIcon--XLF6T{width:28px;height:28px;font-size:14px}}.StreamingMessageDisplay-module__streamingContent--BNU4P{font-family:var(--font-mono);font-size:12px;line-height:1.5;margin:0;padding:8px;background-color:var(--bg-secondary);border-radius:4px;overflow-x:auto;white-space:pre-wrap;word-break:break-word;color:var(--text-secondary);max-height:200px;overflow-y:auto}.StreamingMessageDisplay-module__message--user--GTI3L{justify-content:flex-end}.StreamingMessageDisplay-module__message--user--GTI3L .StreamingMessageDisplay-module__messageContent--h5BzP{margin-left:auto;max-width:70%}.StreamingMessageDisplay-module__message--user--GTI3L .StreamingMessageDisplay-module__messageIcon--XLF6T{display:none}.StreamingMessageDisplay-module__message--inzfX:not(.StreamingMessageDisplay-module__message--user--GTI3L) .StreamingMessageDisplay-module__messageContent--h5BzP{margin-right:20px}',""]),i.locals={"nxtscape-sidepanel":"StreamingMessageDisplay-module__nxtscape-sidepanel--ROZfw",scrollable:"StreamingMessageDisplay-module__scrollable--xfqHN","processing-spinner":"StreamingMessageDisplay-module__processing-spinner--vYrWB",spin:"StreamingMessageDisplay-module__spin--BhqdG","status-indicator--connected":"StreamingMessageDisplay-module__status-indicator--connected--uA4XE","status-indicator--disconnected":"StreamingMessageDisplay-module__status-indicator--disconnected--F2GwV","status-indicator--processing":"StreamingMessageDisplay-module__status-indicator--processing--v0D4N",container:"StreamingMessageDisplay-module__container--tWZjd",message:"StreamingMessageDisplay-module__message--inzfX",fadeIn:"StreamingMessageDisplay-module__fadeIn--KwkpG","message--user":"StreamingMessageDisplay-module__message--user--GTI3L",messageContent:"StreamingMessageDisplay-module__messageContent--h5BzP","message--system":"StreamingMessageDisplay-module__message--system--mG9J6","message--llm":"StreamingMessageDisplay-module__message--llm--wFCFA","message--tool":"StreamingMessageDisplay-module__message--tool--LN07H","message--error":"StreamingMessageDisplay-module__message--error--fwgpb","message--streaming":"StreamingMessageDisplay-module__message--streaming--z8urh",pulse:"StreamingMessageDisplay-module__pulse--TvM_a",messageIcon:"StreamingMessageDisplay-module__messageIcon--XLF6T",userIcon:"StreamingMessageDisplay-module__userIcon--IQgfx",systemIcon:"StreamingMessageDisplay-module__systemIcon--aqC8D",llmIcon:"StreamingMessageDisplay-module__llmIcon--Pj9WB",aiIcon:"StreamingMessageDisplay-module__aiIcon--KRmjD",toolIcon:"StreamingMessageDisplay-module__toolIcon--NmLAX",errorIcon:"StreamingMessageDisplay-module__errorIcon--lSlF6",messageText:"StreamingMessageDisplay-module__messageText--PI5mU",toolMessage:"StreamingMessageDisplay-module__toolMessage--MIYt_",toolHeader:"StreamingMessageDisplay-module__toolHeader--pgnSO",toolName:"StreamingMessageDisplay-module__toolName--Dyrmi",toolArgs:"StreamingMessageDisplay-module__toolArgs--TgNq4",toolResult:"StreamingMessageDisplay-module__toolResult--dxzKM",cursor:"StreamingMessageDisplay-module__cursor--GYVBp",blink:"StreamingMessageDisplay-module__blink--DL6oS",toolStatus:"StreamingMessageDisplay-module__toolStatus--bEb0f",streamingContent:"StreamingMessageDisplay-module__streamingContent--BNU4P",slideIn:"StreamingMessageDisplay-module__slideIn--vh_e2"};const l=i},7463:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>o(s,n))c<a&&0>o(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else{if(!(c<a&&0>o(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var c=[],u=[],d=1,p=null,m=3,f=!1,g=!1,h=!1,b="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function _(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(h=!1,_(e),!g)if(null!==r(c))g=!0,L(k);else{var t=r(u);null!==t&&z(w,t.startTime-e)}}function k(e,n){g=!1,h&&(h=!1,y(E),E=-1),f=!0;var o=m;try{for(_(n),p=r(c);null!==p&&(!(p.expirationTime>n)||e&&!M());){var i=p.callback;if("function"==typeof i){p.callback=null,m=p.priorityLevel;var l=i(p.expirationTime<=n);n=t.unstable_now(),"function"==typeof l?p.callback=l:p===r(c)&&a(c),_(n)}else a(c);p=r(c)}if(null!==p)var s=!0;else{var d=r(u);null!==d&&z(w,d.startTime-n),s=!1}return s}finally{p=null,m=o,f=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var x,S=!1,C=null,E=-1,T=5,P=-1;function M(){return!(t.unstable_now()-P<T)}function I(){if(null!==C){var e=t.unstable_now();P=e;var n=!0;try{n=C(!0,e)}finally{n?x():(S=!1,C=null)}}else S=!1}if("function"==typeof v)x=function(){v(I)};else if("undefined"!=typeof MessageChannel){var N=new MessageChannel,D=N.port2;N.port1.onmessage=I,x=function(){D.postMessage(null)}}else x=function(){b(I,0)};function L(e){C=e,S||(S=!0,x())}function z(e,n){E=b(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){g||f||(g=!0,L(k))},t.unstable_forceFrameRate=function(e){0>e||125<e||(T=0<e?Math.floor(1e3/e):5)},t.unstable_getCurrentPriorityLevel=function(){return m},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},t.unstable_scheduleCallback=function(e,a,o){var i=t.unstable_now();switch("object"==typeof o&&null!==o?o="number"==typeof(o=o.delay)&&0<o?i+o:i:o=i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:l=o+l,sortIndex:-1},o>i?(e.sortIndex=o,n(u,e),null===r(c)&&e===r(u)&&(h?(y(E),E=-1):h=!0,z(w,o-i))):(e.sortIndex=l,n(c,e),g||f||(g=!0,L(k))),e},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}},7659:e=>{var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},7825:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var a=void 0!==n.layer;a&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,a&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var o=n.sourceMap;o&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},7897:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(1601),a=n.n(r),o=n(6314),i=n.n(o)()(a());i.push([e.id,':root{--background-primary: #222222;--background-secondary: #2a2a2b;--surface-primary: #333334;--surface-secondary: #3a3a3b;--surface-hover: #404041;--background-secondary-alpha-50: rgba(42, 42, 43, 0.5);--background-secondary-alpha-30: rgba(42, 42, 43, 0.3);--surface-primary-alpha-30: rgba(51, 51, 52, 0.3);--text-primary: #fafafa;--text-secondary: #a1a1aa;--text-muted: #71717a;--text-disabled: #52525b;--primary: #3b82f6;--primary-rgb: 59, 130, 246;--primary-hover: #2563eb;--primary-foreground: #fafafa;--primary-alpha-05: rgba(59, 130, 246, 0.05);--primary-alpha-10: rgba(59, 130, 246, 0.1);--primary-alpha-15: rgba(59, 130, 246, 0.15);--primary-alpha-20: rgba(59, 130, 246, 0.2);--primary-alpha-30: rgba(59, 130, 246, 0.3);--accent: #06b6d4;--accent-hover: #0891b2;--success: #22c55e;--success-alpha-05: rgba(34, 197, 94, 0.05);--success-alpha-10: rgba(34, 197, 94, 0.1);--success-alpha-20: rgba(34, 197, 94, 0.2);--success-alpha-30: rgba(34, 197, 94, 0.3);--error: #ef4444;--error-alpha-05: rgba(239, 68, 68, 0.05);--error-alpha-10: rgba(239, 68, 68, 0.1);--error-alpha-20: rgba(239, 68, 68, 0.2);--error-alpha-30: rgba(239, 68, 68, 0.3);--error-alpha-50: rgba(239, 68, 68, 0.5);--warning: #f59e0b;--warning-alpha-20: rgba(245, 158, 11, 0.2);--warning-alpha-30: rgba(245, 158, 11, 0.3);--message-bg: #1E1E22;--message-border: rgba(255, 255, 255, 0.1);--border-color: rgba(255, 255, 255, 0.1);--border-hover: rgba(255, 255, 255, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.5);--font-mono: "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Fira Code", monospace;--scrollbar-track: #222222;--scrollbar-thumb: #3a3a3b;--scrollbar-thumb-hover: #404041}@media(prefers-color-scheme: light){:root{--background-primary: #ffffff;--background-secondary: #f8f9fa;--surface-primary: #f1f3f5;--surface-secondary: #e9ecef;--surface-hover: #dee2e6;--background-secondary-alpha-50: rgba(248, 249, 250, 0.5);--background-secondary-alpha-30: rgba(248, 249, 250, 0.3);--surface-primary-alpha-30: rgba(241, 243, 245, 0.3);--text-primary: #0a0a0a;--text-secondary: #52525b;--text-muted: #71717a;--text-disabled: #a1a1aa;--primary: #2563eb;--primary-rgb: 37, 99, 235;--primary-hover: #1d4ed8;--primary-foreground: #ffffff;--primary-alpha-05: rgba(37, 99, 235, 0.05);--primary-alpha-10: rgba(37, 99, 235, 0.1);--primary-alpha-15: rgba(37, 99, 235, 0.15);--primary-alpha-20: rgba(37, 99, 235, 0.2);--primary-alpha-30: rgba(37, 99, 235, 0.3);--accent: #0891b2;--accent-hover: #0e7490;--success: #16a34a;--success-alpha-05: rgba(22, 163, 74, 0.05);--success-alpha-10: rgba(22, 163, 74, 0.1);--success-alpha-20: rgba(22, 163, 74, 0.2);--success-alpha-30: rgba(22, 163, 74, 0.3);--error: #dc2626;--error-alpha-05: rgba(220, 38, 38, 0.05);--error-alpha-10: rgba(220, 38, 38, 0.1);--error-alpha-20: rgba(220, 38, 38, 0.2);--error-alpha-30: rgba(220, 38, 38, 0.3);--error-alpha-50: rgba(220, 38, 38, 0.5);--warning: #d97706;--warning-alpha-20: rgba(217, 119, 6, 0.2);--warning-alpha-30: rgba(217, 119, 6, 0.3);--message-bg: #F4F4F5;--message-border: rgba(0, 0, 0, 0.1);--border-color: rgba(0, 0, 0, 0.1);--border-hover: rgba(0, 0, 0, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.1);--scrollbar-track: #f8f9fa;--scrollbar-thumb: #e9ecef;--scrollbar-thumb-hover: #dee2e6}}*{margin:0;padding:0;box-sizing:border-box}@keyframes TabSelector-module__spin--pAjKB{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes TabSelector-module__pulse--e55xE{0%,100%{opacity:1}50%{opacity:.5}}@keyframes TabSelector-module__slideIn--fwYWU{0%{transform:translateX(100%)}100%{transform:translateX(0)}}.TabSelector-module__nxtscape-sidepanel--pNcJD{font-family:"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;background-color:var(--background-primary);color:var(--text-primary)}.TabSelector-module__scrollable--BYVFw::-webkit-scrollbar{width:6px}.TabSelector-module__scrollable--BYVFw::-webkit-scrollbar-track{background:var(--scrollbar-track)}.TabSelector-module__scrollable--BYVFw::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.TabSelector-module__scrollable--BYVFw::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.TabSelector-module__processing-spinner--JarAL{animation:TabSelector-module__spin--pAjKB 1s linear infinite}.TabSelector-module__status-indicator--connected--hdAl1{color:var(--success)}.TabSelector-module__status-indicator--disconnected--o4FwK{color:var(--error)}.TabSelector-module__status-indicator--processing--BCxzQ{color:var(--primary)}.TabSelector-module__tabDropdown--QZMCp{position:absolute;left:0;width:100%;background-color:var(--surface-primary);border-radius:12px;box-shadow:var(--shadow-lg);z-index:1000;max-height:320px;display:flex;flex-direction:column;overflow:hidden;border:1px solid var(--border-color)}.TabSelector-module__tabDropdownAbove--PiY8N{bottom:100%;margin-bottom:.5rem}.TabSelector-module__tabDropdownHeader--IoMD6{padding:.75rem 1rem;background-color:var(--surface-secondary);border-bottom:1px solid var(--border-color);display:flex;justify-content:space-between;align-items:center}.TabSelector-module__tabDropdownTitle--AHa9e{color:var(--text-primary);font-weight:500;font-size:.9rem}.TabSelector-module__tabDropdownCloseBtn--JytTG{background:none;border:none;color:var(--text-muted);font-size:1.5rem;line-height:1;cursor:pointer;padding:0;width:20px;height:20px;display:flex;align-items:center;justify-content:center;transition:color 150ms ease;border-radius:4px}.TabSelector-module__tabDropdownCloseBtn--JytTG:hover{color:var(--text-secondary);background-color:var(--surface-hover)}.TabSelector-module__tabDropdownContent--VGDv5{flex:1;overflow-y:auto;background-color:var(--surface-primary)}.TabSelector-module__tabDropdownContent--VGDv5::-webkit-scrollbar{width:6px}.TabSelector-module__tabDropdownContent--VGDv5::-webkit-scrollbar-track{background:rgba(0,0,0,0)}.TabSelector-module__tabDropdownContent--VGDv5::-webkit-scrollbar-thumb{background-color:var(--border-color);border-radius:3px}.TabSelector-module__tabDropdownContent--VGDv5::-webkit-scrollbar-thumb:hover{background-color:var(--text-muted)}.TabSelector-module__tabsDropdownList--w8trI{list-style:none;padding:0;margin:0}.TabSelector-module__tabDropdownItem--OSd8s{display:flex;align-items:center;padding:.75rem 1rem;cursor:pointer;transition:all .2s ease;border-bottom:1px solid var(--border-color);position:relative}.TabSelector-module__tabDropdownItem--OSd8s:last-child{border-bottom:none}.TabSelector-module__tabDropdownItem--OSd8s:hover{background-color:var(--primary-alpha-10)}.TabSelector-module__tabDropdownItem--OSd8s:hover .TabSelector-module__tabTitle--qBYum{color:var(--primary)}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__active--keLK1{background-color:var(--primary-alpha-10);outline:2px solid var(--primary);outline-offset:-2px}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__active--keLK1::before{content:"";position:absolute;left:0;top:0;bottom:0;width:3px;background-color:var(--primary)}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4{position:relative;background-color:var(--primary-alpha-20);border:1px solid var(--primary)}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4::after{content:"✓";position:absolute;right:1rem;font-weight:bold;font-size:1.2rem}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4 .TabSelector-module__tabTitle--qBYum{color:var(--text-primary);font-weight:600}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4 .TabSelector-module__tabUrl--ctPEY{color:var(--text-secondary)}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4::after{color:var(--primary)}@media(prefers-color-scheme: dark){.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4{background-color:var(--primary);border:none}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4 .TabSelector-module__tabTitle--qBYum{color:var(--primary-foreground);font-weight:500}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4 .TabSelector-module__tabUrl--ctPEY{color:var(--primary-foreground);opacity:.9}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4 .TabSelector-module__tabIcon--TjSZp{filter:brightness(0) invert(1)}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__selected--x1pZ4::after{color:var(--primary-foreground)}}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__active--keLK1.TabSelector-module__selected--x1pZ4{outline:2px solid var(--primary-hover);outline-offset:-2px;background-color:var(--primary-alpha-20);border:1px solid var(--primary)}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__active--keLK1.TabSelector-module__selected--x1pZ4 .TabSelector-module__tabTitle--qBYum{color:var(--text-primary)}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__active--keLK1.TabSelector-module__selected--x1pZ4 .TabSelector-module__tabUrl--ctPEY{color:var(--text-secondary)}@media(prefers-color-scheme: dark){.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__active--keLK1.TabSelector-module__selected--x1pZ4{background-color:var(--primary);border:none}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__active--keLK1.TabSelector-module__selected--x1pZ4 .TabSelector-module__tabTitle--qBYum,.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__active--keLK1.TabSelector-module__selected--x1pZ4 .TabSelector-module__tabUrl--ctPEY{color:var(--primary-foreground)}}.TabSelector-module__tabDropdownItem--OSd8s.TabSelector-module__currentTab--mQssO .TabSelector-module__tabTitle--qBYum{color:var(--primary);font-weight:500}.TabSelector-module__tabIcon--TjSZp{width:16px;height:16px;margin-right:.75rem;display:flex;align-items:center;justify-content:center;flex-shrink:0}.TabSelector-module__tabFavicon--fYRoe{width:100%;height:100%;object-fit:contain;border-radius:2px}.TabSelector-module__defaultIcon--GZ1im{width:16px;height:16px;background-color:var(--text-muted);border-radius:50%;opacity:.6}.TabSelector-module__tabInfo--ENqz6{flex:1;min-width:0;margin-right:.5rem}.TabSelector-module__tabTitle--qBYum{color:var(--text-primary);font-size:.9rem;margin-bottom:2px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;line-height:1.3}.TabSelector-module__tabUrl--ctPEY{color:var(--text-muted);font-size:.75rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;line-height:1.2}.TabSelector-module__currentTabIndicator--Pyhvm{background-color:var(--primary);color:var(--surface-primary);padding:2px 6px;border-radius:4px;font-size:.7rem;font-weight:500;text-transform:uppercase;letter-spacing:.5px}.TabSelector-module__selectedIndicator--R50Vd{color:var(--primary);font-weight:bold;font-size:1rem;margin-left:.5rem}.TabSelector-module__tabDropdownLoading--PDUap,.TabSelector-module__tabDropdownError--YQIoA{padding:1rem;text-align:center;color:var(--text-muted);font-size:.9rem}.TabSelector-module__tabDropdownError--YQIoA{color:var(--error)}.TabSelector-module__tabDropdownEmpty--_L4Ee{padding:1rem;text-align:center;color:var(--text-muted);font-size:.9rem}.TabSelector-module__loadingIndicator--rD8Yp{display:flex;align-items:center;justify-content:center;padding:1.5rem;color:var(--text-muted);font-size:.9rem}.TabSelector-module__loadingIndicator--rD8Yp::before{content:"";width:16px;height:16px;border:2px solid var(--border-color);border-top:2px solid var(--primary);border-radius:50%;margin-right:.5rem;animation:TabSelector-module__spin--pAjKB 1s linear infinite}.TabSelector-module__noTabsMessage--J16GI{display:flex;align-items:center;justify-content:center;padding:1.5rem;color:var(--text-muted);font-size:.9rem;font-style:italic}.TabSelector-module__keyboardHints--Ix4WE{display:flex;justify-content:center;align-items:center;gap:1rem;padding:.5rem;background-color:var(--surface-secondary);border-top:1px solid var(--border);font-size:.75rem;color:var(--text-muted)}.TabSelector-module__keyboardHints--Ix4WE .TabSelector-module__hint--hbNr7{display:flex;align-items:center;gap:.25rem}.TabSelector-module__keyboardHints--Ix4WE kbd{display:inline-block;padding:2px 6px;font-size:.65rem;font-family:monospace;line-height:1;color:var(--text-primary);background-color:var(--surface-primary);border:1px solid var(--border);border-radius:2px;box-shadow:var(--shadow-sm)}@keyframes TabSelector-module__spin--pAjKB{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@media(max-width: 380px){.TabSelector-module__tabDropdownItem--OSd8s{padding:.5rem .75rem}.TabSelector-module__tabIcon--TjSZp{margin-right:.5rem}.TabSelector-module__tabTitle--qBYum{font-size:.85rem}.TabSelector-module__tabUrl--ctPEY{font-size:.7rem}}',""]),i.locals={"nxtscape-sidepanel":"TabSelector-module__nxtscape-sidepanel--pNcJD",scrollable:"TabSelector-module__scrollable--BYVFw","processing-spinner":"TabSelector-module__processing-spinner--JarAL",spin:"TabSelector-module__spin--pAjKB","status-indicator--connected":"TabSelector-module__status-indicator--connected--hdAl1","status-indicator--disconnected":"TabSelector-module__status-indicator--disconnected--o4FwK","status-indicator--processing":"TabSelector-module__status-indicator--processing--BCxzQ",tabDropdown:"TabSelector-module__tabDropdown--QZMCp",tabDropdownAbove:"TabSelector-module__tabDropdownAbove--PiY8N",tabDropdownHeader:"TabSelector-module__tabDropdownHeader--IoMD6",tabDropdownTitle:"TabSelector-module__tabDropdownTitle--AHa9e",tabDropdownCloseBtn:"TabSelector-module__tabDropdownCloseBtn--JytTG",tabDropdownContent:"TabSelector-module__tabDropdownContent--VGDv5",tabsDropdownList:"TabSelector-module__tabsDropdownList--w8trI",tabDropdownItem:"TabSelector-module__tabDropdownItem--OSd8s",tabTitle:"TabSelector-module__tabTitle--qBYum",active:"TabSelector-module__active--keLK1",selected:"TabSelector-module__selected--x1pZ4",tabUrl:"TabSelector-module__tabUrl--ctPEY",tabIcon:"TabSelector-module__tabIcon--TjSZp",currentTab:"TabSelector-module__currentTab--mQssO",tabFavicon:"TabSelector-module__tabFavicon--fYRoe",defaultIcon:"TabSelector-module__defaultIcon--GZ1im",tabInfo:"TabSelector-module__tabInfo--ENqz6",currentTabIndicator:"TabSelector-module__currentTabIndicator--Pyhvm",selectedIndicator:"TabSelector-module__selectedIndicator--R50Vd",tabDropdownLoading:"TabSelector-module__tabDropdownLoading--PDUap",tabDropdownError:"TabSelector-module__tabDropdownError--YQIoA",tabDropdownEmpty:"TabSelector-module__tabDropdownEmpty--_L4Ee",loadingIndicator:"TabSelector-module__loadingIndicator--rD8Yp",noTabsMessage:"TabSelector-module__noTabsMessage--J16GI",keyboardHints:"TabSelector-module__keyboardHints--Ix4WE",hint:"TabSelector-module__hint--hbNr7",pulse:"TabSelector-module__pulse--e55xE",slideIn:"TabSelector-module__slideIn--fwYWU"};const l=i},8493:(e,t,n)=>{var r=n(6540);var a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useState,i=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),a=r[0].inst,u=r[1];return l(function(){a.value=n,a.getSnapshot=t,c(a)&&u({inst:a})},[e,n,t]),i(function(){return c(a)&&u({inst:a}),e(function(){c(a)&&u({inst:a})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},9182:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(1601),a=n.n(r),o=n(6314),i=n.n(o)()(a());i.push([e.id,':root{--background-primary: #222222;--background-secondary: #2a2a2b;--surface-primary: #333334;--surface-secondary: #3a3a3b;--surface-hover: #404041;--background-secondary-alpha-50: rgba(42, 42, 43, 0.5);--background-secondary-alpha-30: rgba(42, 42, 43, 0.3);--surface-primary-alpha-30: rgba(51, 51, 52, 0.3);--text-primary: #fafafa;--text-secondary: #a1a1aa;--text-muted: #71717a;--text-disabled: #52525b;--primary: #3b82f6;--primary-rgb: 59, 130, 246;--primary-hover: #2563eb;--primary-foreground: #fafafa;--primary-alpha-05: rgba(59, 130, 246, 0.05);--primary-alpha-10: rgba(59, 130, 246, 0.1);--primary-alpha-15: rgba(59, 130, 246, 0.15);--primary-alpha-20: rgba(59, 130, 246, 0.2);--primary-alpha-30: rgba(59, 130, 246, 0.3);--accent: #06b6d4;--accent-hover: #0891b2;--success: #22c55e;--success-alpha-05: rgba(34, 197, 94, 0.05);--success-alpha-10: rgba(34, 197, 94, 0.1);--success-alpha-20: rgba(34, 197, 94, 0.2);--success-alpha-30: rgba(34, 197, 94, 0.3);--error: #ef4444;--error-alpha-05: rgba(239, 68, 68, 0.05);--error-alpha-10: rgba(239, 68, 68, 0.1);--error-alpha-20: rgba(239, 68, 68, 0.2);--error-alpha-30: rgba(239, 68, 68, 0.3);--error-alpha-50: rgba(239, 68, 68, 0.5);--warning: #f59e0b;--warning-alpha-20: rgba(245, 158, 11, 0.2);--warning-alpha-30: rgba(245, 158, 11, 0.3);--message-bg: #1E1E22;--message-border: rgba(255, 255, 255, 0.1);--border-color: rgba(255, 255, 255, 0.1);--border-hover: rgba(255, 255, 255, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.5);--font-mono: "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Fira Code", monospace;--scrollbar-track: #222222;--scrollbar-thumb: #3a3a3b;--scrollbar-thumb-hover: #404041}@media(prefers-color-scheme: light){:root{--background-primary: #ffffff;--background-secondary: #f8f9fa;--surface-primary: #f1f3f5;--surface-secondary: #e9ecef;--surface-hover: #dee2e6;--background-secondary-alpha-50: rgba(248, 249, 250, 0.5);--background-secondary-alpha-30: rgba(248, 249, 250, 0.3);--surface-primary-alpha-30: rgba(241, 243, 245, 0.3);--text-primary: #0a0a0a;--text-secondary: #52525b;--text-muted: #71717a;--text-disabled: #a1a1aa;--primary: #2563eb;--primary-rgb: 37, 99, 235;--primary-hover: #1d4ed8;--primary-foreground: #ffffff;--primary-alpha-05: rgba(37, 99, 235, 0.05);--primary-alpha-10: rgba(37, 99, 235, 0.1);--primary-alpha-15: rgba(37, 99, 235, 0.15);--primary-alpha-20: rgba(37, 99, 235, 0.2);--primary-alpha-30: rgba(37, 99, 235, 0.3);--accent: #0891b2;--accent-hover: #0e7490;--success: #16a34a;--success-alpha-05: rgba(22, 163, 74, 0.05);--success-alpha-10: rgba(22, 163, 74, 0.1);--success-alpha-20: rgba(22, 163, 74, 0.2);--success-alpha-30: rgba(22, 163, 74, 0.3);--error: #dc2626;--error-alpha-05: rgba(220, 38, 38, 0.05);--error-alpha-10: rgba(220, 38, 38, 0.1);--error-alpha-20: rgba(220, 38, 38, 0.2);--error-alpha-30: rgba(220, 38, 38, 0.3);--error-alpha-50: rgba(220, 38, 38, 0.5);--warning: #d97706;--warning-alpha-20: rgba(217, 119, 6, 0.2);--warning-alpha-30: rgba(217, 119, 6, 0.3);--message-bg: #F4F4F5;--message-border: rgba(0, 0, 0, 0.1);--border-color: rgba(0, 0, 0, 0.1);--border-hover: rgba(0, 0, 0, 0.2);--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);--shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.1);--scrollbar-track: #f8f9fa;--scrollbar-thumb: #e9ecef;--scrollbar-thumb-hover: #dee2e6}}*{margin:0;padding:0;box-sizing:border-box}@keyframes SidePanel-module__spin--iFBPO{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes SidePanel-module__pulse--YKKvq{0%,100%{opacity:1}50%{opacity:.5}}@keyframes SidePanel-module__slideIn--xT2xY{0%{transform:translateX(100%)}100%{transform:translateX(0)}}.SidePanel-module__nxtscape-sidepanel--Ad4Ol{font-family:"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;background-color:var(--background-primary);color:var(--text-primary)}.SidePanel-module__scrollable--WS3my::-webkit-scrollbar{width:6px}.SidePanel-module__scrollable--WS3my::-webkit-scrollbar-track{background:var(--scrollbar-track)}.SidePanel-module__scrollable--WS3my::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.SidePanel-module__scrollable--WS3my::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.SidePanel-module__processing-spinner--zpLKA{animation:SidePanel-module__spin--iFBPO 1s linear infinite}.SidePanel-module__status-indicator--connected--pXnu_{color:var(--success)}.SidePanel-module__status-indicator--disconnected--TFgiK{color:var(--error)}.SidePanel-module__status-indicator--processing--nSKq9{color:var(--primary)}.SidePanel-module__container--RwOxi{display:flex;flex-direction:column;height:100vh;width:100%;background-color:var(--background-primary);color:var(--text-primary) !important;font-family:"Inter",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;min-height:0;position:relative}.SidePanel-module__container--RwOxi *{color:inherit}.SidePanel-module__header--rANTu{display:flex;justify-content:space-between;align-items:center;padding:.75rem 1rem;height:56px;background-color:var(--background-primary);border-bottom:1px solid var(--border-color)}.SidePanel-module__headerLeft--DTgC9{display:flex;align-items:center;gap:.75rem}.SidePanel-module__brandIcon--FEZ3c{display:flex;align-items:center;justify-content:center;flex-shrink:0}.SidePanel-module__brandTitle--kKsTj{font-size:.875rem;font-weight:500;margin:0;color:var(--text-primary) !important;letter-spacing:-0.01em}.SidePanel-module__headerActions--Cq2J6{display:flex;gap:.5rem;align-items:center}.SidePanel-module__actionButton--U4_yO{display:flex;align-items:center;justify-content:center;width:32px;height:32px;padding:0;background:rgba(0,0,0,0);border:1px solid rgba(0,0,0,0);border-radius:6px;color:var(--text-muted);cursor:pointer;transition:all .2s ease}.SidePanel-module__actionButton--U4_yO:hover{color:var(--text-primary);background-color:var(--background-secondary-alpha-50);border-color:var(--surface-primary)}.SidePanel-module__actionButton--U4_yO:active{transform:scale(0.95)}.SidePanel-module__actionButton--U4_yO:has(rect):hover{color:var(--error);border-color:var(--error-alpha-30);background-color:var(--error-alpha-10)}.SidePanel-module__connectionDot--lWjvE{width:8px;height:8px;border-radius:50%;transition:all .3s ease}.SidePanel-module__connectionDotConnected--OIb6k{background-color:var(--success);box-shadow:0 0 0 3px var(--success-alpha-20)}.SidePanel-module__connectionDotDisconnected--Jb5jp{background-color:var(--error);box-shadow:0 0 0 3px var(--error-alpha-20)}.SidePanel-module__modeToggle--Xmf3V{display:flex;gap:2px;padding:2px;background-color:var(--background-secondary);border:1px solid var(--surface-primary);border-radius:8px}.SidePanel-module__modeButton--mykYB{display:flex;align-items:center;justify-content:center;gap:.25rem;padding:.25rem .5rem;background:rgba(0,0,0,0);border:none;border-radius:6px;color:var(--text-muted);cursor:pointer;transition:all .2s ease;font-size:.75rem;font-weight:500}.SidePanel-module__modeButton--mykYB:hover{color:var(--text-primary)}.SidePanel-module__modeButtonActive--rBx6d{background-color:var(--background-primary);color:var(--primary);box-shadow:var(--shadow-sm)}.SidePanel-module__modeButton--mykYB svg{width:14px;height:14px}@media(max-width: 400px){.SidePanel-module__modeLabel--Nx0u1{display:none}}.SidePanel-module__divider--cZmUM{width:1px;height:24px;background-color:var(--surface-primary);margin:0 .25rem}.SidePanel-module__mainContent--l1YyK{flex:1;display:flex;flex-direction:column;min-height:0;overflow:hidden}.SidePanel-module__welcomeState--jM9vR{flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:1.5rem;animation:SidePanel-module__fadeIn--AYG4f .3s ease}.SidePanel-module__welcomeTitle--fKTFP{font-size:1.5rem;font-weight:600;margin-bottom:1.5rem;text-align:center;color:var(--text-primary) !important}.SidePanel-module__examplesGrid--uIQg3{display:flex;flex-direction:column;gap:.75rem;width:100%;max-width:500px}.SidePanel-module__exampleCard--W8aQl{cursor:pointer;border:none;border-radius:8px;font-weight:500;transition:all .2s ease;width:100%;padding:1rem;background-color:var(--background-secondary);border:1px solid var(--surface-primary);text-align:left;transition:all .2s ease}.SidePanel-module__exampleCard--W8aQl:disabled{opacity:.6;cursor:not-allowed}.SidePanel-module__exampleCard--W8aQl:hover:not(:disabled){background-color:var(--surface-primary);border-color:var(--primary);transform:translateY(-2px);box-shadow:var(--shadow-md)}.SidePanel-module__exampleCard--W8aQl:active:not(:disabled){transform:translateY(0)}.SidePanel-module__exampleCard--W8aQl:disabled{opacity:.5;cursor:not-allowed}.SidePanel-module__exampleText--GgjQ5{font-size:.875rem;line-height:1.5;color:var(--text-secondary) !important;display:block}.SidePanel-module__exampleCard--W8aQl:hover .SidePanel-module__exampleText--GgjQ5{color:var(--text-primary) !important}.SidePanel-module__messageArea--lDOvG{flex:1;display:flex;flex-direction:column;min-height:0;padding:1rem;overflow-y:auto}.SidePanel-module__messageArea--lDOvG::-webkit-scrollbar{width:6px}.SidePanel-module__messageArea--lDOvG::-webkit-scrollbar-track{background:var(--scrollbar-track)}.SidePanel-module__messageArea--lDOvG::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.SidePanel-module__messageArea--lDOvG::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.SidePanel-module__messageDisplay--TIxHH{flex:1}.SidePanel-module__inputSection--_eqz6{flex-shrink:0;border-top:1px solid var(--border-color);background-color:var(--background-primary);padding:1rem;position:relative}.SidePanel-module__tabSelector--DFxU7{position:absolute;bottom:100%;left:1rem;right:1rem;margin-bottom:.5rem;z-index:1000}.SidePanel-module__inputForm--YLBRB{display:flex;flex-direction:column;gap:.5rem;position:relative}.SidePanel-module__inputWrapper--NzsCl{display:flex;align-items:flex-end;gap:.75rem}.SidePanel-module__inputField--d6R6P{border:1px solid var(--border-color);border-radius:8px;background:var(--surface-primary);color:var(--text-primary);font-size:.875rem;transition:all .2s ease;flex:1;min-height:40px;max-height:200px;padding:.5rem .75rem;resize:none;font-size:.875rem;line-height:1.5;color:var(--text-primary) !important;overflow-y:auto;transition:height .1s ease}.SidePanel-module__inputField--d6R6P::placeholder{color:var(--text-muted)}.SidePanel-module__inputField--d6R6P:focus{outline:none;box-shadow:0 0 0 2px var(--primary);border-color:rgba(0,0,0,0)}.SidePanel-module__inputField--d6R6P:disabled{opacity:.6;cursor:not-allowed}.SidePanel-module__inputField--d6R6P::placeholder{color:var(--text-muted) !important}.SidePanel-module__inputField--d6R6P:focus{outline:none;border-color:var(--primary);box-shadow:0 0 0 3px var(--primary-alpha-20)}.SidePanel-module__inputField--d6R6P:disabled{background-color:var(--background-secondary-alpha-50);color:var(--text-disabled) !important}.SidePanel-module__inputField--d6R6P::-webkit-scrollbar{width:6px}.SidePanel-module__inputField--d6R6P::-webkit-scrollbar-track{background:var(--scrollbar-track)}.SidePanel-module__inputField--d6R6P::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.SidePanel-module__inputField--d6R6P::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.SidePanel-module__sendButton--f9hf9{display:flex;align-items:center;justify-content:center;flex-shrink:0;width:40px;height:40px;padding:0;border:none;border-radius:8px;transition:all .2s ease;cursor:pointer}.SidePanel-module__sendButtonEnabled--HOVHi{background-color:var(--primary);color:#fff !important}.SidePanel-module__sendButtonEnabled--HOVHi:hover{background-color:var(--primary-hover);transform:scale(1.05)}.SidePanel-module__sendButtonEnabled--HOVHi:active{transform:scale(0.95)}.SidePanel-module__sendButtonDisabled--RgRzT{background-color:var(--background-secondary);color:var(--text-disabled) !important;cursor:not-allowed}.SidePanel-module__helpText--PGw5B{text-align:center;font-size:.75rem;color:var(--text-muted) !important}@keyframes SidePanel-module__fadeIn--AYG4f{from{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.SidePanel-module__welcomeHeader--IZUH3{flex-shrink:0;padding:.75rem 1rem;border-bottom:1px solid var(--border-color)}.SidePanel-module__welcomeHeader--IZUH3 .SidePanel-module__welcomeTitle--fKTFP{font-size:1rem;font-weight:500;margin:0;color:var(--text-primary)}.SidePanel-module__currentQueryHeader--tpxuH{display:flex;align-items:center;justify-content:space-between;flex-shrink:0;padding:.75rem 1rem;background-color:var(--primary-alpha-05);border-bottom:1px solid var(--primary-alpha-10)}.SidePanel-module__currentQueryHeader--tpxuH .SidePanel-module__currentQueryContent--MiAEJ{display:flex;flex-direction:column;flex:1;gap:.25rem}.SidePanel-module__currentQueryHeader--tpxuH .SidePanel-module__currentQueryContent--MiAEJ .SidePanel-module__currentQueryLabel--lryqS{font-size:.75rem;font-weight:600;color:var(--primary);text-transform:uppercase;letter-spacing:.05em}.SidePanel-module__currentQueryHeader--tpxuH .SidePanel-module__currentQueryContent--MiAEJ .SidePanel-module__currentQueryText--pLI_U{font-size:.875rem;color:var(--text-primary);font-weight:500;word-wrap:break-word;line-height:1.4}.SidePanel-module__currentQueryHeader--tpxuH .SidePanel-module__cancelButton--WP1Fy{display:flex;align-items:center;justify-content:center;width:2rem;height:2rem;padding:0;background:rgba(0,0,0,0);border:1px solid var(--error-alpha-30);border-radius:6px;color:var(--error);cursor:pointer;transition:all .2s ease}.SidePanel-module__currentQueryHeader--tpxuH .SidePanel-module__cancelButton--WP1Fy:hover{background-color:var(--error-alpha-10);border-color:var(--error);transform:scale(1.05)}.SidePanel-module__currentQueryHeader--tpxuH .SidePanel-module__cancelButton--WP1Fy:active{transform:scale(0.95)}.SidePanel-module__outputSection--BcdUf{display:flex;flex-direction:column;flex:1;min-height:0}.SidePanel-module__outputSectionHeader--bvBYT{flex-shrink:0;padding:.5rem 1rem;border-bottom:1px solid var(--border-color)}.SidePanel-module__outputSectionHeader--bvBYT h4{font-size:.75rem;font-weight:500;color:var(--text-secondary);text-transform:uppercase;letter-spacing:.05em;margin:0}.SidePanel-module__outputSectionContent--k0SG4{flex:1;padding:.75rem 1rem;overflow-y:auto}.SidePanel-module__outputSectionContent--k0SG4::-webkit-scrollbar{width:6px}.SidePanel-module__outputSectionContent--k0SG4::-webkit-scrollbar-track{background:var(--scrollbar-track)}.SidePanel-module__outputSectionContent--k0SG4::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.SidePanel-module__outputSectionContent--k0SG4::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.SidePanel-module__outputSectionContent--k0SG4 .SidePanel-module__outputText--t9_XR{font-size:.875rem;color:var(--text-secondary);line-height:1.6;white-space:pre-wrap;word-wrap:break-word}.SidePanel-module__outputSectionContent--k0SG4 .SidePanel-module__processingIndicator--PUfDo{display:flex;align-items:center;justify-content:center;gap:.5rem;margin-top:.75rem;color:var(--primary)}.SidePanel-module__outputSectionContent--k0SG4 .SidePanel-module__processingIndicator--PUfDo .SidePanel-module__spinner--KZuS3{width:.75rem;height:.75rem;border:2px solid rgba(0,0,0,0);border-top:2px solid currentColor;border-radius:50%;animation:SidePanel-module__spin--iFBPO 1s linear infinite}.SidePanel-module__badge--_3Wpz{display:inline-flex;align-items:center;padding:.125rem .5rem;border-radius:9999px;font-size:.75rem;font-weight:600;border:1px solid rgba(0,0,0,0)}.SidePanel-module__badge--success--wWq_b{background-color:var(--success-alpha-10);color:var(--success);border-color:var(--success-alpha-20)}.SidePanel-module__badge--destructive--pBjJb{background-color:rgba(var(--error), 0.1);color:var(--error);border-color:var(--error-alpha-20)}.SidePanel-module__helpButton--jsZaB:hover{color:var(--primary);border-color:var(--primary-alpha-30);background-color:var(--primary-alpha-10)}.SidePanel-module__helpOverlay--eUXdx{position:fixed;top:0;left:0;right:0;bottom:0;background-color:var(--background-secondary-alpha-50);backdrop-filter:blur(4px);z-index:1000;display:flex;align-items:center;justify-content:center;animation:SidePanel-module__fadeIn--AYG4f .2s ease}.SidePanel-module__helpModal--CzKxv{background-color:var(--background-primary);border:1px solid var(--surface-primary);border-radius:12px;width:90%;max-width:500px;max-height:80vh;overflow:hidden;box-shadow:var(--shadow-lg);animation:SidePanel-module__slideUp--rHN_r .3s ease;display:flex;flex-direction:column}.SidePanel-module__helpHeader--LzvDW{display:flex;align-items:center;justify-content:space-between;padding:1rem;border-bottom:1px solid var(--background-secondary);flex-shrink:0}.SidePanel-module__helpTitle--GD6il{font-size:1.25rem;font-weight:600;margin:0;color:var(--text-primary) !important}.SidePanel-module__helpCloseButton--HsfEP{display:flex;align-items:center;justify-content:center;width:36px;height:36px;padding:0;background:rgba(0,0,0,0);border:none;border-radius:6px;color:var(--text-muted);cursor:pointer;transition:all .2s ease}.SidePanel-module__helpCloseButton--HsfEP:hover{background-color:var(--background-secondary-alpha-50);color:var(--text-primary)}.SidePanel-module__helpContent--dX5uw{flex:1;overflow-y:auto;padding:1rem}.SidePanel-module__helpContent--dX5uw::-webkit-scrollbar{width:6px}.SidePanel-module__helpContent--dX5uw::-webkit-scrollbar-track{background:var(--scrollbar-track)}.SidePanel-module__helpContent--dX5uw::-webkit-scrollbar-thumb{background:var(--scrollbar-thumb);border-radius:3px}.SidePanel-module__helpContent--dX5uw::-webkit-scrollbar-thumb:hover{background:var(--scrollbar-thumb-hover)}.SidePanel-module__helpSection--KBHZe{margin-bottom:2rem}.SidePanel-module__helpSection--KBHZe:last-child{margin-bottom:0}.SidePanel-module__helpSectionHeader--ItW0V{display:flex;align-items:center;gap:.5rem;margin-bottom:.75rem}.SidePanel-module__helpSectionIcon--J9I3o{font-size:1.5rem}.SidePanel-module__helpSectionTitle--bD0ti{font-size:.95rem;font-weight:500;margin:0;color:var(--text-primary) !important;line-height:1.4}.SidePanel-module__helpFeatureList--hgcBq{list-style:none;padding:0;margin:0}.SidePanel-module__helpFeature--RbFcb{position:relative;padding-left:1.75rem;margin-bottom:.5rem;color:var(--text-secondary) !important;font-size:.875rem;line-height:1.6}.SidePanel-module__helpFeature--RbFcb:before{content:"•";position:absolute;left:.75rem;color:var(--primary);font-weight:bold}.SidePanel-module__helpFeature--RbFcb:last-child{margin-bottom:0}.SidePanel-module__helpFooter--XCPji{padding:1rem;border-top:1px solid var(--background-secondary);background-color:var(--primary-alpha-05);flex-shrink:0}.SidePanel-module__helpTip--WMCeD{margin:0;font-size:.875rem;color:var(--text-primary) !important}.SidePanel-module__helpTip--WMCeD strong{color:var(--primary) !important}@keyframes SidePanel-module__slideUp--rHN_r{from{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.SidePanel-module__selectedTabsContainer--s2_iR{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:12px;padding:12px;background:var(--surface-primary);border:1px solid var(--border-color);border-radius:12px;backdrop-filter:blur(10px)}.SidePanel-module__selectedTabPill--JP83q{display:flex;align-items:center;gap:6px;padding:6px 12px;border-radius:20px;font-size:13px;transition:all .2s ease;background:hsla(0,0%,100%,.08);border:1px solid hsla(0,0%,100%,.12);color:hsla(0,0%,100%,.9)}.SidePanel-module__selectedTabPill--JP83q:hover{background:hsla(0,0%,100%,.12);border-color:hsla(0,0%,100%,.2)}@media(prefers-color-scheme: light){.SidePanel-module__selectedTabPill--JP83q{background:var(--surface-secondary);border:1px solid var(--border-color);color:var(--text-primary)}.SidePanel-module__selectedTabPill--JP83q:hover{background:var(--surface-hover);border-color:var(--border-hover)}}.SidePanel-module__tabIconSmall--EgSj1{width:16px;height:16px;object-fit:contain;opacity:.8}@media(prefers-color-scheme: light){.SidePanel-module__tabIconSmall--EgSj1{filter:none}}.SidePanel-module__selectedTabTitle--lxnTa{max-width:150px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-weight:500}.SidePanel-module__removeTabBtn--L5kwT{background:none;border:none;font-size:18px;line-height:1;cursor:pointer;padding:0;margin-left:4px;transition:all .2s ease;color:hsla(0,0%,100%,.5)}.SidePanel-module__removeTabBtn--L5kwT:hover{color:hsla(0,0%,100%,.9)}@media(prefers-color-scheme: light){.SidePanel-module__removeTabBtn--L5kwT{color:var(--text-muted)}.SidePanel-module__removeTabBtn--L5kwT:hover{color:var(--text-primary)}}.SidePanel-module__debugBox--ohoX6{background-color:var(--background-secondary-alpha-30);border-bottom:1px solid var(--surface-primary);font-family:"SF Mono","Monaco","Consolas",monospace;font-size:.75rem}.SidePanel-module__debugDetails--zwyEV{margin:0}.SidePanel-module__debugDetails--zwyEV[open] .SidePanel-module__debugSummary--hKnlE::before{transform:rotate(90deg)}.SidePanel-module__debugSummary--hKnlE{padding:.5rem 1rem;cursor:pointer;user-select:none;list-style:none;color:var(--text-muted);display:flex;align-items:center;gap:.5rem;transition:color .2s ease}.SidePanel-module__debugSummary--hKnlE:hover{color:var(--text-primary)}.SidePanel-module__debugSummary--hKnlE::-webkit-details-marker{display:none}.SidePanel-module__debugSummary--hKnlE::before{content:"▶";font-size:.625rem;transition:transform .2s ease}.SidePanel-module__debugRefreshButton--CNzuu{margin-left:auto;background:rgba(0,0,0,0);border:1px solid rgba(0,0,0,0);border-radius:6px;padding:4px;color:var(--text-muted);cursor:pointer;transition:all .2s ease;display:flex;align-items:center;justify-content:center}.SidePanel-module__debugRefreshButton--CNzuu:hover{color:var(--primary);background-color:var(--primary-alpha-10);border-color:rgba(var(--primary), 0.2);transform:rotate(180deg)}.SidePanel-module__debugRefreshButton--CNzuu:active{transform:rotate(180deg) scale(0.9)}.SidePanel-module__debugProvider--VxcaR{color:var(--primary);font-weight:600}.SidePanel-module__debugContent--MS6hZ{padding:0 1rem .75rem 1rem;animation:SidePanel-module__fadeIn--AYG4f .2s ease}.SidePanel-module__debugError--wwbxV{color:var(--error);padding:.5rem;background-color:rgba(var(--error), 0.1);border-radius:6px;border:1px solid var(--error-alpha-20)}.SidePanel-module__debugLoading--_BWxo{color:var(--text-muted);font-style:italic}.SidePanel-module__debugSettings--oyAl5{display:flex;flex-direction:column;gap:.75rem}.SidePanel-module__debugSection--u6glJ{border-left:2px solid var(--surface-primary);padding-left:.75rem}.SidePanel-module__debugSection--u6glJ:first-child{border-left:none;padding-left:0}.SidePanel-module__debugSectionTitle--E6Pyi{font-weight:600;color:var(--text-primary);margin-bottom:.25rem;text-transform:uppercase;font-size:.7rem;letter-spacing:.05em}.SidePanel-module__debugItem--Mse7o{color:var(--text-secondary);line-height:1.4;word-break:break-all}.SidePanel-module__debugItem--Mse7o strong{color:var(--text-primary)}.SidePanel-module__debugNote--lF9_v{margin-top:.75rem;padding:.5rem;background-color:rgba(var(--primary), 0.1);border:1px solid var(--primary-alpha-20);border-radius:6px;color:var(--primary);font-size:.75rem;text-align:center}',""]),i.locals={"nxtscape-sidepanel":"SidePanel-module__nxtscape-sidepanel--Ad4Ol",scrollable:"SidePanel-module__scrollable--WS3my","processing-spinner":"SidePanel-module__processing-spinner--zpLKA",spin:"SidePanel-module__spin--iFBPO","status-indicator--connected":"SidePanel-module__status-indicator--connected--pXnu_","status-indicator--disconnected":"SidePanel-module__status-indicator--disconnected--TFgiK","status-indicator--processing":"SidePanel-module__status-indicator--processing--nSKq9",container:"SidePanel-module__container--RwOxi",header:"SidePanel-module__header--rANTu",headerLeft:"SidePanel-module__headerLeft--DTgC9",brandIcon:"SidePanel-module__brandIcon--FEZ3c",brandTitle:"SidePanel-module__brandTitle--kKsTj",headerActions:"SidePanel-module__headerActions--Cq2J6",actionButton:"SidePanel-module__actionButton--U4_yO",connectionDot:"SidePanel-module__connectionDot--lWjvE",connectionDotConnected:"SidePanel-module__connectionDotConnected--OIb6k",connectionDotDisconnected:"SidePanel-module__connectionDotDisconnected--Jb5jp",modeToggle:"SidePanel-module__modeToggle--Xmf3V",modeButton:"SidePanel-module__modeButton--mykYB",modeButtonActive:"SidePanel-module__modeButtonActive--rBx6d",modeLabel:"SidePanel-module__modeLabel--Nx0u1",divider:"SidePanel-module__divider--cZmUM",mainContent:"SidePanel-module__mainContent--l1YyK",welcomeState:"SidePanel-module__welcomeState--jM9vR",fadeIn:"SidePanel-module__fadeIn--AYG4f",welcomeTitle:"SidePanel-module__welcomeTitle--fKTFP",examplesGrid:"SidePanel-module__examplesGrid--uIQg3",exampleCard:"SidePanel-module__exampleCard--W8aQl",exampleText:"SidePanel-module__exampleText--GgjQ5",messageArea:"SidePanel-module__messageArea--lDOvG",messageDisplay:"SidePanel-module__messageDisplay--TIxHH",inputSection:"SidePanel-module__inputSection--_eqz6",tabSelector:"SidePanel-module__tabSelector--DFxU7",inputForm:"SidePanel-module__inputForm--YLBRB",inputWrapper:"SidePanel-module__inputWrapper--NzsCl",inputField:"SidePanel-module__inputField--d6R6P",sendButton:"SidePanel-module__sendButton--f9hf9",sendButtonEnabled:"SidePanel-module__sendButtonEnabled--HOVHi",sendButtonDisabled:"SidePanel-module__sendButtonDisabled--RgRzT",helpText:"SidePanel-module__helpText--PGw5B",welcomeHeader:"SidePanel-module__welcomeHeader--IZUH3",currentQueryHeader:"SidePanel-module__currentQueryHeader--tpxuH",currentQueryContent:"SidePanel-module__currentQueryContent--MiAEJ",currentQueryLabel:"SidePanel-module__currentQueryLabel--lryqS",currentQueryText:"SidePanel-module__currentQueryText--pLI_U",cancelButton:"SidePanel-module__cancelButton--WP1Fy",outputSection:"SidePanel-module__outputSection--BcdUf",outputSectionHeader:"SidePanel-module__outputSectionHeader--bvBYT",outputSectionContent:"SidePanel-module__outputSectionContent--k0SG4",outputText:"SidePanel-module__outputText--t9_XR",processingIndicator:"SidePanel-module__processingIndicator--PUfDo",spinner:"SidePanel-module__spinner--KZuS3",badge:"SidePanel-module__badge--_3Wpz","badge--success":"SidePanel-module__badge--success--wWq_b","badge--destructive":"SidePanel-module__badge--destructive--pBjJb",helpButton:"SidePanel-module__helpButton--jsZaB",helpOverlay:"SidePanel-module__helpOverlay--eUXdx",helpModal:"SidePanel-module__helpModal--CzKxv",slideUp:"SidePanel-module__slideUp--rHN_r",helpHeader:"SidePanel-module__helpHeader--LzvDW",helpTitle:"SidePanel-module__helpTitle--GD6il",helpCloseButton:"SidePanel-module__helpCloseButton--HsfEP",helpContent:"SidePanel-module__helpContent--dX5uw",helpSection:"SidePanel-module__helpSection--KBHZe",helpSectionHeader:"SidePanel-module__helpSectionHeader--ItW0V",helpSectionIcon:"SidePanel-module__helpSectionIcon--J9I3o",helpSectionTitle:"SidePanel-module__helpSectionTitle--bD0ti",helpFeatureList:"SidePanel-module__helpFeatureList--hgcBq",helpFeature:"SidePanel-module__helpFeature--RbFcb",helpFooter:"SidePanel-module__helpFooter--XCPji",helpTip:"SidePanel-module__helpTip--WMCeD",selectedTabsContainer:"SidePanel-module__selectedTabsContainer--s2_iR",selectedTabPill:"SidePanel-module__selectedTabPill--JP83q",tabIconSmall:"SidePanel-module__tabIconSmall--EgSj1",selectedTabTitle:"SidePanel-module__selectedTabTitle--lxnTa",removeTabBtn:"SidePanel-module__removeTabBtn--L5kwT",debugBox:"SidePanel-module__debugBox--ohoX6",debugDetails:"SidePanel-module__debugDetails--zwyEV",debugSummary:"SidePanel-module__debugSummary--hKnlE",debugRefreshButton:"SidePanel-module__debugRefreshButton--CNzuu",debugProvider:"SidePanel-module__debugProvider--VxcaR",debugContent:"SidePanel-module__debugContent--MS6hZ",debugError:"SidePanel-module__debugError--wwbxV",debugLoading:"SidePanel-module__debugLoading--_BWxo",debugSettings:"SidePanel-module__debugSettings--oyAl5",debugSection:"SidePanel-module__debugSection--u6glJ",debugSectionTitle:"SidePanel-module__debugSectionTitle--E6Pyi",debugItem:"SidePanel-module__debugItem--Mse7o",debugNote:"SidePanel-module__debugNote--lF9_v",pulse:"SidePanel-module__pulse--YKKvq",slideIn:"SidePanel-module__slideIn--xT2xY"};const l=i},9242:(e,t,n)=>{e.exports=n(2162)},9888:(e,t,n)=>{e.exports=n(8493)},9982:(e,t,n)=>{e.exports=n(7463)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={id:r,exports:{}};return e[r](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0;var r,a,o=n(6540),i=n(5338);!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},e.getValidEnumValues=t=>{const n=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),r={};for(const e of n)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},e.find=(e,t)=>{for(const n of e)if(t(n))return n},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(r||(r={})),function(e){e.mergeShapes=(e,t)=>({...e,...t})}(a||(a={}));const l=r.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),s=e=>{switch(typeof e){case"undefined":return l.undefined;case"string":return l.string;case"number":return Number.isNaN(e)?l.nan:l.number;case"boolean":return l.boolean;case"function":return l.function;case"bigint":return l.bigint;case"symbol":return l.symbol;case"object":return Array.isArray(e)?l.array:null===e?l.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?l.promise:"undefined"!=typeof Map&&e instanceof Map?l.map:"undefined"!=typeof Set&&e instanceof Set?l.set:"undefined"!=typeof Date&&e instanceof Date?l.date:l.object;default:return l.unknown}},c=r.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class u extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(e){return e.message},n={_errors:[]},r=e=>{for(const a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(r);else if("invalid_return_type"===a.code)r(a.returnTypeError);else if("invalid_arguments"===a.code)r(a.argumentsError);else if(0===a.path.length)n._errors.push(t(a));else{let e=n,r=0;for(;r<a.path.length;){const n=a.path[r];r===a.path.length-1?(e[n]=e[n]||{_errors:[]},e[n]._errors.push(t(a))):e[n]=e[n]||{_errors:[]},e=e[n],r++}}};return r(this),n}static assert(e){if(!(e instanceof u))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,r.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},n=[];for(const r of this.issues)if(r.path.length>0){const n=r.path[0];t[n]=t[n]||[],t[n].push(e(r))}else n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}u.create=e=>new u(e);const d=(e,t)=>{let n;switch(e.code){case c.invalid_type:n=e.received===l.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case c.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,r.jsonStringifyReplacer)}`;break;case c.unrecognized_keys:n=`Unrecognized key(s) in object: ${r.joinValues(e.keys,", ")}`;break;case c.invalid_union:n="Invalid input";break;case c.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${r.joinValues(e.options)}`;break;case c.invalid_enum_value:n=`Invalid enum value. Expected ${r.joinValues(e.options)}, received '${e.received}'`;break;case c.invalid_arguments:n="Invalid function arguments";break;case c.invalid_return_type:n="Invalid function return type";break;case c.invalid_date:n="Invalid date";break;case c.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:r.assertNever(e.validation):n="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case c.too_small:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type||"bigint"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case c.too_big:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case c.custom:n="Invalid input";break;case c.invalid_intersection_types:n="Intersection results could not be merged";break;case c.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case c.not_finite:n="Number must be finite";break;default:n=t.defaultError,r.assertNever(e)}return{message:n}};let p=d;function m(){return p}var f;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(f||(f={}));const g=e=>{const{data:t,path:n,errorMaps:r,issueData:a}=e,o=[...n,...a.path||[]],i={...a,path:o};if(void 0!==a.message)return{...a,path:o,message:a.message};let l="";const s=r.filter(e=>!!e).slice().reverse();for(const e of s)l=e(i,{data:t,defaultError:l}).message;return{...a,path:o,message:l}};function h(e,t){const n=m(),r=g({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===d?void 0:d].filter(e=>!!e)});e.common.issues.push(r)}class b{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if("aborted"===r.status)return y;"dirty"===r.status&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const e of t){const t=await e.key,r=await e.value;n.push({key:t,value:r})}return b.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:t,value:a}=r;if("aborted"===t.status)return y;if("aborted"===a.status)return y;"dirty"===t.status&&e.dirty(),"dirty"===a.status&&e.dirty(),"__proto__"===t.value||void 0===a.value&&!r.alwaysSet||(n[t.value]=a.value)}return{status:e.value,value:n}}}const y=Object.freeze({status:"aborted"}),v=e=>({status:"dirty",value:e}),_=e=>({status:"valid",value:e}),w=e=>"aborted"===e.status,k=e=>"dirty"===e.status,x=e=>"valid"===e.status,S=e=>"undefined"!=typeof Promise&&e instanceof Promise;class C{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const E=(e,t)=>{if(x(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new u(e.common.issues);return this._error=t,this._error}}};function T(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:a}=e;if(t&&(n||r))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:a};return{errorMap:(t,a)=>{const{message:o}=e;return"invalid_enum_value"===t.code?{message:o??a.defaultError}:void 0===a.data?{message:o??r??a.defaultError}:"invalid_type"!==t.code?{message:a.defaultError}:{message:o??n??a.defaultError}},description:a}}class P{get description(){return this._def.description}_getType(e){return s(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:s(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new b,ctx:{common:e.parent.common,data:e.data,parsedType:s(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(S(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){const n={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)},r=this._parseSync({data:e,path:n.path,parent:n});return E(n,r)}"~validate"(e){const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)};if(!this["~standard"].async)try{const n=this._parseSync({data:e,path:[],parent:t});return x(n)?{value:n.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>x(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)},r=this._parse({data:e,path:n.path,parent:n}),a=await(S(r)?r:Promise.resolve(r));return E(n,a)}refine(e,t){const n=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,r)=>{const a=e(t),o=()=>r.addIssue({code:c.custom,...n(t)});return"undefined"!=typeof Promise&&a instanceof Promise?a.then(e=>!!e||(o(),!1)):!!a||(o(),!1)})}refinement(e,t){return this._refinement((n,r)=>!!e(n)||(r.addIssue("function"==typeof t?t(n,r):t),!1))}_refinement(e){return new Me({schema:this,typeName:Fe.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return Ie.create(this,this._def)}nullable(){return Ne.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return de.create(this)}promise(){return Pe.create(this,this._def)}or(e){return fe.create([this,e],this._def)}and(e){return ye.create(this,e,this._def)}transform(e){return new Me({...T(this._def),schema:this,typeName:Fe.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new De({...T(this._def),innerType:this,defaultValue:t,typeName:Fe.ZodDefault})}brand(){return new Oe({typeName:Fe.ZodBranded,type:this,...T(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new Le({...T(this._def),innerType:this,catchValue:t,typeName:Fe.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return Ae.create(this,e)}readonly(){return Re.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const M=/^c[^\s-]{8,}$/i,I=/^[0-9a-z]+$/,N=/^[0-9A-HJKMNP-TV-Z]{26}$/i,D=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,L=/^[a-z0-9_-]{21}$/i,z=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,O=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,A=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let R;const F=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,B=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,H=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,U=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,j=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Z=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,$="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",V=new RegExp(`^${$}$`);function W(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${e.precision?"+":"?"}`}function K(e){return new RegExp(`^${W(e)}$`)}function G(e){let t=`${$}T${W(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function Y(e,t){return!("v4"!==t&&t||!F.test(e))||!("v6"!==t&&t||!H.test(e))}function Q(e,t){if(!z.test(e))return!1;try{const[n]=e.split(".");if(!n)return!1;const r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),a=JSON.parse(atob(r));return"object"==typeof a&&null!==a&&((!("typ"in a)||"JWT"===a?.typ)&&(!!a.alg&&(!t||a.alg===t)))}catch{return!1}}function q(e,t){return!("v4"!==t&&t||!B.test(e))||!("v6"!==t&&t||!U.test(e))}class X extends P{_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==l.string){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.string,received:t.parsedType}),y}const t=new b;let n;for(const a of this._def.checks)if("min"===a.kind)e.data.length<a.value&&(n=this._getOrReturnCtx(e,n),h(n,{code:c.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),t.dirty());else if("max"===a.kind)e.data.length>a.value&&(n=this._getOrReturnCtx(e,n),h(n,{code:c.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),t.dirty());else if("length"===a.kind){const r=e.data.length>a.value,o=e.data.length<a.value;(r||o)&&(n=this._getOrReturnCtx(e,n),r?h(n,{code:c.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):o&&h(n,{code:c.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),t.dirty())}else if("email"===a.kind)A.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"email",code:c.invalid_string,message:a.message}),t.dirty());else if("emoji"===a.kind)R||(R=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),R.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"emoji",code:c.invalid_string,message:a.message}),t.dirty());else if("uuid"===a.kind)D.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"uuid",code:c.invalid_string,message:a.message}),t.dirty());else if("nanoid"===a.kind)L.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"nanoid",code:c.invalid_string,message:a.message}),t.dirty());else if("cuid"===a.kind)M.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"cuid",code:c.invalid_string,message:a.message}),t.dirty());else if("cuid2"===a.kind)I.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"cuid2",code:c.invalid_string,message:a.message}),t.dirty());else if("ulid"===a.kind)N.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"ulid",code:c.invalid_string,message:a.message}),t.dirty());else if("url"===a.kind)try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),h(n,{validation:"url",code:c.invalid_string,message:a.message}),t.dirty()}else if("regex"===a.kind){a.regex.lastIndex=0;a.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"regex",code:c.invalid_string,message:a.message}),t.dirty())}else if("trim"===a.kind)e.data=e.data.trim();else if("includes"===a.kind)e.data.includes(a.value,a.position)||(n=this._getOrReturnCtx(e,n),h(n,{code:c.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),t.dirty());else if("toLowerCase"===a.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===a.kind)e.data=e.data.toUpperCase();else if("startsWith"===a.kind)e.data.startsWith(a.value)||(n=this._getOrReturnCtx(e,n),h(n,{code:c.invalid_string,validation:{startsWith:a.value},message:a.message}),t.dirty());else if("endsWith"===a.kind)e.data.endsWith(a.value)||(n=this._getOrReturnCtx(e,n),h(n,{code:c.invalid_string,validation:{endsWith:a.value},message:a.message}),t.dirty());else if("datetime"===a.kind){G(a).test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{code:c.invalid_string,validation:"datetime",message:a.message}),t.dirty())}else if("date"===a.kind){V.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{code:c.invalid_string,validation:"date",message:a.message}),t.dirty())}else if("time"===a.kind){K(a).test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{code:c.invalid_string,validation:"time",message:a.message}),t.dirty())}else"duration"===a.kind?O.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"duration",code:c.invalid_string,message:a.message}),t.dirty()):"ip"===a.kind?Y(e.data,a.version)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"ip",code:c.invalid_string,message:a.message}),t.dirty()):"jwt"===a.kind?Q(e.data,a.alg)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"jwt",code:c.invalid_string,message:a.message}),t.dirty()):"cidr"===a.kind?q(e.data,a.version)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"cidr",code:c.invalid_string,message:a.message}),t.dirty()):"base64"===a.kind?j.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"base64",code:c.invalid_string,message:a.message}),t.dirty()):"base64url"===a.kind?Z.test(e.data)||(n=this._getOrReturnCtx(e,n),h(n,{validation:"base64url",code:c.invalid_string,message:a.message}),t.dirty()):r.assertNever(a);return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(t=>e.test(t),{validation:t,code:c.invalid_string,...f.errToObj(n)})}_addCheck(e){return new X({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...f.errToObj(e)})}url(e){return this._addCheck({kind:"url",...f.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...f.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...f.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...f.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...f.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...f.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...f.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...f.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...f.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...f.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...f.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...f.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...f.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...f.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...f.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...f.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...f.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...f.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...f.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...f.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...f.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...f.errToObj(t)})}nonempty(e){return this.min(1,f.errToObj(e))}trim(){return new X({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new X({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new X({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function J(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,a=n>r?n:r;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}X.create=e=>new X({checks:[],typeName:Fe.ZodString,coerce:e?.coerce??!1,...T(e)});class ee extends P{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==l.number){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.number,received:t.parsedType}),y}let t;const n=new b;for(const a of this._def.checks)if("int"===a.kind)r.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),h(t,{code:c.invalid_type,expected:"integer",received:"float",message:a.message}),n.dirty());else if("min"===a.kind){(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),h(t,{code:c.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty())}else if("max"===a.kind){(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),h(t,{code:c.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty())}else"multipleOf"===a.kind?0!==J(e.data,a.value)&&(t=this._getOrReturnCtx(e,t),h(t,{code:c.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),h(t,{code:c.not_finite,message:a.message}),n.dirty()):r.assertNever(a);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,f.toString(t))}gt(e,t){return this.setLimit("min",e,!1,f.toString(t))}lte(e,t){return this.setLimit("max",e,!0,f.toString(t))}lt(e,t){return this.setLimit("max",e,!1,f.toString(t))}setLimit(e,t,n,r){return new ee({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:f.toString(r)}]})}_addCheck(e){return new ee({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:f.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:f.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:f.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:f.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:f.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:f.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:f.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:f.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:f.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&r.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if("finite"===n.kind||"int"===n.kind||"multipleOf"===n.kind)return!0;"min"===n.kind?(null===t||n.value>t)&&(t=n.value):"max"===n.kind&&(null===e||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}ee.create=e=>new ee({checks:[],typeName:Fe.ZodNumber,coerce:e?.coerce||!1,...T(e)});class te extends P{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==l.bigint)return this._getInvalidInput(e);let t;const n=new b;for(const a of this._def.checks)if("min"===a.kind){(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),h(t,{code:c.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty())}else if("max"===a.kind){(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),h(t,{code:c.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty())}else"multipleOf"===a.kind?e.data%a.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),h(t,{code:c.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):r.assertNever(a);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.bigint,received:t.parsedType}),y}gte(e,t){return this.setLimit("min",e,!0,f.toString(t))}gt(e,t){return this.setLimit("min",e,!1,f.toString(t))}lte(e,t){return this.setLimit("max",e,!0,f.toString(t))}lt(e,t){return this.setLimit("max",e,!1,f.toString(t))}setLimit(e,t,n,r){return new te({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:f.toString(r)}]})}_addCheck(e){return new te({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:f.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:f.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:f.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:f.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:f.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}te.create=e=>new te({checks:[],typeName:Fe.ZodBigInt,coerce:e?.coerce??!1,...T(e)});class ne extends P{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==l.boolean){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.boolean,received:t.parsedType}),y}return _(e.data)}}ne.create=e=>new ne({typeName:Fe.ZodBoolean,coerce:e?.coerce||!1,...T(e)});class re extends P{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==l.date){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.date,received:t.parsedType}),y}if(Number.isNaN(e.data.getTime())){return h(this._getOrReturnCtx(e),{code:c.invalid_date}),y}const t=new b;let n;for(const a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(n=this._getOrReturnCtx(e,n),h(n,{code:c.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),t.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(n=this._getOrReturnCtx(e,n),h(n,{code:c.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),t.dirty()):r.assertNever(a);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new re({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:f.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:f.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}re.create=e=>new re({checks:[],coerce:e?.coerce||!1,typeName:Fe.ZodDate,...T(e)});class ae extends P{_parse(e){if(this._getType(e)!==l.symbol){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.symbol,received:t.parsedType}),y}return _(e.data)}}ae.create=e=>new ae({typeName:Fe.ZodSymbol,...T(e)});class oe extends P{_parse(e){if(this._getType(e)!==l.undefined){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.undefined,received:t.parsedType}),y}return _(e.data)}}oe.create=e=>new oe({typeName:Fe.ZodUndefined,...T(e)});class ie extends P{_parse(e){if(this._getType(e)!==l.null){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.null,received:t.parsedType}),y}return _(e.data)}}ie.create=e=>new ie({typeName:Fe.ZodNull,...T(e)});class le extends P{constructor(){super(...arguments),this._any=!0}_parse(e){return _(e.data)}}le.create=e=>new le({typeName:Fe.ZodAny,...T(e)});class se extends P{constructor(){super(...arguments),this._unknown=!0}_parse(e){return _(e.data)}}se.create=e=>new se({typeName:Fe.ZodUnknown,...T(e)});class ce extends P{_parse(e){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.never,received:t.parsedType}),y}}ce.create=e=>new ce({typeName:Fe.ZodNever,...T(e)});class ue extends P{_parse(e){if(this._getType(e)!==l.undefined){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.void,received:t.parsedType}),y}return _(e.data)}}ue.create=e=>new ue({typeName:Fe.ZodVoid,...T(e)});class de extends P{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==l.array)return h(t,{code:c.invalid_type,expected:l.array,received:t.parsedType}),y;if(null!==r.exactLength){const e=t.data.length>r.exactLength.value,a=t.data.length<r.exactLength.value;(e||a)&&(h(t,{code:e?c.too_big:c.too_small,minimum:a?r.exactLength.value:void 0,maximum:e?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(null!==r.minLength&&t.data.length<r.minLength.value&&(h(t,{code:c.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),null!==r.maxLength&&t.data.length>r.maxLength.value&&(h(t,{code:c.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((e,n)=>r.type._parseAsync(new C(t,e,t.path,n)))).then(e=>b.mergeArray(n,e));const a=[...t.data].map((e,n)=>r.type._parseSync(new C(t,e,t.path,n)));return b.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new de({...this._def,minLength:{value:e,message:f.toString(t)}})}max(e,t){return new de({...this._def,maxLength:{value:e,message:f.toString(t)}})}length(e,t){return new de({...this._def,exactLength:{value:e,message:f.toString(t)}})}nonempty(e){return this.min(1,e)}}function pe(e){if(e instanceof me){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=Ie.create(pe(r))}return new me({...e._def,shape:()=>t})}return e instanceof de?new de({...e._def,type:pe(e.element)}):e instanceof Ie?Ie.create(pe(e.unwrap())):e instanceof Ne?Ne.create(pe(e.unwrap())):e instanceof ve?ve.create(e.items.map(e=>pe(e))):e}de.create=(e,t)=>new de({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Fe.ZodArray,...T(t)});class me extends P{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=r.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==l.object){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.object,received:t.parsedType}),y}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),o=[];if(!(this._def.catchall instanceof ce&&"strip"===this._def.unknownKeys))for(const e in n.data)a.includes(e)||o.push(e);const i=[];for(const e of a){const t=r[e],a=n.data[e];i.push({key:{status:"valid",value:e},value:t._parse(new C(n,a,n.path,e)),alwaysSet:e in n.data})}if(this._def.catchall instanceof ce){const e=this._def.unknownKeys;if("passthrough"===e)for(const e of o)i.push({key:{status:"valid",value:e},value:{status:"valid",value:n.data[e]}});else if("strict"===e)o.length>0&&(h(n,{code:c.unrecognized_keys,keys:o}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of o){const r=n.data[t];i.push({key:{status:"valid",value:t},value:e._parse(new C(n,r,n.path,t)),alwaysSet:t in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const e=[];for(const t of i){const n=await t.key,r=await t.value;e.push({key:n,value:r,alwaysSet:t.alwaysSet})}return e}).then(e=>b.mergeObjectSync(t,e)):b.mergeObjectSync(t,i)}get shape(){return this._def.shape()}strict(e){return f.errToObj,new me({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,n)=>{const r=this._def.errorMap?.(t,n).message??n.defaultError;return"unrecognized_keys"===t.code?{message:f.errToObj(e).message??r}:{message:r}}}:{}})}strip(){return new me({...this._def,unknownKeys:"strip"})}passthrough(){return new me({...this._def,unknownKeys:"passthrough"})}extend(e){return new me({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new me({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Fe.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new me({...this._def,catchall:e})}pick(e){const t={};for(const n of r.objectKeys(e))e[n]&&this.shape[n]&&(t[n]=this.shape[n]);return new me({...this._def,shape:()=>t})}omit(e){const t={};for(const n of r.objectKeys(this.shape))e[n]||(t[n]=this.shape[n]);return new me({...this._def,shape:()=>t})}deepPartial(){return pe(this)}partial(e){const t={};for(const n of r.objectKeys(this.shape)){const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}return new me({...this._def,shape:()=>t})}required(e){const t={};for(const n of r.objectKeys(this.shape))if(e&&!e[n])t[n]=this.shape[n];else{let e=this.shape[n];for(;e instanceof Ie;)e=e._def.innerType;t[n]=e}return new me({...this._def,shape:()=>t})}keyof(){return Ce(r.objectKeys(this.shape))}}me.create=(e,t)=>new me({shape:()=>e,unknownKeys:"strip",catchall:ce.create(),typeName:Fe.ZodObject,...T(t)}),me.strictCreate=(e,t)=>new me({shape:()=>e,unknownKeys:"strict",catchall:ce.create(),typeName:Fe.ZodObject,...T(t)}),me.lazycreate=(e,t)=>new me({shape:e,unknownKeys:"strip",catchall:ce.create(),typeName:Fe.ZodObject,...T(t)});class fe extends P{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async e=>{const n={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:n}),ctx:n}})).then(function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const n of e)if("dirty"===n.result.status)return t.common.issues.push(...n.ctx.common.issues),n.result;const n=e.map(e=>new u(e.ctx.common.issues));return h(t,{code:c.invalid_union,unionErrors:n}),y});{let e;const r=[];for(const a of n){const n={...t,common:{...t.common,issues:[]},parent:null},o=a._parseSync({data:t.data,path:t.path,parent:n});if("valid"===o.status)return o;"dirty"!==o.status||e||(e={result:o,ctx:n}),n.common.issues.length&&r.push(n.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const a=r.map(e=>new u(e));return h(t,{code:c.invalid_union,unionErrors:a}),y}}get options(){return this._def.options}}fe.create=(e,t)=>new fe({options:e,typeName:Fe.ZodUnion,...T(t)});const ge=e=>e instanceof xe?ge(e.schema):e instanceof Me?ge(e.innerType()):e instanceof Se?[e.value]:e instanceof Ee?e.options:e instanceof Te?r.objectValues(e.enum):e instanceof De?ge(e._def.innerType):e instanceof oe?[void 0]:e instanceof ie?[null]:e instanceof Ie?[void 0,...ge(e.unwrap())]:e instanceof Ne?[null,...ge(e.unwrap())]:e instanceof Oe||e instanceof Re?ge(e.unwrap()):e instanceof Le?ge(e._def.innerType):[];class he extends P{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==l.object)return h(t,{code:c.invalid_type,expected:l.object,received:t.parsedType}),y;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(h(t,{code:c.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),y)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const n of t){const t=ge(n.shape[e]);if(!t.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const a of t){if(r.has(a))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(a)}`);r.set(a,n)}}return new he({typeName:Fe.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...T(n)})}}function be(e,t){const n=s(e),a=s(t);if(e===t)return{valid:!0,data:e};if(n===l.object&&a===l.object){const n=r.objectKeys(t),a=r.objectKeys(e).filter(e=>-1!==n.indexOf(e)),o={...e,...t};for(const n of a){const r=be(e[n],t[n]);if(!r.valid)return{valid:!1};o[n]=r.data}return{valid:!0,data:o}}if(n===l.array&&a===l.array){if(e.length!==t.length)return{valid:!1};const n=[];for(let r=0;r<e.length;r++){const a=be(e[r],t[r]);if(!a.valid)return{valid:!1};n.push(a.data)}return{valid:!0,data:n}}return n===l.date&&a===l.date&&+e===+t?{valid:!0,data:e}:{valid:!1}}class ye extends P{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(e,r)=>{if(w(e)||w(r))return y;const a=be(e.value,r.value);return a.valid?((k(e)||k(r))&&t.dirty(),{status:t.value,value:a.data}):(h(n,{code:c.invalid_intersection_types}),y)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([e,t])=>r(e,t)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}ye.create=(e,t,n)=>new ye({left:e,right:t,typeName:Fe.ZodIntersection,...T(n)});class ve extends P{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==l.array)return h(n,{code:c.invalid_type,expected:l.array,received:n.parsedType}),y;if(n.data.length<this._def.items.length)return h(n,{code:c.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),y;!this._def.rest&&n.data.length>this._def.items.length&&(h(n,{code:c.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((e,t)=>{const r=this._def.items[t]||this._def.rest;return r?r._parse(new C(n,e,n.path,t)):null}).filter(e=>!!e);return n.common.async?Promise.all(r).then(e=>b.mergeArray(t,e)):b.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new ve({...this._def,rest:e})}}ve.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new ve({items:e,typeName:Fe.ZodTuple,rest:null,...T(t)})};class _e extends P{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==l.map)return h(n,{code:c.invalid_type,expected:l.map,received:n.parsedType}),y;const r=this._def.keyType,a=this._def.valueType,o=[...n.data.entries()].map(([e,t],o)=>({key:r._parse(new C(n,e,n.path,[o,"key"])),value:a._parse(new C(n,t,n.path,[o,"value"]))}));if(n.common.async){const e=new Map;return Promise.resolve().then(async()=>{for(const n of o){const r=await n.key,a=await n.value;if("aborted"===r.status||"aborted"===a.status)return y;"dirty"!==r.status&&"dirty"!==a.status||t.dirty(),e.set(r.value,a.value)}return{status:t.value,value:e}})}{const e=new Map;for(const n of o){const r=n.key,a=n.value;if("aborted"===r.status||"aborted"===a.status)return y;"dirty"!==r.status&&"dirty"!==a.status||t.dirty(),e.set(r.value,a.value)}return{status:t.value,value:e}}}}_e.create=(e,t,n)=>new _e({valueType:t,keyType:e,typeName:Fe.ZodMap,...T(n)});class we extends P{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==l.set)return h(n,{code:c.invalid_type,expected:l.set,received:n.parsedType}),y;const r=this._def;null!==r.minSize&&n.data.size<r.minSize.value&&(h(n,{code:c.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),null!==r.maxSize&&n.data.size>r.maxSize.value&&(h(n,{code:c.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function o(e){const n=new Set;for(const r of e){if("aborted"===r.status)return y;"dirty"===r.status&&t.dirty(),n.add(r.value)}return{status:t.value,value:n}}const i=[...n.data.values()].map((e,t)=>a._parse(new C(n,e,n.path,t)));return n.common.async?Promise.all(i).then(e=>o(e)):o(i)}min(e,t){return new we({...this._def,minSize:{value:e,message:f.toString(t)}})}max(e,t){return new we({...this._def,maxSize:{value:e,message:f.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}we.create=(e,t)=>new we({valueType:e,minSize:null,maxSize:null,typeName:Fe.ZodSet,...T(t)});class ke extends P{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==l.function)return h(t,{code:c.invalid_type,expected:l.function,received:t.parsedType}),y;function n(e,n){return g({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,m(),d].filter(e=>!!e),issueData:{code:c.invalid_arguments,argumentsError:n}})}function r(e,n){return g({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,m(),d].filter(e=>!!e),issueData:{code:c.invalid_return_type,returnTypeError:n}})}const a={errorMap:t.common.contextualErrorMap},o=t.data;if(this._def.returns instanceof Pe){const e=this;return _(async function(...t){const i=new u([]),l=await e._def.args.parseAsync(t,a).catch(e=>{throw i.addIssue(n(t,e)),i}),s=await Reflect.apply(o,this,l);return await e._def.returns._def.type.parseAsync(s,a).catch(e=>{throw i.addIssue(r(s,e)),i})})}{const e=this;return _(function(...t){const i=e._def.args.safeParse(t,a);if(!i.success)throw new u([n(t,i.error)]);const l=Reflect.apply(o,this,i.data),s=e._def.returns.safeParse(l,a);if(!s.success)throw new u([r(l,s.error)]);return s.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ke({...this._def,args:ve.create(e).rest(se.create())})}returns(e){return new ke({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new ke({args:e||ve.create([]).rest(se.create()),returns:t||se.create(),typeName:Fe.ZodFunction,...T(n)})}}class xe extends P{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}xe.create=(e,t)=>new xe({getter:e,typeName:Fe.ZodLazy,...T(t)});class Se extends P{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return h(t,{received:t.data,code:c.invalid_literal,expected:this._def.value}),y}return{status:"valid",value:e.data}}get value(){return this._def.value}}function Ce(e,t){return new Ee({values:e,typeName:Fe.ZodEnum,...T(t)})}Se.create=(e,t)=>new Se({value:e,typeName:Fe.ZodLiteral,...T(t)});class Ee extends P{_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),n=this._def.values;return h(t,{expected:r.joinValues(n),received:t.parsedType,code:c.invalid_type}),y}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return h(t,{received:t.data,code:c.invalid_enum_value,options:n}),y}return _(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Ee.create(e,{...this._def,...t})}exclude(e,t=this._def){return Ee.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}Ee.create=Ce;class Te extends P{_parse(e){const t=r.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==l.string&&n.parsedType!==l.number){const e=r.objectValues(t);return h(n,{expected:r.joinValues(e),received:n.parsedType,code:c.invalid_type}),y}if(this._cache||(this._cache=new Set(r.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const e=r.objectValues(t);return h(n,{received:n.data,code:c.invalid_enum_value,options:e}),y}return _(e.data)}get enum(){return this._def.values}}Te.create=(e,t)=>new Te({values:e,typeName:Fe.ZodNativeEnum,...T(t)});class Pe extends P{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==l.promise&&!1===t.common.async)return h(t,{code:c.invalid_type,expected:l.promise,received:t.parsedType}),y;const n=t.parsedType===l.promise?t.data:Promise.resolve(t.data);return _(n.then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}Pe.create=(e,t)=>new Pe({type:e,typeName:Fe.ZodPromise,...T(t)});class Me extends P{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Fe.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),a=this._def.effect||null,o={addIssue:e=>{h(n,e),e.fatal?t.abort():t.dirty()},get path(){return n.path}};if(o.addIssue=o.addIssue.bind(o),"preprocess"===a.type){const e=a.transform(n.data,o);if(n.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return y;const r=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return"aborted"===r.status?y:"dirty"===r.status||"dirty"===t.value?v(r.value):r});{if("aborted"===t.value)return y;const r=this._def.schema._parseSync({data:e,path:n.path,parent:n});return"aborted"===r.status?y:"dirty"===r.status||"dirty"===t.value?v(r.value):r}}if("refinement"===a.type){const e=e=>{const t=a.refinement(e,o);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===n.common.async){const r=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===r.status?y:("dirty"===r.status&&t.dirty(),e(r.value),{status:t.value,value:r.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(n=>"aborted"===n.status?y:("dirty"===n.status&&t.dirty(),e(n.value).then(()=>({status:t.value,value:n.value}))))}if("transform"===a.type){if(!1===n.common.async){const e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!x(e))return y;const r=a.transform(e.value,o);if(r instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:r}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(e=>x(e)?Promise.resolve(a.transform(e.value,o)).then(e=>({status:t.value,value:e})):y)}r.assertNever(a)}}Me.create=(e,t,n)=>new Me({schema:e,typeName:Fe.ZodEffects,effect:t,...T(n)}),Me.createWithPreprocess=(e,t,n)=>new Me({schema:t,effect:{type:"preprocess",transform:e},typeName:Fe.ZodEffects,...T(n)});class Ie extends P{_parse(e){return this._getType(e)===l.undefined?_(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ie.create=(e,t)=>new Ie({innerType:e,typeName:Fe.ZodOptional,...T(t)});class Ne extends P{_parse(e){return this._getType(e)===l.null?_(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ne.create=(e,t)=>new Ne({innerType:e,typeName:Fe.ZodNullable,...T(t)});class De extends P{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===l.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}De.create=(e,t)=>new De({innerType:e,typeName:Fe.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...T(t)});class Le extends P{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return S(r)?r.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new u(n.common.issues)},input:n.data})})):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new u(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Le.create=(e,t)=>new Le({innerType:e,typeName:Fe.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...T(t)});class ze extends P{_parse(e){if(this._getType(e)!==l.nan){const t=this._getOrReturnCtx(e);return h(t,{code:c.invalid_type,expected:l.nan,received:t.parsedType}),y}return{status:"valid",value:e.data}}}ze.create=e=>new ze({typeName:Fe.ZodNaN,...T(e)});Symbol("zod_brand");class Oe extends P{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class Ae extends P{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?y:"dirty"===e.status?(t.dirty(),v(e.value)):this._def.out._parseAsync({data:e.value,path:n.path,parent:n})})()}{const e=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?y:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:n.path,parent:n})}}static create(e,t){return new Ae({in:e,out:t,typeName:Fe.ZodPipeline})}}class Re extends P{_parse(e){const t=this._def.innerType._parse(e),n=e=>(x(e)&&(e.value=Object.freeze(e.value)),e);return S(t)?t.then(e=>n(e)):n(t)}unwrap(){return this._def.innerType}}Re.create=(e,t)=>new Re({innerType:e,typeName:Fe.ZodReadonly,...T(t)});me.lazycreate;var Fe;!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(Fe||(Fe={}));const Be=X.create,He=ee.create,Ue=(ze.create,te.create,ne.create),je=re.create,Ze=(ae.create,oe.create,ie.create,le.create),$e=se.create,Ve=(ce.create,ue.create),We=de.create,Ke=me.create,Ge=(me.strictCreate,fe.create,he.create),Ye=(ye.create,ve.create,_e.create,we.create,ke.create),Qe=(xe.create,Se.create),qe=Ee.create,Xe=Te.create;Pe.create,Me.create,Ie.create,Ne.create,Me.createWithPreprocess,Ae.create;function Je(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=Je(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function et(){for(var e,t,n=0,r="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=Je(e))&&(r&&(r+=" "),r+=t);return r}const tt=e=>{const t=ot(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{const n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),nt(n,t)||at(e)},getConflictingClassGroupIds:(e,t)=>{const a=n[e]||[];return t&&r[e]?[...a,...r[e]]:a}}},nt=(e,t)=>{if(0===e.length)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),a=r?nt(e.slice(1),r):void 0;if(a)return a;if(0===t.validators.length)return;const o=e.join("-");return t.validators.find(({validator:e})=>e(o))?.classGroupId},rt=/^\[(.+)\]$/,at=e=>{if(rt.test(e)){const t=rt.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},ot=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const e in n)it(n[e],r,e,t);return r},it=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){return void((""===e?t:lt(t,e)).classGroupId=n)}if("function"==typeof e)return st(e)?void it(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,a])=>{it(a,lt(t,e),n,r)})})},lt=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},st=e=>e.isThemeGetter,ct=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const a=(a,o)=>{n.set(a,o),t++,t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(a(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):a(e,t)}}},ut=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=e=>{const t=[];let n,r=0,a=0,o=0;for(let i=0;i<e.length;i++){let l=e[i];if(0===r&&0===a){if(":"===l){t.push(e.slice(o,i)),o=i+1;continue}if("/"===l){n=i;continue}}"["===l?r++:"]"===l?r--:"("===l?a++:")"===l&&a--}const i=0===t.length?e:e.substring(o),l=dt(i);return{modifiers:t,hasImportantModifier:l!==i,baseClassName:l,maybePostfixModifierPosition:n&&n>o?n-o:void 0}};if(t){const e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){const e=r;r=t=>n({className:t,parseClassName:e})}return r},dt=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,pt=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;const n=[];let r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},mt=/\s+/;function ft(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=gt(e))&&(r&&(r+=" "),r+=t);return r}const gt=e=>{if("string"==typeof e)return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=gt(e[r]))&&(n&&(n+=" "),n+=t);return n};function ht(e,...t){let n,r,a,o=function(l){const s=t.reduce((e,t)=>t(e),e());return n=(e=>({cache:ct(e.cacheSize),parseClassName:ut(e),sortModifiers:pt(e),...tt(e)}))(s),r=n.cache.get,a=n.cache.set,o=i,i(l)};function i(e){const t=r(e);if(t)return t;const o=((e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:a,sortModifiers:o}=t,i=[],l=e.trim().split(mt);let s="";for(let e=l.length-1;e>=0;e-=1){const t=l[e],{isExternal:c,modifiers:u,hasImportantModifier:d,baseClassName:p,maybePostfixModifierPosition:m}=n(t);if(c){s=t+(s.length>0?" "+s:s);continue}let f=!!m,g=r(f?p.substring(0,m):p);if(!g){if(!f){s=t+(s.length>0?" "+s:s);continue}if(g=r(p),!g){s=t+(s.length>0?" "+s:s);continue}f=!1}const h=o(u).join(":"),b=d?h+"!":h,y=b+g;if(i.includes(y))continue;i.push(y);const v=a(g,f);for(let e=0;e<v.length;++e){const t=v[e];i.push(b+t)}s=t+(s.length>0?" "+s:s)}return s})(e,n);return a(e,o),o}return function(){return o(ft.apply(null,arguments))}}const bt=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},yt=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,vt=/^\((?:(\w[\w-]*):)?(.+)\)$/i,_t=/^\d+\/\d+$/,wt=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,kt=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,xt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,St=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Ct=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Et=e=>_t.test(e),Tt=e=>!!e&&!Number.isNaN(Number(e)),Pt=e=>!!e&&Number.isInteger(Number(e)),Mt=e=>e.endsWith("%")&&Tt(e.slice(0,-1)),It=e=>wt.test(e),Nt=()=>!0,Dt=e=>kt.test(e)&&!xt.test(e),Lt=()=>!1,zt=e=>St.test(e),Ot=e=>Ct.test(e),At=e=>!Ft(e)&&!$t(e),Rt=e=>qt(e,tn,Lt),Ft=e=>yt.test(e),Bt=e=>qt(e,nn,Dt),Ht=e=>qt(e,rn,Tt),Ut=e=>qt(e,Jt,Lt),jt=e=>qt(e,en,Ot),Zt=e=>qt(e,on,zt),$t=e=>vt.test(e),Vt=e=>Xt(e,nn),Wt=e=>Xt(e,an),Kt=e=>Xt(e,Jt),Gt=e=>Xt(e,tn),Yt=e=>Xt(e,en),Qt=e=>Xt(e,on,!0),qt=(e,t,n)=>{const r=yt.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},Xt=(e,t,n=!1)=>{const r=vt.exec(e);return!!r&&(r[1]?t(r[1]):n)},Jt=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,tn=e=>"length"===e||"size"===e||"bg-size"===e,nn=e=>"length"===e,rn=e=>"number"===e,an=e=>"family-name"===e,on=e=>"shadow"===e,ln=(Symbol.toStringTag,()=>{const e=bt("color"),t=bt("font"),n=bt("text"),r=bt("font-weight"),a=bt("tracking"),o=bt("leading"),i=bt("breakpoint"),l=bt("container"),s=bt("spacing"),c=bt("radius"),u=bt("shadow"),d=bt("inset-shadow"),p=bt("text-shadow"),m=bt("drop-shadow"),f=bt("blur"),g=bt("perspective"),h=bt("aspect"),b=bt("ease"),y=bt("animate"),v=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",$t,Ft],_=()=>[$t,Ft,s],w=()=>[Et,"full","auto",..._()],k=()=>[Pt,"none","subgrid",$t,Ft],x=()=>["auto",{span:["full",Pt,$t,Ft]},Pt,$t,Ft],S=()=>[Pt,"auto",$t,Ft],C=()=>["auto","min","max","fr",$t,Ft],E=()=>["auto",..._()],T=()=>[Et,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",..._()],P=()=>[e,$t,Ft],M=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",Kt,Ut,{position:[$t,Ft]}],I=()=>["auto","cover","contain",Gt,Rt,{size:[$t,Ft]}],N=()=>[Mt,Vt,Bt],D=()=>["","none","full",c,$t,Ft],L=()=>["",Tt,Vt,Bt],z=()=>[Tt,Mt,Kt,Ut],O=()=>["","none",f,$t,Ft],A=()=>["none",Tt,$t,Ft],R=()=>["none",Tt,$t,Ft],F=()=>[Tt,$t,Ft],B=()=>[Et,"full",..._()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[It],breakpoint:[It],color:[Nt],container:[It],"drop-shadow":[It],ease:["in","out","in-out"],font:[At],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[It],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[It],shadow:[It],spacing:["px",Tt],text:[It],"text-shadow":[It],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Et,Ft,$t,h]}],container:["container"],columns:[{columns:[Tt,Ft,$t,l]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:w()}],"inset-x":[{"inset-x":w()}],"inset-y":[{"inset-y":w()}],start:[{start:w()}],end:[{end:w()}],top:[{top:w()}],right:[{right:w()}],bottom:[{bottom:w()}],left:[{left:w()}],visibility:["visible","invisible","collapse"],z:[{z:[Pt,"auto",$t,Ft]}],basis:[{basis:[Et,"full","auto",l,..._()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Tt,Et,"auto","initial","none",Ft]}],grow:[{grow:["",Tt,$t,Ft]}],shrink:[{shrink:["",Tt,$t,Ft]}],order:[{order:[Pt,"first","last","none",$t,Ft]}],"grid-cols":[{"grid-cols":k()}],"col-start-end":[{col:x()}],"col-start":[{"col-start":S()}],"col-end":[{"col-end":S()}],"grid-rows":[{"grid-rows":k()}],"row-start-end":[{row:x()}],"row-start":[{"row-start":S()}],"row-end":[{"row-end":S()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":C()}],"auto-rows":[{"auto-rows":C()}],gap:[{gap:_()}],"gap-x":[{"gap-x":_()}],"gap-y":[{"gap-y":_()}],"justify-content":[{justify:["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe","normal"]}],"justify-items":[{"justify-items":["start","end","center","stretch","center-safe","end-safe","normal"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"align-items":[{items:["start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"align-self":[{self:["auto","start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"place-items":[{"place-items":["start","end","center","stretch","center-safe","end-safe","baseline"]}],"place-self":[{"place-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],p:[{p:_()}],px:[{px:_()}],py:[{py:_()}],ps:[{ps:_()}],pe:[{pe:_()}],pt:[{pt:_()}],pr:[{pr:_()}],pb:[{pb:_()}],pl:[{pl:_()}],m:[{m:E()}],mx:[{mx:E()}],my:[{my:E()}],ms:[{ms:E()}],me:[{me:E()}],mt:[{mt:E()}],mr:[{mr:E()}],mb:[{mb:E()}],ml:[{ml:E()}],"space-x":[{"space-x":_()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":_()}],"space-y-reverse":["space-y-reverse"],size:[{size:T()}],w:[{w:[l,"screen",...T()]}],"min-w":[{"min-w":[l,"screen","none",...T()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[i]},...T()]}],h:[{h:["screen","lh",...T()]}],"min-h":[{"min-h":["screen","lh","none",...T()]}],"max-h":[{"max-h":["screen","lh",...T()]}],"font-size":[{text:["base",n,Vt,Bt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,$t,Ht]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Mt,Ft]}],"font-family":[{font:[Wt,Ft,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,$t,Ft]}],"line-clamp":[{"line-clamp":[Tt,"none",$t,Ht]}],leading:[{leading:[o,..._()]}],"list-image":[{"list-image":["none",$t,Ft]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",$t,Ft]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:P()}],"text-color":[{text:P()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","wavy"]}],"text-decoration-thickness":[{decoration:[Tt,"from-font","auto",$t,Bt]}],"text-decoration-color":[{decoration:P()}],"underline-offset":[{"underline-offset":[Tt,"auto",$t,Ft]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",$t,Ft]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",$t,Ft]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:M()}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:I()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Pt,$t,Ft],radial:["",$t,Ft],conic:[Pt,$t,Ft]},Yt,jt]}],"bg-color":[{bg:P()}],"gradient-from-pos":[{from:N()}],"gradient-via-pos":[{via:N()}],"gradient-to-pos":[{to:N()}],"gradient-from":[{from:P()}],"gradient-via":[{via:P()}],"gradient-to":[{to:P()}],rounded:[{rounded:D()}],"rounded-s":[{"rounded-s":D()}],"rounded-e":[{"rounded-e":D()}],"rounded-t":[{"rounded-t":D()}],"rounded-r":[{"rounded-r":D()}],"rounded-b":[{"rounded-b":D()}],"rounded-l":[{"rounded-l":D()}],"rounded-ss":[{"rounded-ss":D()}],"rounded-se":[{"rounded-se":D()}],"rounded-ee":[{"rounded-ee":D()}],"rounded-es":[{"rounded-es":D()}],"rounded-tl":[{"rounded-tl":D()}],"rounded-tr":[{"rounded-tr":D()}],"rounded-br":[{"rounded-br":D()}],"rounded-bl":[{"rounded-bl":D()}],"border-w":[{border:L()}],"border-w-x":[{"border-x":L()}],"border-w-y":[{"border-y":L()}],"border-w-s":[{"border-s":L()}],"border-w-e":[{"border-e":L()}],"border-w-t":[{"border-t":L()}],"border-w-r":[{"border-r":L()}],"border-w-b":[{"border-b":L()}],"border-w-l":[{"border-l":L()}],"divide-x":[{"divide-x":L()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":L()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:["solid","dashed","dotted","double","hidden","none"]}],"divide-style":[{divide:["solid","dashed","dotted","double","hidden","none"]}],"border-color":[{border:P()}],"border-color-x":[{"border-x":P()}],"border-color-y":[{"border-y":P()}],"border-color-s":[{"border-s":P()}],"border-color-e":[{"border-e":P()}],"border-color-t":[{"border-t":P()}],"border-color-r":[{"border-r":P()}],"border-color-b":[{"border-b":P()}],"border-color-l":[{"border-l":P()}],"divide-color":[{divide:P()}],"outline-style":[{outline:["solid","dashed","dotted","double","none","hidden"]}],"outline-offset":[{"outline-offset":[Tt,$t,Ft]}],"outline-w":[{outline:["",Tt,Vt,Bt]}],"outline-color":[{outline:P()}],shadow:[{shadow:["","none",u,Qt,Zt]}],"shadow-color":[{shadow:P()}],"inset-shadow":[{"inset-shadow":["none",d,Qt,Zt]}],"inset-shadow-color":[{"inset-shadow":P()}],"ring-w":[{ring:L()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:P()}],"ring-offset-w":[{"ring-offset":[Tt,Bt]}],"ring-offset-color":[{"ring-offset":P()}],"inset-ring-w":[{"inset-ring":L()}],"inset-ring-color":[{"inset-ring":P()}],"text-shadow":[{"text-shadow":["none",p,Qt,Zt]}],"text-shadow-color":[{"text-shadow":P()}],opacity:[{opacity:[Tt,$t,Ft]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[Tt]}],"mask-image-linear-from-pos":[{"mask-linear-from":z()}],"mask-image-linear-to-pos":[{"mask-linear-to":z()}],"mask-image-linear-from-color":[{"mask-linear-from":P()}],"mask-image-linear-to-color":[{"mask-linear-to":P()}],"mask-image-t-from-pos":[{"mask-t-from":z()}],"mask-image-t-to-pos":[{"mask-t-to":z()}],"mask-image-t-from-color":[{"mask-t-from":P()}],"mask-image-t-to-color":[{"mask-t-to":P()}],"mask-image-r-from-pos":[{"mask-r-from":z()}],"mask-image-r-to-pos":[{"mask-r-to":z()}],"mask-image-r-from-color":[{"mask-r-from":P()}],"mask-image-r-to-color":[{"mask-r-to":P()}],"mask-image-b-from-pos":[{"mask-b-from":z()}],"mask-image-b-to-pos":[{"mask-b-to":z()}],"mask-image-b-from-color":[{"mask-b-from":P()}],"mask-image-b-to-color":[{"mask-b-to":P()}],"mask-image-l-from-pos":[{"mask-l-from":z()}],"mask-image-l-to-pos":[{"mask-l-to":z()}],"mask-image-l-from-color":[{"mask-l-from":P()}],"mask-image-l-to-color":[{"mask-l-to":P()}],"mask-image-x-from-pos":[{"mask-x-from":z()}],"mask-image-x-to-pos":[{"mask-x-to":z()}],"mask-image-x-from-color":[{"mask-x-from":P()}],"mask-image-x-to-color":[{"mask-x-to":P()}],"mask-image-y-from-pos":[{"mask-y-from":z()}],"mask-image-y-to-pos":[{"mask-y-to":z()}],"mask-image-y-from-color":[{"mask-y-from":P()}],"mask-image-y-to-color":[{"mask-y-to":P()}],"mask-image-radial":[{"mask-radial":[$t,Ft]}],"mask-image-radial-from-pos":[{"mask-radial-from":z()}],"mask-image-radial-to-pos":[{"mask-radial-to":z()}],"mask-image-radial-from-color":[{"mask-radial-from":P()}],"mask-image-radial-to-color":[{"mask-radial-to":P()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"]}],"mask-image-conic-pos":[{"mask-conic":[Tt]}],"mask-image-conic-from-pos":[{"mask-conic-from":z()}],"mask-image-conic-to-pos":[{"mask-conic-to":z()}],"mask-image-conic-from-color":[{"mask-conic-from":P()}],"mask-image-conic-to-color":[{"mask-conic-to":P()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:M()}],"mask-repeat":[{mask:["no-repeat",{repeat:["","x","y","space","round"]}]}],"mask-size":[{mask:I()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",$t,Ft]}],filter:[{filter:["","none",$t,Ft]}],blur:[{blur:O()}],brightness:[{brightness:[Tt,$t,Ft]}],contrast:[{contrast:[Tt,$t,Ft]}],"drop-shadow":[{"drop-shadow":["","none",m,Qt,Zt]}],"drop-shadow-color":[{"drop-shadow":P()}],grayscale:[{grayscale:["",Tt,$t,Ft]}],"hue-rotate":[{"hue-rotate":[Tt,$t,Ft]}],invert:[{invert:["",Tt,$t,Ft]}],saturate:[{saturate:[Tt,$t,Ft]}],sepia:[{sepia:["",Tt,$t,Ft]}],"backdrop-filter":[{"backdrop-filter":["","none",$t,Ft]}],"backdrop-blur":[{"backdrop-blur":O()}],"backdrop-brightness":[{"backdrop-brightness":[Tt,$t,Ft]}],"backdrop-contrast":[{"backdrop-contrast":[Tt,$t,Ft]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Tt,$t,Ft]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Tt,$t,Ft]}],"backdrop-invert":[{"backdrop-invert":["",Tt,$t,Ft]}],"backdrop-opacity":[{"backdrop-opacity":[Tt,$t,Ft]}],"backdrop-saturate":[{"backdrop-saturate":[Tt,$t,Ft]}],"backdrop-sepia":[{"backdrop-sepia":["",Tt,$t,Ft]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":_()}],"border-spacing-x":[{"border-spacing-x":_()}],"border-spacing-y":[{"border-spacing-y":_()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",$t,Ft]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Tt,"initial",$t,Ft]}],ease:[{ease:["linear","initial",b,$t,Ft]}],delay:[{delay:[Tt,$t,Ft]}],animate:[{animate:["none",y,$t,Ft]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,$t,Ft]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:A()}],"rotate-x":[{"rotate-x":A()}],"rotate-y":[{"rotate-y":A()}],"rotate-z":[{"rotate-z":A()}],scale:[{scale:R()}],"scale-x":[{"scale-x":R()}],"scale-y":[{"scale-y":R()}],"scale-z":[{"scale-z":R()}],"scale-3d":["scale-3d"],skew:[{skew:F()}],"skew-x":[{"skew-x":F()}],"skew-y":[{"skew-y":F()}],transform:[{transform:[$t,Ft,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:B()}],"translate-x":[{"translate-x":B()}],"translate-y":[{"translate-y":B()}],"translate-z":[{"translate-z":B()}],"translate-none":["translate-none"],accent:[{accent:P()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:P()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",$t,Ft]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",$t,Ft]}],fill:[{fill:["none",...P()]}],"stroke-w":[{stroke:[Tt,Vt,Bt,Ht]}],stroke:[{stroke:["none",...P()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}}),sn=ht(ln);function cn(...e){return sn(et(e))}var un=n(5072),dn=n.n(un),pn=n(7825),mn=n.n(pn),fn=n(7659),gn=n.n(fn),hn=n(5056),bn=n.n(hn),yn=n(540),vn=n.n(yn),_n=n(1113),wn=n.n(_n),kn=n(9182),xn={};xn.styleTagTransform=wn(),xn.setAttributes=bn(),xn.insert=gn().bind(null,"head"),xn.domAPI=mn(),xn.insertStyleElement=vn();dn()(kn.A,xn);const Sn=kn.A&&kn.A.locals?kn.A.locals:void 0;var Cn=n(7170),En={};En.styleTagTransform=wn(),En.setAttributes=bn(),En.insert=gn().bind(null,"head"),En.domAPI=mn(),En.insertStyleElement=vn();dn()(Cn.A,En);const Tn=Cn.A&&Cn.A.locals?Cn.A.locals:void 0;function Pn(){return Pn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pn.apply(this,arguments)}const Mn=["children","options"],In=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce((e,t)=>(e[t.toLowerCase()]=t,e),{class:"className",for:"htmlFor"}),Nn={amp:"&",apos:"'",gt:">",lt:"<",nbsp:" ",quot:"“"},Dn=["style","script","pre"],Ln=["src","href","data","formAction","srcDoc","action"],zn=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,On=/mailto:/i,An=/\n{2,}$/,Rn=/^(\s*>[\s\S]*?)(?=\n\n|$)/,Fn=/^ *> ?/gm,Bn=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,Hn=/^ {2,}\n/,Un=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,jn=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,Zn=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,$n=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,Vn=/^(?:\n *)*\n/,Wn=/\r\n?/g,Kn=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,Gn=/^\[\^([^\]]+)]/,Yn=/\f/g,Qn=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,qn=/^\s*?\[(x|\s)\]/,Xn=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,Jn=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,er=/^([^\n]+)\n *(=|-){3,} *\n/,tr=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,nr=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,rr=/^<!--[\s\S]*?(?:-->)/,ar=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,or=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,ir=/^\{.*\}$/,lr=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,sr=/^<([^ >]+@[^ >]+)>/,cr=/^<([^ >]+:\/[^ >]+)>/,ur=/-([a-z])?/gi,dr=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,pr=/^[^\n]+(?:  \n|\n{2,})/,mr=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,fr=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,gr=/^\[([^\]]*)\] ?\[([^\]]*)\]/,hr=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,br=/\t/g,yr=/(^ *\||\| *$)/g,vr=/^ *:-+: *$/,_r=/^ *:-+ *$/,wr=/^ *-+: *$/,kr="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",xr=RegExp(`^([*_])\\1${kr}\\1\\1(?!\\1)`),Sr=RegExp(`^([*_])${kr}\\1(?!\\1)`),Cr=RegExp(`^(==)${kr}\\1`),Er=RegExp(`^(~~)${kr}\\1`),Tr=/^(:[a-zA-Z0-9-_]+:)/,Pr=/^\\([^0-9A-Za-z\s])/,Mr=/\\([^0-9A-Za-z\s])/g,Ir=/^[\s\S](?:(?!  \n|[0-9]\.|http)[^=*_~\-\n:<`\\\[!])*/,Nr=/^\n+/,Dr=/^([ \t]*)/,Lr=/(?:^|\n)( *)$/,zr="(?:\\d+\\.)",Or="(?:[*+-])";function Ar(e){return"( *)("+(1===e?zr:Or)+") +"}const Rr=Ar(1),Fr=Ar(2);function Br(e){return RegExp("^"+(1===e?Rr:Fr))}const Hr=Br(1),Ur=Br(2);function jr(e){return RegExp("^"+(1===e?Rr:Fr)+"[^\\n]*(?:\\n(?!\\1"+(1===e?zr:Or)+" )[^\\n]*)*(\\n|$)","gm")}const Zr=jr(1),$r=jr(2);function Vr(e){const t=1===e?zr:Or;return RegExp("^( *)("+t+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+t+" (?!"+t+" ))\\n*|\\s*\\n*$)")}const Wr=Vr(1),Kr=Vr(2);function Gr(e,t){const n=1===t,r=n?Wr:Kr,a=n?Zr:$r,o=n?Hr:Ur;return{t:e=>o.test(e),o:oa(function(e,t){const n=Lr.exec(t.prevCapture);return n&&(t.list||!t.inline&&!t.simple)?r.exec(e=n[1]+e):null}),i:1,u(e,t,r){const i=n?+e[2]:void 0,l=e[0].replace(An,"\n").match(a);let s=!1;return{items:l.map(function(e,n){const a=o.exec(e)[0].length,i=RegExp("^ {1,"+a+"}","gm"),c=e.replace(i,"").replace(o,""),u=n===l.length-1,d=-1!==c.indexOf("\n\n")||u&&s;s=d;const p=r.inline,m=r.list;let f;r.list=!0,d?(r.inline=!1,f=qr(c)+"\n\n"):(r.inline=!0,f=qr(c));const g=t(f,r);return r.inline=p,r.list=m,g}),ordered:n,start:i}},l:(t,n,r)=>e(t.ordered?"ol":"ul",{key:r.key,start:"20"===t.type?t.start:void 0},t.items.map(function(t,a){return e("li",{key:a},n(t,r))}))}}const Yr=RegExp("^\\[((?:\\[[^\\[\\]]*(?:\\[[^\\[\\]]*\\][^\\[\\]]*)*\\]|[^\\[\\]])*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['\"]([\\s\\S]*?)['\"])?\\s*\\)"),Qr=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/;function qr(e){let t=e.length;for(;t>0&&e[t-1]<=" ";)t--;return e.slice(0,t)}function Xr(e,t){return e.startsWith(t)}function Jr(e,t,n){if(Array.isArray(n)){for(let t=0;t<n.length;t++)if(Xr(e,n[t]))return!0;return!1}return n(e,t)}function ea(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}function ta(e){return wr.test(e)?"right":vr.test(e)?"center":_r.test(e)?"left":null}function na(e,t,n,r){const a=n.inTable;n.inTable=!0;let o=[[]],i="";function l(){if(!i)return;const e=o[o.length-1];e.push.apply(e,t(i,n)),i=""}return e.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach((e,t,n)=>{"|"===e.trim()&&(l(),r)?0!==t&&t!==n.length-1&&o.push([]):i+=e}),l(),n.inTable=a,o}function ra(e,t,n){n.inline=!0;const r=e[2]?e[2].replace(yr,"").split("|").map(ta):[],a=e[3]?function(e,t,n){return e.trim().split("\n").map(function(e){return na(e,t,n,!0)})}(e[3],t,n):[],o=na(e[1],t,n,!!a.length);return n.inline=!1,a.length?{align:r,cells:a,header:o,type:"25"}:{children:o,type:"21"}}function aa(e,t){return null==e.align[t]?{}:{textAlign:e.align[t]}}function oa(e){return e.inline=1,e}function ia(e){return oa(function(t,n){return n.inline?e.exec(t):null})}function la(e){return oa(function(t,n){return n.inline||n.simple?e.exec(t):null})}function sa(e){return function(t,n){return n.inline||n.simple?null:e.exec(t)}}function ca(e){return oa(function(t){return e.exec(t)})}const ua=/(javascript|vbscript|data(?!:image)):/i;function da(e){try{const t=decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"");if(ua.test(t))return null}catch(e){return null}return e}function pa(e){return e?e.replace(Mr,"$1"):e}function ma(e,t,n){const r=n.inline||!1,a=n.simple||!1;n.inline=!0,n.simple=!0;const o=e(t,n);return n.inline=r,n.simple=a,o}function fa(e,t,n){const r=n.inline||!1,a=n.simple||!1;n.inline=!1,n.simple=!0;const o=e(t,n);return n.inline=r,n.simple=a,o}function ga(e,t,n){const r=n.inline||!1;n.inline=!1;const a=e(t,n);return n.inline=r,a}const ha=(e,t,n)=>({children:ma(t,e[2],n)});function ba(){return{}}function ya(){return null}function va(...e){return e.filter(Boolean).join(" ")}function _a(e,t,n){let r=e;const a=t.split(".");for(;a.length&&(r=r[a[0]],void 0!==r);)a.shift();return r||n}function wa(e="",t={}){t.overrides=t.overrides||{},t.namedCodesToUnicode=t.namedCodesToUnicode?Pn({},Nn,t.namedCodesToUnicode):Nn;const n=t.slugify||ea,r=t.sanitizer||da,a=t.createElement||o.createElement,i=[Rn,jn,Zn,t.enforceAtxHeadings?Jn:Xn,er,dr,Wr,Kr],l=[...i,pr,tr,rr,or];function s(e,n,...r){const o=_a(t.overrides,e+".props",{});return a(function(e,t){const n=_a(t,e);return n?"function"==typeof n||"object"==typeof n&&"render"in n?n:_a(t,e+".component",e):e}(e,t.overrides),Pn({},n,o,{className:va(null==n?void 0:n.className,o.className)||void 0}),...r)}function c(e){e=e.replace(Qn,"");let n=!1;t.forceInline?n=!0:t.forceBlock||(n=!1===hr.test(e));const r=g(f(n?e:qr(e).replace(Nr,"")+"\n\n",{inline:n}));for(;"string"==typeof r[r.length-1]&&!r[r.length-1].trim();)r.pop();if(null===t.wrapper)return r;const o=t.wrapper||(n?"span":"div");let i;if(r.length>1||t.forceWrapper)i=r;else{if(1===r.length)return i=r[0],"string"==typeof i?s("span",{key:"outer"},i):i;i=null}return a(o,{key:"outer"},i)}function u(e,t){if(!t||!t.trim())return null;const n=t.match(zn);return n?n.reduce(function(t,n){const a=n.indexOf("=");if(-1!==a){const o=function(e){return-1!==e.indexOf("-")&&null===e.match(ar)&&(e=e.replace(ur,function(e,t){return t.toUpperCase()})),e}(n.slice(0,a)).trim(),i=function(e){const t=e[0];return('"'===t||"'"===t)&&e.length>=2&&e[e.length-1]===t?e.slice(1,-1):e}(n.slice(a+1).trim()),l=In[o]||o;if("ref"===l)return t;const s=t[l]=function(e,t,n,r){return"style"===t?function(e){const t=[];let n="",r=!1,a=!1,o="";if(!e)return t;for(let i=0;i<e.length;i++){const l=e[i];if('"'!==l&&"'"!==l||r||(a?l===o&&(a=!1,o=""):(a=!0,o=l)),"("===l&&n.endsWith("url")?r=!0:")"===l&&r&&(r=!1),";"!==l||a||r)n+=l;else{const e=n.trim();if(e){const n=e.indexOf(":");if(n>0){const r=e.slice(0,n).trim(),a=e.slice(n+1).trim();t.push([r,a])}}n=""}}const i=n.trim();if(i){const e=i.indexOf(":");if(e>0){const n=i.slice(0,e).trim(),r=i.slice(e+1).trim();t.push([n,r])}}return t}(n).reduce(function(t,[n,a]){return t[n.replace(/(-[a-z])/g,e=>e[1].toUpperCase())]=r(a,e,n),t},{}):-1!==Ln.indexOf(t)?r(pa(n),e,t):(n.match(ir)&&(n=pa(n.slice(1,n.length-1))),"true"===n||"false"!==n&&n)}(e,o,i,r);"string"==typeof s&&(tr.test(s)||or.test(s))&&(t[l]=c(s.trim()))}else"style"!==n&&(t[In[n]||n]=!0);return t},{}):null}const d=[],p={},m={0:{t:[">"],o:sa(Rn),i:1,u(e,t,n){const[,r,a]=e[0].replace(Fn,"").match(Bn);return{alert:r,children:t(a,n)}},l(e,t,r){const a={key:r.key};return e.alert&&(a.className="markdown-alert-"+n(e.alert.toLowerCase(),ea),e.children.unshift({attrs:{},children:[{type:"27",text:e.alert}],noInnerParse:!0,type:"11",tag:"header"})),s("blockquote",a,t(e.children,r))}},1:{o:ca(Hn),i:1,u:ba,l:(e,t,n)=>s("br",{key:n.key})},2:{t:e=>{const t=e[0];return"-"===t||"*"===t||"_"===t},o:sa(Un),i:1,u:ba,l:(e,t,n)=>s("hr",{key:n.key})},3:{t:["    "],o:sa(Zn),i:0,u:e=>({lang:void 0,text:pa(qr(e[0].replace(/^ {4}/gm,"")))}),l:(e,t,n)=>s("pre",{key:n.key},s("code",Pn({},e.attrs,{className:e.lang?"lang-"+e.lang:""}),e.text))},4:{t:["```","~~~"],o:sa(jn),i:0,u:e=>({attrs:u("code",e[3]||""),lang:e[2]||void 0,text:e[4],type:"3"})},5:{t:["`"],o:la($n),i:3,u:e=>({text:pa(e[2])}),l:(e,t,n)=>s("code",{key:n.key},e.text)},6:{t:["[^"],o:sa(Kn),i:0,u:e=>(d.push({footnote:e[2],identifier:e[1]}),{}),l:ya},7:{t:["[^"],o:ia(Gn),i:1,u:e=>({target:"#"+n(e[1],ea),text:e[1]}),l:(e,t,n)=>s("a",{key:n.key,href:r(e.target,"a","href")},s("sup",{key:n.key},e.text))},8:{t:["[ ]","[x]"],o:ia(qn),i:1,u:e=>({completed:"x"===e[1].toLowerCase()}),l:(e,t,n)=>s("input",{checked:e.completed,key:n.key,readOnly:!0,type:"checkbox"})},9:{t:["#"],o:sa(t.enforceAtxHeadings?Jn:Xn),i:1,u:(e,t,r)=>({children:ma(t,e[2],r),id:n(e[2],ea),level:e[1].length}),l:(e,t,n)=>s("h"+e.level,{id:e.id,key:n.key},t(e.children,n))},10:{o:sa(er),i:0,u:(e,t,n)=>({children:ma(t,e[1],n),level:"="===e[2]?1:2,type:"9"})},11:{t:["<"],o:ca(tr),i:1,u(e,t,n){const[,r]=e[3].match(Dr),a=RegExp("^"+r,"gm"),o=e[3].replace(a,""),i=(s=o,l.some(e=>e.test(s))?ga:ma);var s;const c=e[1].toLowerCase(),d=-1!==Dn.indexOf(c),p=(d?c:e[1]).trim(),m={attrs:u(p,e[2]),noInnerParse:d,tag:p};if(n.inAnchor=n.inAnchor||"a"===c,d)m.text=e[3];else{const e=n.inHTML;n.inHTML=!0,m.children=i(t,o,n),n.inHTML=e}return n.inAnchor=!1,m},l:(e,t,n)=>s(e.tag,Pn({key:n.key},e.attrs),e.text||(e.children?t(e.children,n):""))},13:{t:["<"],o:ca(or),i:1,u(e){const t=e[1].trim();return{attrs:u(t,e[2]||""),tag:t}},l:(e,t,n)=>s(e.tag,Pn({},e.attrs,{key:n.key}))},12:{t:["\x3c!--"],o:ca(rr),i:1,u:()=>({}),l:ya},14:{t:["!["],o:la(Qr),i:1,u:e=>({alt:pa(e[1]),target:pa(e[2]),title:pa(e[3])}),l:(e,t,n)=>s("img",{key:n.key,alt:e.alt||void 0,title:e.title||void 0,src:r(e.target,"img","src")})},15:{t:["["],o:ia(Yr),i:3,u:(e,t,n)=>({children:fa(t,e[1],n),target:pa(e[2]),title:pa(e[3])}),l:(e,t,n)=>s("a",{key:n.key,href:r(e.target,"a","href"),title:e.title},t(e.children,n))},16:{t:["<"],o:ia(cr),i:0,u:e=>({children:[{text:e[1],type:"27"}],target:e[1],type:"15"})},17:{t:(e,n)=>!n.inAnchor&&!t.disableAutoLink&&(Xr(e,"http://")||Xr(e,"https://")),o:ia(lr),i:0,u:e=>({children:[{text:e[1],type:"27"}],target:e[1],title:void 0,type:"15"})},18:{t:["<"],o:ia(sr),i:0,u(e){let t=e[1],n=e[1];return On.test(n)||(n="mailto:"+n),{children:[{text:t.replace("mailto:",""),type:"27"}],target:n,type:"15"}}},20:Gr(s,1),33:Gr(s,2),19:{o:sa(Vn),i:3,u:ba,l:()=>"\n"},21:{o:oa(function(e,t){if(t.inline||t.simple||t.inHTML&&-1===e.indexOf("\n\n")&&-1===t.prevCapture.indexOf("\n\n"))return null;let n="";e.split("\n").every(e=>(e+="\n",!i.some(t=>t.test(e))&&(n+=e,!!e.trim())));const r=qr(n);return""===r?null:[n,,r]}),i:3,u:ha,l:(e,t,n)=>s("p",{key:n.key},t(e.children,n))},22:{t:["["],o:ia(mr),i:0,u:e=>(p[e[1]]={target:e[2],title:e[4]},{}),l:ya},23:{t:["!["],o:la(fr),i:0,u:e=>({alt:e[1]?pa(e[1]):void 0,ref:e[2]}),l:(e,t,n)=>p[e.ref]?s("img",{key:n.key,alt:e.alt,src:r(p[e.ref].target,"img","src"),title:p[e.ref].title}):null},24:{t:e=>"["===e[0]&&-1===e.indexOf("]("),o:ia(gr),i:0,u:(e,t,n)=>({children:t(e[1],n),fallbackChildren:e[0],ref:e[2]}),l:(e,t,n)=>p[e.ref]?s("a",{key:n.key,href:r(p[e.ref].target,"a","href"),title:p[e.ref].title},t(e.children,n)):s("span",{key:n.key},e.fallbackChildren)},25:{t:["|"],o:sa(dr),i:1,u:ra,l(e,t,n){const r=e;return s("table",{key:n.key},s("thead",null,s("tr",null,r.header.map(function(e,a){return s("th",{key:a,style:aa(r,a)},t(e,n))}))),s("tbody",null,r.cells.map(function(e,a){return s("tr",{key:a},e.map(function(e,a){return s("td",{key:a,style:aa(r,a)},t(e,n))}))})))}},27:{o:oa(function(e,t){let n;return Xr(e,":")&&(n=Tr.exec(e)),n||Ir.exec(e)}),i:4,u(e){const n=e[0];return{text:-1===n.indexOf("&")?n:n.replace(nr,(e,n)=>t.namedCodesToUnicode[n]||e)}},l:e=>e.text},28:{t:["**","__"],o:la(xr),i:2,u:(e,t,n)=>({children:t(e[2],n)}),l:(e,t,n)=>s("strong",{key:n.key},t(e.children,n))},29:{t:e=>{const t=e[0];return("*"===t||"_"===t)&&e[1]!==t},o:la(Sr),i:3,u:(e,t,n)=>({children:t(e[2],n)}),l:(e,t,n)=>s("em",{key:n.key},t(e.children,n))},30:{t:["\\"],o:la(Pr),i:1,u:e=>({text:e[1],type:"27"})},31:{t:["=="],o:la(Cr),i:3,u:ha,l:(e,t,n)=>s("mark",{key:n.key},t(e.children,n))},32:{t:["~~"],o:la(Er),i:3,u:ha,l:(e,t,n)=>s("del",{key:n.key},t(e.children,n))}};!0===t.disableParsingRawHTML&&(delete m[11],delete m[13]);const f=function(e){var t=Object.keys(e);function n(r,a){var o=[];if(a.prevCapture=a.prevCapture||"",r.trim())for(;r;)for(var i=0;i<t.length;){var l=t[i],s=e[l];if(!s.t||Jr(r,a,s.t)){var c=s.o(r,a);if(c&&c[0]){r=r.substring(c[0].length);var u=s.u(c,n,a);a.prevCapture+=c[0],u.type||(u.type=l),o.push(u);break}i++}else i++}return a.prevCapture="",o}return t.sort(function(t,n){return e[t].i-e[n].i||(t<n?-1:1)}),function(e,t){return n(function(e){return e.replace(Wn,"\n").replace(Yn,"").replace(br,"    ")}(e),t)}}(m),g=(h=function(e,t){return function(n,r,a){const o=e[n.type].l;return t?t(()=>o(n,r,a),n,r,a):o(n,r,a)}}(m,t.renderRule),function e(t,n={}){if(Array.isArray(t)){const r=n.key,a=[];let o=!1;for(let r=0;r<t.length;r++){n.key=r;const i=e(t[r],n),l="string"==typeof i;l&&o?a[a.length-1]+=i:null!==i&&a.push(i),o=l}return n.key=r,a}return h(t,e,n)});var h;const b=c(e);return d.length?s("div",null,b,s("footer",{key:"footer"},d.map(function(e){return s("div",{id:n(e.identifier,ea),key:e.identifier},e.identifier,g(f(e.footnote,{inline:!0})))}))):b}const ka=e=>{let{children:t="",options:n}=e,r=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)t.indexOf(n=o[r])>=0||(a[n]=e[n]);return a}(e,Mn);return o.cloneElement(wa(t,n),r)};var xa=n(1729),Sa={};Sa.styleTagTransform=wn(),Sa.setAttributes=bn(),Sa.insert=gn().bind(null,"head"),Sa.domAPI=mn(),Sa.insertStyleElement=vn();dn()(xa.A,Sa);const Ca=xa.A&&xa.A.locals?xa.A.locals:void 0;function Ea({content:e,className:t,forceMarkdown:n=!1,skipMarkdown:r=!1,compact:a=!1}){return r?o.createElement("div",{className:cn(Ca.container,Ca.plainText,a&&Ca.compact,t)},o.createElement("span",{style:{whiteSpace:"pre-wrap"}},e)):o.createElement("div",{className:cn(Ca.container,Ca.markdown,a&&Ca.compact,t)},o.createElement(ka,{options:{overrides:{table:{component:"table",props:{className:Ca.table}},a:{component:"a",props:{className:Ca.link,target:"_blank",rel:"noopener noreferrer"}},pre:{component:"pre",props:{className:Ca.codeBlock}},code:{component:"code",props:{className:Ca.inlineCode}},blockquote:{component:"blockquote",props:{className:Ca.blockquote}},ul:{component:"ul",props:{className:Ca.list}},ol:{component:"ol",props:{className:Ca.orderedList}},p:{component:"p",props:{className:Ca.paragraph,style:{margin:"0.25em 0"}}},h1:{component:"h1",props:{className:Ca.heading1}},h2:{component:"h2",props:{className:Ca.heading2}},h3:{component:"h3",props:{className:Ca.heading3}},h4:{component:"h4",props:{className:Ca.heading4}},h5:{component:"h5",props:{className:Ca.heading5}},h6:{component:"h6",props:{className:Ca.heading6}},hr:{component:"hr",props:{className:Ca.divider}}},forceWrapper:!1,disableParsingRawHTML:!1}},e))}const Ta=qe(["user","system","llm","tool","error","streaming-llm","streaming-tool","thinking"]);Ke({id:Be(),type:Ta,content:Be(),toolName:Be().optional(),toolArgs:Ze().optional(),isComplete:Ue().default(!1),timestamp:je()});function Pa({messages:e,className:t}){const n=e.filter(e=>"error"!==e.type||"Aborted"!==e.content&&"aborted"!==e.content.toLowerCase()&&!e.content.includes("AbortError"));return o.createElement("div",{className:cn(Tn.container,t)},n.map((e,t)=>o.createElement(Ma,{key:e.id,message:e})))}function Ma({message:e}){if(!e.content&&"streaming-tool"!==e.type&&"tool"!==e.type)return o.createElement(o.Fragment,null);const t=()=>"streaming-llm"===e.type?"llm":"streaming-tool"===e.type?"tool":"thinking"===e.type?"system":e.type;return o.createElement("div",{className:cn(Tn.message,Tn[`message--${t()}`],e.isComplete&&Tn["message--complete"],("streaming-llm"===e.type||"streaming-tool"===e.type)&&Tn["message--streaming"])},o.createElement("div",{className:Tn.messageIcon},o.createElement("span",{className:Tn[`${t()}Icon`]},(()=>{switch(e.type){case"user":return"👤";case"system":return"✨";case"thinking":case"llm":case"streaming-llm":return"💭";case"tool":case"streaming-tool":return"🛠️";case"error":return"❌"}})())),o.createElement("div",{className:Tn.messageContent},"tool"!==e.type&&"streaming-tool"!==e.type||!e.toolName?"streaming-llm"===e.type?e.content||e.isComplete?o.createElement("div",{className:Tn.messageText},e.isComplete?o.createElement(Ea,{content:e.content}):o.createElement("div",{className:Tn.streamingContent},o.createElement("pre",{style:{whiteSpace:"pre-wrap",wordBreak:"break-word",fontFamily:"inherit",margin:0,padding:0,background:"transparent",border:"none",fontSize:"inherit",lineHeight:"inherit",color:"inherit"}},e.content),o.createElement("span",{className:Tn.cursor},"|"))):null:o.createElement("div",{className:Tn.messageText},o.createElement(Ea,{content:e.content,skipMarkdown:"user"===e.type})):o.createElement("div",{className:Tn.toolMessage},o.createElement("div",{className:Tn.toolHeader},o.createElement("span",{className:Tn.toolName},e.toolName),e.toolArgs&&o.createElement("span",{className:Tn.toolArgs},(e.toolName,(n=e.toolArgs)?"string"==typeof n?n:n.description?n.description:n.target?n.target:n.text?`"${n.text}"`:n.key?n.key:n.selector?n.selector:"":"")),"streaming-tool"===e.type&&!e.isComplete&&o.createElement("span",{className:Tn.toolStatus},"Working...")),e.content&&o.createElement("div",{className:Tn.toolResult},"streaming-tool"!==e.type||e.isComplete?o.createElement(Ea,{content:e.content,compact:!0}):o.createElement("pre",{className:Tn.streamingContent},e.content)))));var n}var Ia=n(7897),Na={};Na.styleTagTransform=wn(),Na.setAttributes=bn(),Na.insert=gn().bind(null,"head"),Na.domAPI=mn(),Na.insertStyleElement=vn();dn()(Ia.A,Na);const Da=Ia.A&&Ia.A.locals?Ia.A.locals:void 0,La=e=>{let t;const n=new Set,r=(e,r)=>{const a="function"==typeof e?e(t):e;if(!Object.is(a,t)){const e=t;t=(null!=r?r:"object"!=typeof a||null===a)?a:Object.assign({},t,a),n.forEach(n=>n(t,e))}},a=()=>t,o={setState:r,getState:a,getInitialState:()=>i,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{n.clear()}},i=t=e(r,a,o);return o},za=e=>e?La(e):La;var Oa=n(9242);const{useDebugValue:Aa}=o,{useSyncExternalStoreWithSelector:Ra}=Oa;let Fa=!1;const Ba=e=>e;const Ha=e=>{const t="function"==typeof e?za(e):e,n=(e,n)=>function(e,t=Ba,n){n&&!Fa&&(Fa=!0);const r=Ra(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return Aa(r),r}(t,e,n);return Object.assign(n,t),n},Ua=e=>e?Ha(e):Ha;Ke({id:He(),title:Be(),url:Be(),favIconUrl:Be().optional(),active:Ue().optional(),windowId:He().optional()});const ja=e=>e.url.startsWith("http://")||e.url.startsWith("https://"),Za=(Ke({tabId:He(),url:Be(),intents:We(Be()),confidence:He().optional(),timestamp:He(),error:Be().optional()}),Ua((e,t)=>({openTabs:[],currentTabId:null,selectedTabs:[],isCurrentTabRemoved:!1,lastFetch:0,intentPredictions:new Map,async fetchOpenTabs(n,r=!1){const{lastFetch:a}=t(),o=Date.now();if(r||!(o-a<5e3))try{let t;if(n)t=await n();else{t=(await chrome.tabs.query({currentWindow:!0})).map(e=>({id:e.id,title:e.title||"Untitled",url:e.url||"",favIconUrl:e.favIconUrl||void 0,active:e.active,windowId:e.windowId}))}const r=t.filter(ja);let a=null;const i=r.find(e=>e.active);i&&(a=i.id),e({openTabs:r,currentTabId:a,lastFetch:o})}catch(e){}},toggleTabSelection(n){const{selectedTabs:r,currentTabId:a,isCurrentTabRemoved:o}=t(),i=r.includes(n);let l;l=i?r.filter(e=>e!==n):[...r,n];let s=o;null!==a&&n===a&&(s=i),e({selectedTabs:l,isCurrentTabRemoved:s})},clearSelectedTabs(){e({selectedTabs:[],isCurrentTabRemoved:!1})},getContextTabs(){const{openTabs:e,selectedTabs:n,currentTabId:r,isCurrentTabRemoved:a}=t(),o=new Set(n);return null===r||a||o.add(r),e.filter(e=>o.has(e.id))},updateIntentPredictions(t){e(e=>{const n=new Map(e.intentPredictions);return n.set(t.tabId,t),{intentPredictions:n}})},getIntentPredictionsForTab:e=>t().intentPredictions.get(e)||null,clearIntentPredictions(){e({intentPredictions:new Map})}}))),$a=(Ke({isOpen:Ue(),onClose:Ye(),className:Be().optional(),filterQuery:Be().optional()}),({isOpen:e,onClose:t,className:n,filterQuery:r=""})=>{const{openTabs:a,selectedTabs:i,currentTabId:l,isCurrentTabRemoved:s,fetchOpenTabs:c,toggleTabSelection:u,getContextTabs:d}=Za(),[p,m]=(0,o.useState)(0),f=(0,o.useRef)(null),g=(0,o.useRef)(new Map),h=(0,o.useMemo)(()=>{if(!r.trim())return a;const e=r.toLowerCase();return a.filter(t=>{const n=t.title.toLowerCase().includes(e),r=t.url.toLowerCase().includes(e);return n||r})},[a,r]);return(0,o.useEffect)(()=>{m(0)},[h]),(0,o.useEffect)(()=>{e&&(m(0),c())},[e,c]),(0,o.useEffect)(()=>{if(!e||0===h.length)return;const n=e=>{switch(e.key){case"ArrowDown":e.preventDefault(),m(e=>(e+1)%h.length);break;case"ArrowUp":e.preventDefault(),m(e=>(e-1+h.length)%h.length);break;case"Enter":e.preventDefault();const n=h[p];n&&(u(n.id),t());break;case"Escape":e.preventDefault(),t()}};return document.addEventListener("keydown",n),()=>document.removeEventListener("keydown",n)},[e,h,p,u,t]),(0,o.useEffect)(()=>{if(!e||0===h.length)return;const t=h[p];if(t){const e=g.current.get(t.id);e?.scrollIntoView({block:"nearest",behavior:"smooth"})}},[p,h,e]),(0,o.useEffect)(()=>{if(!e)return;const n=e=>{e.target.closest(`.${Da.tabDropdown}`)||t()},r=setTimeout(()=>{document.addEventListener("click",n)},0);return()=>{clearTimeout(r),document.removeEventListener("click",n)}},[e,t]),e?o.createElement("div",{className:cn(Da.tabDropdown,Da.tabDropdownAbove,n),role:"dialog","aria-labelledby":"tab-selector-heading"},o.createElement("div",{className:Da.tabDropdownHeader},o.createElement("h3",{id:"tab-selector-heading",className:Da.tabDropdownTitle},"Browser Tabs (",h.length,")"),o.createElement("button",{className:Da.tabDropdownCloseBtn,onClick:t,"aria-label":"Close tab selector"},"×")),o.createElement("div",{className:Da.tabDropdownContent,ref:f},0===h.length?o.createElement("div",{className:Da.noTabsMessage},a.length>0?"No tabs match your search":"No tabs available"):o.createElement("ul",{className:Da.tabsDropdownList,role:"list"},h.map((e,n)=>{const r=(a=e.id,d().some(e=>e.id===a));var a;const i=e.id===l,s=n===p;return o.createElement("li",{key:e.id,ref:t=>{t?g.current.set(e.id,t):g.current.delete(e.id)},className:cn(Da.tabDropdownItem,r&&Da.selected,i&&Da.currentTab,s&&Da.active),onClick:()=>{u(e.id),t()},role:"option","aria-selected":r},o.createElement("div",{className:Da.tabIcon},e.favIconUrl?o.createElement("img",{src:e.favIconUrl,alt:"",className:Da.tabFavicon}):o.createElement("div",{className:Da.defaultIcon})),o.createElement("div",{className:Da.tabInfo},o.createElement("div",{className:Da.tabTitle},e.title),o.createElement("div",{className:Da.tabUrl},e.url)),i&&o.createElement("span",{className:Da.currentTabIndicator},"Current"),r&&o.createElement("span",{className:Da.selectedIndicator,"aria-label":"Selected"},"✓"))})))):null});var Va=n(5233),Wa={};Wa.styleTagTransform=wn(),Wa.setAttributes=bn(),Wa.insert=gn().bind(null,"head"),Wa.domAPI=mn(),Wa.insertStyleElement=vn();dn()(Va.A,Wa);const Ka=Va.A&&Va.A.locals?Va.A.locals:void 0,Ga=()=>o.createElement("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o.createElement("path",{d:"M18 6 6 18"}),o.createElement("path",{d:"M6 6l12 12"})),Ya=()=>o.createElement("svg",{width:"16",height:"16",viewBox:"0 0 24 24","aria-hidden":"true",focusable:"false"},o.createElement("rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",fill:"currentColor"}),o.createElement("rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",fill:"currentColor"})),Qa=()=>o.createElement("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o.createElement("path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"}),o.createElement("path",{d:"M21 3v5h-5"}),o.createElement("path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"}),o.createElement("path",{d:"M8 16H3v5"})),qa=()=>o.createElement("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o.createElement("rect",{x:"3",y:"11",width:"18",height:"10",rx:"2"}),o.createElement("circle",{cx:"12",cy:"5",r:"2"}),o.createElement("path",{d:"M12 7v4"}),o.createElement("line",{x1:"8",y1:"16",x2:"8",y2:"16"}),o.createElement("line",{x1:"16",y1:"16",x2:"16",y2:"16"})),Xa=()=>o.createElement("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o.createElement("path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}),o.createElement("polyline",{points:"15 3 21 3 21 9"}),o.createElement("line",{x1:"10",y1:"14",x2:"21",y2:"3"})),Ja=(Ke({isOpen:Ue(),onClose:Ye().args().returns(Ve()),className:Be().optional()}),{browse:{title:"🌐 Web Navigation & Automation",description:"I can navigate websites, fill forms, click buttons, and automate complex web tasks",examples:["Open amazon.com and search for wireless headphones under $100","Accept all LinkedIn connection requests on this page","Add this item to my shopping cart and complete the purchase"]},answer:{title:"📊 Data Extraction & Analysis",description:"I can read, analyze, and extract information from any webpage you're viewing",examples:["Summarize this research paper in bullet points","Extract all email addresses from this page","What are the key features mentioned in this product description?"]},productivity:{title:"📑 Tab & Browser Management",description:"I can organize your tabs, manage bookmarks, and help you work more efficiently",examples:["List all tabs in this window","Close all YouTube tabs","Organize my tabs by topic","Save current tabs as 'Work' session","Resume my 'Work' session from yesterday"]}});function eo({isOpen:e,onClose:t,className:n}){const[r,a]=(0,o.useState)("");return(0,o.useEffect)(()=>{const e=chrome.runtime.getManifest();a(e.version||"")},[]),e?o.createElement("div",{className:Ka.overlay,onClick:t},o.createElement("div",{className:`${Ka.modal} ${n||""}`,onClick:e=>e.stopPropagation()},o.createElement("div",{className:Ka.header},o.createElement("div",{className:Ka.headerContent},o.createElement(qa,null),o.createElement("h2",{className:Ka.title},"BrowserOS Agent"),r&&o.createElement("span",{className:Ka.version},"v",r)),o.createElement("button",{onClick:t,className:Ka.closeButton,title:"Close help"},o.createElement(Ga,null))),o.createElement("div",{className:Ka.intro},o.createElement("p",{className:Ka.introText},"I'm your intelligent browser automation assistant. I can navigate websites, extract information, and manage your browsing productivity—all through natural conversation.")),o.createElement("div",{className:Ka.controlsSection},o.createElement("h3",{className:Ka.sectionTitle},"Quick Controls"),o.createElement("div",{className:Ka.controlsGrid},o.createElement("div",{className:Ka.controlItem},o.createElement("button",{className:Ka.controlButton,disabled:!0},o.createElement(Ya,null)),o.createElement("div",{className:Ka.controlInfo},o.createElement("span",{className:Ka.controlLabel},"Pause"),o.createElement("span",{className:Ka.controlDesc},"Stop execution at any time"))),o.createElement("div",{className:Ka.controlItem},o.createElement("button",{className:Ka.controlButton,disabled:!0},o.createElement(Qa,null)),o.createElement("div",{className:Ka.controlInfo},o.createElement("span",{className:Ka.controlLabel},"Reset"),o.createElement("span",{className:Ka.controlDesc},"Start a fresh conversation")))),o.createElement("div",{className:Ka.interruptNote},"💡 ",o.createElement("strong",null,"Pro tip:")," You can interrupt me anytime by typing a new instruction. I'll pause what I'm doing and switch to your new task immediately.")),o.createElement("div",{className:Ka.capabilitiesSection},o.createElement("h3",{className:Ka.sectionTitle},"What I Can Do"),Object.entries(Ja).map(([e,t])=>o.createElement("div",{key:e,className:Ka.agentSection},o.createElement("h4",{className:Ka.agentTitle},t.title),o.createElement("p",{className:Ka.agentDescription},t.description),o.createElement("div",{className:Ka.examplesGrid},t.examples.map((e,t)=>o.createElement("div",{key:t,className:Ka.exampleChip},'"',e,'"')))))),o.createElement("div",{className:Ka.learnMore},o.createElement("a",{href:"https://bit.ly/BrowserOS-setup",target:"_blank",rel:"noopener noreferrer",className:Ka.learnMoreLink},o.createElement(Xa,null),o.createElement("span",null,"View detailed usage guide"))))):null}Ke({DEV_MODE:Ue(),MOCK_LLM_SETTINGS:Ue(),VERSION:Be(),LOG_LEVEL:qe(["info","error","warning","debug"]).default("info")});const to={DEV_MODE:!1,MOCK_LLM_SETTINGS:!1,VERSION:"0.1.0",LOG_LEVEL:"info"};function no(){return to.DEV_MODE}function ro(){return to.MOCK_LLM_SETTINGS}var ao;!function(e){e.NAVIGATE="NAVIGATE",e.CLICK="CLICK",e.EXTRACT="EXTRACT",e.LOG="LOG",e.CONTENT_READY="CONTENT_READY",e.EXECUTE_WORKFLOW="EXECUTE_WORKFLOW",e.WORKFLOW_STATUS="WORKFLOW_STATUS",e.CONNECTION_STATUS="CONNECTION_STATUS",e.EXECUTE_QUERY="EXECUTE_QUERY",e.HEARTBEAT="HEARTBEAT",e.HEARTBEAT_ACK="HEARTBEAT_ACK",e.AGENT_STREAM_UPDATE="AGENT_STREAM_UPDATE",e.CANCEL_TASK="CANCEL_TASK",e.CLOSE_PANEL="CLOSE_PANEL",e.RESET_CONVERSATION="RESET_CONVERSATION",e.GET_TABS="GET_TABS",e.GET_TAB_HISTORY="GET_TAB_HISTORY",e.INTENT_PREDICTION_UPDATED="INTENT_PREDICTION_UPDATED",e.INTENT_BUBBLES_SHOW="INTENT_BUBBLES_SHOW",e.INTENT_BUBBLE_CLICKED="INTENT_BUBBLE_CLICKED"}(ao||(ao={}));const oo=Ke({type:Xe(ao),payload:$e()});Ge("type",[oo.extend({type:Qe(ao.NAVIGATE),payload:Ke({url:Be()})}),oo.extend({type:Qe(ao.CLICK),payload:Ke({selector:Be()})}),oo.extend({type:Qe(ao.LOG),payload:Ke({source:Be(),message:Be(),level:qe(["info","error","warning"]),timestamp:Be()})}),oo.extend({type:Qe(ao.CONTENT_READY),payload:Ke({url:Be(),title:Be()})}),oo.extend({type:Qe(ao.EXECUTE_WORKFLOW),payload:Ke({dsl:Be()})}),oo.extend({type:Qe(ao.WORKFLOW_STATUS),payload:Ke({workflowId:Be(),steps:We(Ke({id:Be(),status:Be(),message:Be().optional(),error:Be().optional()})),output:$e().optional()})}),oo.extend({type:Qe(ao.CONNECTION_STATUS),payload:Ke({connected:Ue(),port:Be().optional()})}),oo.extend({type:Qe(ao.EXECUTE_QUERY),payload:Ke({query:Be(),tabIds:We(He()).optional(),source:Be().optional()})}),oo.extend({type:Qe(ao.HEARTBEAT),payload:Ke({timestamp:He()})}),oo.extend({type:Qe(ao.HEARTBEAT_ACK),payload:Ke({timestamp:He()})}),oo.extend({type:Qe(ao.AGENT_STREAM_UPDATE),payload:Ke({step:He(),action:Be(),status:qe(["thinking","executing","completed","error","debug"]),details:Ke({content:Be().optional(),toolName:Be().optional(),toolArgs:Ze().optional(),toolResult:Be().optional(),error:Be().optional(),messageType:Be().optional(),messageId:Be().optional(),segmentId:He().optional(),data:Ze().optional(),timestamp:Be().optional()})})}),oo.extend({type:Qe(ao.CANCEL_TASK),payload:Ke({reason:Be().optional(),source:Be().optional()})}),oo.extend({type:Qe(ao.CLOSE_PANEL),payload:Ke({reason:Be().optional()})}),oo.extend({type:Qe(ao.RESET_CONVERSATION),payload:Ke({source:Be().optional()})}),oo.extend({type:Qe(ao.GET_TABS),payload:Ke({currentWindowOnly:Ue().default(!0)})}),oo.extend({type:Qe(ao.GET_TAB_HISTORY),payload:Ke({tabId:He(),limit:He().optional().default(5)})}),oo.extend({type:Qe(ao.INTENT_PREDICTION_UPDATED),payload:Ke({tabId:He(),url:Be(),intents:We(Be()),confidence:He().optional(),timestamp:He(),error:Be().optional()})}),oo.extend({type:Qe(ao.INTENT_BUBBLES_SHOW),payload:Ke({intents:We(Be()),confidence:He().optional()})}),oo.extend({type:Qe(ao.INTENT_BUBBLE_CLICKED),payload:Ke({intent:Be()})})]);var io;!function(e){e.OPTIONS_TO_BACKGROUND="options-to-background",e.SIDEPANEL_TO_BACKGROUND="sidepanel-to-background"}(io||(io={}));Xe(io),Ke({type:Xe(ao),payload:$e(),id:Be().optional()});class lo{constructor(){this.port=null,this.listeners=new Map,this.connectionListeners=[],this.connected=!1,this.currentPortName=null,this.heartbeatInterval=null,this.heartbeatIntervalMs=5e3,this.autoReconnect=!1,this.reconnectTimeoutMs=1e3,this.handleIncomingMessage=e=>{const{type:t,payload:n,id:r}=e;if(t===ao.HEARTBEAT_ACK)return;const a=this.listeners.get(t);a&&a.length>0&&a.forEach(e=>e(n,r))},this.handleDisconnect=()=>{this.stopHeartbeat(),this.port=null,this.connected=!1,this.notifyConnectionListeners(!1),this.autoReconnect&&this.attemptReconnect()}}connect(e,t=!1){try{return this.currentPortName=e,this.autoReconnect=t,this.port=chrome.runtime.connect({name:e}),this.port.onMessage.addListener(this.handleIncomingMessage),this.port.onDisconnect.addListener(this.handleDisconnect),this.connected=!0,this.notifyConnectionListeners(!0),this.startHeartbeat(),!0}catch(e){return this.connected=!1,!1}}disconnect(){this.autoReconnect=!1,this.stopHeartbeat(),this.port&&(this.port.disconnect(),this.port=null,this.connected=!1,this.notifyConnectionListeners(!1))}startHeartbeat(){this.stopHeartbeat(),this.heartbeatInterval=window.setInterval(()=>{if(this.connected&&this.port)try{this.sendMessage(ao.HEARTBEAT,{timestamp:Date.now()})}catch(e){}},this.heartbeatIntervalMs)}stopHeartbeat(){null!==this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null)}attemptReconnect(){this.autoReconnect&&this.currentPortName&&setTimeout(()=>{if(!this.connected&&this.currentPortName){this.connect(this.currentPortName,this.autoReconnect)||this.attemptReconnect()}},this.reconnectTimeoutMs)}addMessageListener(e,t){this.listeners.has(e)||this.listeners.set(e,[]);const n=this.listeners.get(e);n&&n.push(t)}removeMessageListener(e,t){const n=this.listeners.get(e);if(n){const e=n.indexOf(t);-1!==e&&n.splice(e,1)}}addConnectionListener(e){this.connectionListeners.push(e),e(this.connected)}removeConnectionListener(e){const t=this.connectionListeners.indexOf(e);-1!==t&&this.connectionListeners.splice(t,1)}sendMessage(e,t,n){if(!this.port||!this.connected)return!1;try{const r={type:e,payload:t,id:n};return this.port.postMessage(r),!0}catch(e){return!1}}isConnected(){return this.connected&&null!==this.port}notifyConnectionListeners(e){this.connectionListeners.forEach(t=>t(e))}}const so=qe(["info","error","warning"]);Ke({source:Be(),message:Be(),level:so,timestamp:Be()});class co{static initialize(e={}){this.debugMode=e.debugMode||!1}static registerPort(e,t){this.connectedPorts.set(e,t)}static unregisterPort(e){this.connectedPorts.delete(e)}static log(e,t,n="info"){if(!this.debugMode&&"info"===n)return;const r={source:e,message:t,level:n,timestamp:(new Date).toISOString()};let a=!1;if(no()){const e=this.connectedPorts.get(io.OPTIONS_TO_BACKGROUND);if(e)try{void 0!==e.name?(e.postMessage({type:ao.LOG,payload:r}),a=!0):this.unregisterPort(io.OPTIONS_TO_BACKGROUND)}catch(e){this.unregisterPort(io.OPTIONS_TO_BACKGROUND),"info"!==n||t.includes("heartbeat")}}!a&&"undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.sendMessage({type:ao.LOG,payload:r}).catch(e=>{})}}co.connectedPorts=new Map,co.debugMode=!1;const uo=qe(["nxtscape","openai","anthropic","gemini","ollama"]),po=Ke({model:Be().optional()}),mo=Ke({apiKey:Be().optional(),model:Be().optional(),baseUrl:Be().optional()}),fo=Ke({apiKey:Be().optional(),model:Be().optional(),baseUrl:Be().optional()}),go=Ke({apiKey:Be().optional(),model:Be().optional(),baseUrl:Be().optional()}),ho=Ke({apiKey:Be().optional(),baseUrl:Be().optional(),model:Be().optional()}),bo=Ke({defaultProvider:uo,nxtscape:po,openai:mo,anthropic:fo,gemini:go,ollama:ho}),yo=(Ke({provider:qe(["openai","anthropic","gemini","ollama"]),model:Be(),apiKey:Be().optional(),baseUrl:Be().url().optional(),useProxy:Ue(),temperature:He().min(0).max(2).optional()}),{DEFAULT_PROVIDER:"nxtscape.default_provider",NXTSCAPE_MODEL:"nxtscape.nxtscape_model",OPENAI_API_KEY:"nxtscape.openai_api_key",OPENAI_MODEL:"nxtscape.openai_model",OPENAI_BASE_URL:"nxtscape.openai_base_url",ANTHROPIC_API_KEY:"nxtscape.anthropic_api_key",ANTHROPIC_MODEL:"nxtscape.anthropic_model",ANTHROPIC_BASE_URL:"nxtscape.anthropic_base_url",GEMINI_API_KEY:"nxtscape.gemini_api_key",GEMINI_MODEL:"nxtscape.gemini_model",GEMINI_BASE_URL:"nxtscape.gemini_base_url",OLLAMA_API_KEY:"nxtscape.ollama_api_key",OLLAMA_BASE_URL:"nxtscape.ollama_base_url",OLLAMA_MODEL:"nxtscape.ollama_model"}),vo={"nxtscape.default_provider":"nxtscape","nxtscape.nxtscape_model":"gpt-4o-mini","nxtscape.openai_api_key":"TBD","nxtscape.openai_model":"gpt-4o","nxtscape.openai_base_url":void 0,"nxtscape.anthropic_api_key":"TBD","nxtscape.anthropic_model":"claude-3-5-sonnet-latest","nxtscape.anthropic_base_url":void 0,"nxtscape.gemini_api_key":"TBD","nxtscape.gemini_model":"gemini-2.0-flash","nxtscape.gemini_base_url":void 0,"nxtscape.ollama_base_url":"http://localhost:11434","nxtscape.ollama_model":"qwen3:4b","nxtscape.ollama_api_key":void 0};class _o{static setMockPreferences(e){ro()?(Object.assign(vo,e),co.log("LLMSettingsReader",`Mock preferences updated: ${JSON.stringify(e)}`)):co.log("LLMSettingsReader","setMockPreferences is only available in development mode","warning")}static async read(){try{co.log("LLMSettingsReader","Reading LLM settings from Chrome preferences");const e=await this.getPreferences(),t={defaultProvider:this.getProviderType(e[yo.DEFAULT_PROVIDER]),nxtscape:{model:e[yo.NXTSCAPE_MODEL]},openai:{apiKey:e[yo.OPENAI_API_KEY],model:e[yo.OPENAI_MODEL],baseUrl:e[yo.OPENAI_BASE_URL]},anthropic:{apiKey:e[yo.ANTHROPIC_API_KEY],model:e[yo.ANTHROPIC_MODEL],baseUrl:e[yo.ANTHROPIC_BASE_URL]},gemini:{apiKey:e[yo.GEMINI_API_KEY],model:e[yo.GEMINI_MODEL],baseUrl:e[yo.GEMINI_BASE_URL]},ollama:{apiKey:e[yo.OLLAMA_API_KEY],baseUrl:e[yo.OLLAMA_BASE_URL],model:e[yo.OLLAMA_MODEL]}},n=bo.parse(t);return co.log("LLMSettingsReader",`Settings loaded successfully. Provider: ${n.defaultProvider}`),n}catch(e){const t=e instanceof Error?e.message:String(e);return co.log("LLMSettingsReader",`Failed to read settings: ${t}`,"error"),{defaultProvider:"nxtscape",nxtscape:{},openai:{},anthropic:{},gemini:{},ollama:{}}}}static async getPreferences(){if("undefined"==typeof chrome||!chrome.settingsPrivate||!chrome.settingsPrivate.getPref)return ro()?(co.log("LLMSettingsReader","Chrome settingsPrivate API not available, using mock values for development","warning"),co.log("LLMSettingsReader",`Mock provider: ${vo["nxtscape.default_provider"]}`),vo):(co.log("LLMSettingsReader","Chrome settingsPrivate API not available, using defaults","warning"),{});const e=Object.entries(yo).map(([e,t])=>new Promise(e=>{chrome.settingsPrivate.getPref(t,n=>{if(chrome.runtime.lastError)co.log("LLMSettingsReader",`Failed to read preference ${t}: ${chrome.runtime.lastError.message}`,"warning"),e([t,void 0]);else{const r=n?.value;e([t,r])}})})),t=await Promise.all(e);return Object.fromEntries(t)}static getProviderType(e){return e&&["nxtscape","openai","anthropic","gemini","ollama"].includes(e)?e:"nxtscape"}static async readPreference(e){return"undefined"!=typeof chrome&&chrome.settingsPrivate&&chrome.settingsPrivate.getPref?new Promise(t=>{chrome.settingsPrivate.getPref(e,n=>{if(chrome.runtime.lastError)co.log("LLMSettingsReader",`Failed to read preference ${e}: ${chrome.runtime.lastError.message}`,"warning"),t(void 0);else{const e=n?.value;t(e)}})}):ro()?(co.log("LLMSettingsReader",`Chrome settingsPrivate API not available, using mock value for ${e}`,"warning"),vo[e]):void co.log("LLMSettingsReader","Chrome settingsPrivate API not available","warning")}}const wo=()=>o.createElement("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o.createElement("path",{d:"m22 2-7 20-4-9-9-4Z"}),o.createElement("path",{d:"M22 2 11 13"})),ko=()=>o.createElement("svg",{width:"16",height:"16",viewBox:"0 0 24 24","aria-hidden":"true",focusable:"false"},o.createElement("rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",fill:"currentColor"}),o.createElement("rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",fill:"currentColor"})),xo=()=>o.createElement("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o.createElement("path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"}),o.createElement("path",{d:"M21 3v5h-5"}),o.createElement("path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"}),o.createElement("path",{d:"M8 16H3v5"})),So=()=>o.createElement("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o.createElement("circle",{cx:"12",cy:"12",r:"10"}),o.createElement("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),o.createElement("path",{d:"M12 17h.01"})),Co=()=>o.createElement("svg",{width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},o.createElement("path",{d:"M21.5 2v6h-6M21.34 15.57a10 10 0 1 1-.57-8.38"})),Eo=()=>o.createElement("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round","aria-hidden":"true",focusable:"false"},o.createElement("path",{d:"M14 2H6a2 2 0 0 0 -2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2V8z"}),o.createElement("polyline",{points:"14 2 14 8 20 8"})),To=["Open amazon.com and order sensodyne toothpaste","Write a tweet saying Hello World","Find top rated headphones under 100$","Extract all news headlines from this page","List all my tabs","Close all my YouTube tabs","Organize my tabs by topic","Summarize this article for me","What are the key points on this page?"],Po=()=>[...To].sort(()=>.5-Math.random()).slice(0,3);Ke({input:Be(),isProcessing:Ue(),isConnected:Ue(),currentQuery:Be().optional(),examples:We(Be()),showHelp:Ue(),showTabSelector:Ue(),selectedTabs:We(He())});function Mo({className:e,onNewTask:t,onCancelTask:n,onReset:r,onClose:a,isConnected:i=!1,isProcessing:l=!1,messages:s=[],externalIntent:c,onExternalIntentHandled:u}){const[d,p]=(0,o.useState)({input:"",isProcessing:l,isConnected:i,currentQuery:void 0,examples:Po(),showHelp:!1,showTabSelector:!1,selectedTabs:[]}),m=d.isProcessing||l||s.some(e=>("streaming-llm"===e.type||"streaming-tool"===e.type)&&!e.isComplete),f=(0,o.useRef)(null),g=(0,o.useRef)(null),[h,b]=(0,o.useState)(!1),[y,v]=(0,o.useState)(null),[_,w]=(0,o.useState)(null),{openTabs:k,selectedTabs:x,currentTabId:S,toggleTabSelection:C,clearSelectedTabs:E,getContextTabs:T,fetchOpenTabs:P,updateIntentPredictions:M}=Za(),I=(0,o.useMemo)(()=>k.filter(e=>x.includes(e.id)),[k,x]);(0,o.useEffect)(()=>{c&&i&&!d.isProcessing&&(p(e=>({...e,input:c})),setTimeout(()=>{i&&!d.isProcessing&&L(c)},200),u?.())},[c,i,d.isProcessing,u]),(0,o.useEffect)(()=>{P();(async()=>{try{const e=(await chrome.tabs.query({currentWindow:!0})).filter(e=>e.url&&e.id&&(e.url.startsWith("http://")||e.url.startsWith("https://")));for(const t of e)if(t.id){const e=`intent_${t.id}`,n=await chrome.storage.session.get(e);n[e]&&M(n[e])}}catch(e){}})();const e=setInterval(()=>{P()},5e3);return()=>clearInterval(e)},[P,M]);const N=()=>{const e=f.current;if(e){e.style.height="auto";const t=Math.max(40,e.scrollHeight);e.style.height=`${t}px`}};(0,o.useEffect)(()=>{p(e=>({...e,isProcessing:l,isConnected:i}))},[l,i]),(0,o.useEffect)(()=>{no()&&_o.read().then(e=>{v(e),w(null)}).catch(e=>{w(e.message||"Failed to load settings")})},[]);(0,o.useEffect)(()=>{const e=setTimeout(()=>{f.current&&(f.current.focus(),f.current.style.height="40px")},100),t=setTimeout(()=>{f.current&&document.activeElement!==f.current&&f.current.focus()},300);return()=>{clearTimeout(e),clearTimeout(t)}},[]),(0,o.useEffect)(()=>{f.current&&!l&&(f.current.focus(),f.current.style.height="40px")},[l]),(0,o.useEffect)(()=>{d.input?N():f.current&&(f.current.style.height="40px")},[d.input]),(0,o.useEffect)(()=>{const e=()=>{requestAnimationFrame(()=>{(()=>{if(g.current){const e=g.current,{scrollTop:t,scrollHeight:n,clientHeight:r}=e;(!h||n-(t+r)<100||s.length<=1)&&(e.scrollTop=n,e.scrollTo({top:n,behavior:"smooth"}))}})()})},t=setTimeout(e,0),n=setTimeout(e,50),r=setTimeout(e,200);return()=>{clearTimeout(t),clearTimeout(n),clearTimeout(r)}},[s,h]),(0,o.useEffect)(()=>{const e=g.current;if(!e)return;let t;const n=()=>{b(!0),t&&clearTimeout(t),t=setTimeout(()=>{b(!1)},2e3);const{scrollTop:n,scrollHeight:r,clientHeight:a}=e;r-(n+a)<10&&b(!1)};return e.addEventListener("scroll",n,{passive:!0}),()=>{e.removeEventListener("scroll",n),t&&clearTimeout(t)}},[]),(0,o.useEffect)(()=>{0===s.length&&b(!1)},[s.length]);const D=e=>{if(e.preventDefault(),d.showTabSelector)return;if(!d.input.trim()||!i)return;const t=d.input.trim();L(t)},L=e=>{if(!e||!i)return;p(e=>({...e,isProcessing:!0}));const n=T().map(e=>e.id);p(t=>({...t,input:"",isProcessing:!0,currentQuery:e,showTabSelector:!1})),E(),t?.(e,n.length>0?n:void 0)},z=()=>{p(e=>({...e,isProcessing:!1,currentQuery:void 0})),n?.()},O=()=>{p(e=>({...e,showHelp:!e.showHelp}))},A=s.length>0||d.isProcessing;return o.createElement("div",{className:cn(Sn.container,e)},o.createElement(eo,{isOpen:d.showHelp,onClose:O}),o.createElement("div",{className:Sn.header},o.createElement("div",{className:Sn.headerLeft},o.createElement("h1",{className:Sn.brandTitle},"Your browser assistant")),o.createElement("div",{className:Sn.headerActions},m&&o.createElement("button",{onClick:z,className:Sn.actionButton,title:"Pause current task (Esc)"},o.createElement(ko,null)),A&&o.createElement("button",{onClick:()=>{p(e=>({...e,input:"",isProcessing:!1,currentQuery:void 0,examples:Po(),showHelp:!1,showTabSelector:!1})),E(),b(!1),f.current&&(f.current.style.height="40px"),r?.()},className:Sn.actionButton,title:"Clear conversation"},o.createElement(xo,null)),o.createElement("button",{onClick:O,className:cn(Sn.actionButton,Sn.helpButton),title:"Show help"},o.createElement(So,null)),o.createElement("button",{onClick:()=>{p(e=>({...e,showTabSelector:!e.showTabSelector}))},className:Sn.actionButton,title:"Select tabs"},o.createElement(Eo,null)))),no()&&o.createElement("div",{className:Sn.debugBox},o.createElement("details",{className:Sn.debugDetails},o.createElement("summary",{className:Sn.debugSummary},"🔧 Debug: LLM Settings",o.createElement("span",{className:Sn.debugProvider},"(",y?.defaultProvider||"loading...",")"),o.createElement("button",{className:Sn.debugRefreshButton,onClick:e=>{e.stopPropagation(),e.preventDefault(),v(null),_o.read().then(e=>{v(e),w(null)}).catch(e=>{w(e.message||"Failed to load settings")})},title:"Refresh settings"},o.createElement(Co,null))),o.createElement("div",{className:Sn.debugContent},_?o.createElement("div",{className:Sn.debugError},"❌ Error: ",_):y?o.createElement("div",{className:Sn.debugSettings},o.createElement("div",{className:Sn.debugSection},o.createElement("strong",null,"Default Provider:")," ",y.defaultProvider),o.createElement("div",{className:Sn.debugSection},o.createElement("div",{className:Sn.debugSectionTitle},"Nxtscape:"),o.createElement("div",{className:Sn.debugItem},o.createElement("strong",null,"Model:")," ",y.nxtscape.model||"not set")),o.createElement("div",{className:Sn.debugSection},o.createElement("div",{className:Sn.debugSectionTitle},"OpenAI:"),o.createElement("div",{className:Sn.debugItem},o.createElement("strong",null,"API Key:")," ",y.openai.apiKey?"TBD"===y.openai.apiKey?"TBD (mock)":"***"+y.openai.apiKey.slice(-4):"not set"),o.createElement("div",{className:Sn.debugItem},o.createElement("strong",null,"Model:")," ",y.openai.model||"not set")),o.createElement("div",{className:Sn.debugSection},o.createElement("div",{className:Sn.debugSectionTitle},"Anthropic:"),o.createElement("div",{className:Sn.debugItem},o.createElement("strong",null,"API Key:")," ",y.anthropic.apiKey?"TBD"===y.anthropic.apiKey?"TBD (mock)":"***"+y.anthropic.apiKey.slice(-4):"not set"),o.createElement("div",{className:Sn.debugItem},o.createElement("strong",null,"Model:")," ",y.anthropic.model||"not set")),o.createElement("div",{className:Sn.debugSection},o.createElement("div",{className:Sn.debugSectionTitle},"Ollama:"),o.createElement("div",{className:Sn.debugItem},o.createElement("strong",null,"Base URL:")," ",y.ollama.baseUrl||"not set"),o.createElement("div",{className:Sn.debugItem},o.createElement("strong",null,"Model:")," ",y.ollama.model||"not set"),o.createElement("div",{className:Sn.debugItem},o.createElement("strong",null,"API Key:")," ",y.ollama.apiKey?"***"+y.ollama.apiKey.slice(-4):"not set")),"nxtscape"===y.defaultProvider&&o.createElement("div",{className:Sn.debugNote},"ℹ️ Using Nxtscape proxy - no API key required")):o.createElement("div",{className:Sn.debugLoading},"Loading settings...")))),o.createElement("div",{className:Sn.mainContent},A?o.createElement("div",{className:Sn.messageArea,ref:g},o.createElement(Pa,{messages:s,className:Sn.messageDisplay})):o.createElement("div",{className:Sn.welcomeState},o.createElement("h2",{className:Sn.welcomeTitle},"What can I help you with?"),o.createElement("div",{className:Sn.examplesGrid},d.examples.map((e,t)=>o.createElement("button",{key:t,className:Sn.exampleCard,onClick:()=>(e=>{p(t=>({...t,input:e})),f.current?.focus(),setTimeout(N,0)})(e),disabled:!i},o.createElement("span",{className:Sn.exampleText},e)))))),o.createElement("div",{className:Sn.inputSection},I.length>0&&o.createElement("div",{className:Sn.selectedTabsContainer},I.map(e=>o.createElement("div",{key:e.id,className:Sn.selectedTabPill},e.favIconUrl&&o.createElement("img",{src:e.favIconUrl,alt:"",className:Sn.tabIconSmall}),o.createElement("span",{className:Sn.selectedTabTitle},e.title),o.createElement("button",{type:"button",className:Sn.removeTabBtn,onClick:()=>{return t=e.id,void C(t);var t},"aria-label":"Remove tab"},"×")))),d.showTabSelector&&o.createElement("div",{className:Sn.tabSelectorWrapper},o.createElement($a,{isOpen:d.showTabSelector,onClose:()=>{p(e=>{let t=e.input;const n=[...e.input.matchAll(/(^@|\s@)/g)];if(n.length>0){const r=n[n.length-1],a=r.index+(r[0].startsWith(" ")?1:0);t=e.input.slice(0,a)+e.input.slice(e.input.length)}return{...e,showTabSelector:!1,input:t}}),f.current?.focus()},className:Sn.tabSelectorDropdown,filterQuery:(()=>{const e=d.input.lastIndexOf("@");return-1!==e?d.input.slice(e+1):""})()})),o.createElement("form",{onSubmit:D,className:Sn.inputForm},o.createElement("div",{className:Sn.inputWrapper},o.createElement("textarea",{ref:f,value:d.input,onChange:e=>{const t=e.target.value;p(e=>({...e,input:t}));if("@"===t.slice(-1)&&!d.showTabSelector){const e=t.slice(0,-1);(""===e||e.endsWith(" "))&&p(e=>({...e,showTabSelector:!0}))}""===t&&d.showTabSelector&&p(e=>({...e,showTabSelector:!1}))},onKeyDown:e=>{if("Enter"===e.key&&!e.shiftKey){if(d.showTabSelector)return void e.preventDefault();if(e.preventDefault(),m){const e=d.input.trim();z(),e&&(p(e=>({...e,isProcessing:!0})),setTimeout(()=>{L(e)},300))}else D(e)}"Escape"===e.key&&(d.showTabSelector?(p(e=>({...e,showTabSelector:!1})),f.current?.focus()):m&&z())},placeholder:l||d.isProcessing?"Feel free to interrupt my task if you have updated instructions":"Ask me to do something...",className:Sn.inputField,disabled:!i,rows:1,style:{height:"40px"}}),o.createElement("button",{type:"submit",className:cn(Sn.sendButton,i&&d.input.trim()?Sn.sendButtonEnabled:Sn.sendButtonDisabled),disabled:!i||!d.input.trim(),title:"Send message"},o.createElement(wo,null))),o.createElement("div",{className:Sn.helpText},l||d.isProcessing?"Press Enter twice to interrupt • Shift+Enter for new line":"Press Enter to send • Shift+Enter for new line"))))}const Io=Ua(e=>({taskInput:"",setTaskInput:t=>e({taskInput:t}),logs:[],addLog:t=>e(e=>({logs:[...e.logs,t].slice(-1e3)})),clearLogs:()=>e({logs:[]}),isExecuting:!1,startExecution:()=>e({isExecuting:!0}),stopExecution:()=>e({isExecuting:!1}),executionResult:null,setExecutionResult:t=>e({executionResult:t,isExecuting:"running"===t?.status})}));Ke({isOpen:Ue(),isCollapsed:Ue(),currentTab:qe(["tasks","history","settings"]),width:He().min(300).max(800),hasUnreadNotifications:Ue(),lastActivity:je().optional()});Ke({isVisible:Ue(),hasUnreadNotifications:Ue(),lastActivity:je().optional(),isProcessing:Ue(),messages:We(Ze()),currentSegmentId:He()});var No=n(873),Do={};Do.styleTagTransform=wn(),Do.setAttributes=bn(),Do.insert=gn().bind(null,"head"),Do.domAPI=mn(),Do.insertStyleElement=vn();dn()(No.A,Do);No.A&&No.A.locals&&No.A.locals;var Lo=n(890),zo={};zo.styleTagTransform=wn(),zo.setAttributes=bn(),zo.insert=gn().bind(null,"head"),zo.domAPI=mn(),zo.insertStyleElement=vn();dn()(Lo.A,zo);Lo.A&&Lo.A.locals&&Lo.A.locals;i.createRoot(document.getElementById("root")).render(o.createElement(o.StrictMode,null,o.createElement(function({onClose:e}){const{taskInput:t,setTaskInput:n,addLog:r,startExecution:a,executionResult:i,setExecutionResult:l}=Io(),{connected:s,sendMessage:c,addMessageListener:u,removeMessageListener:d}=function(){const e=(0,o.useRef)(new lo),[t,n]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{const t=e.current,r=e=>{n(e)};return t.addConnectionListener(r),t.connect(io.SIDEPANEL_TO_BACKGROUND,!0),()=>{t.removeConnectionListener(r),t.disconnect()}},[]),{connected:t,sendMessage:(t,n,r)=>e.current.sendMessage(t,n,r),addMessageListener:(t,n)=>{e.current.addMessageListener(t,n)},removeMessageListener:(t,n)=>{e.current.removeMessageListener(t,n)}}}(),[p,m]=(0,o.useState)({isVisible:!0,hasUnreadNotifications:!1,isProcessing:!1,messages:[],currentSegmentId:0}),f=(0,o.useRef)(0),g=(0,o.useRef)({messageId:"",content:"",timer:null}),h=()=>`msg-${Date.now()}-${++f.current}`,b=()=>{if(!g.current.content||!g.current.messageId)return;const e=g.current.messageId,t=g.current.content;m(n=>{const r=[...n.messages],a=r.findIndex(t=>t.id===e&&"streaming-llm"===t.type);return-1!==a&&(r[a]={...r[a],content:r[a].content+t}),{...n,messages:r}}),g.current={messageId:"",content:"",timer:null}};(0,o.useEffect)(()=>{const e=e=>{const{step:t,action:n,details:r}=e;if("SystemMessage"!==r?.messageType||r.content&&!r.content.includes("🚀 Initializing browser agent..."))if("SystemMessage"===r?.messageType){const e={id:h(),type:"system",content:r.content||n,isComplete:!0,timestamp:new Date};m(t=>({...t,messages:[...t.messages.filter(e=>"thinking"!==e.type),e]}))}else if("NewSegment"===r?.messageType){const e={id:r.messageId||h(),type:"streaming-llm",content:"",isComplete:!1,timestamp:new Date};m(t=>({...t,messages:[...t.messages.filter(e=>"thinking"!==e.type),e]}))}else if("StreamingChunk"===r?.messageType)g.current.messageId===r.messageId?g.current.content+=r.content:(b(),g.current={messageId:r.messageId,content:r.content,timer:null}),g.current.timer&&clearTimeout(g.current.timer),g.current.timer=setTimeout(()=>{b()},50);else if("FinalizeSegment"===r?.messageType)b(),m(e=>{const t=[...e.messages],n=t.findIndex(e=>e.id===r.messageId&&"streaming-llm"===e.type);if(-1!==n){const e=t[n],a=r.content||e.content;a&&a.trim()?t[n]={...e,type:"llm",content:a,isComplete:!0}:t.splice(n,1)}else r.content&&r.content.trim()&&t.push({id:r.messageId||h(),type:"llm",content:r.content,isComplete:!0,timestamp:new Date});return{...e,messages:t}});else if("ToolStart"===r?.messageType);else if("ToolStream"===r?.messageType);else if("ToolEnd"===r?.messageType);else if("ToolResult"===r?.messageType){const e={id:h(),type:"tool",content:r.content||"",toolName:r.toolName,isComplete:!0,timestamp:new Date};m(t=>({...t,messages:[...t.messages,e]}))}else if("LLMResponse"===r?.messageType);else if("ErrorMessage"===r?.messageType){const e={id:h(),type:"system",content:`❌ ${r.error||r.content||"An error occurred"}`,isComplete:!0,timestamp:new Date};m(t=>({...t,messages:[...t.messages.filter(e=>"thinking"!==e.type),e],isProcessing:!1}))}else if("DebugMessage"===r?.messageType){let e=r.content||"";r.data&&(e+=`\n\`\`\`json\n${JSON.stringify(r.data,null,2)}\n\`\`\``);const t={id:h(),type:"system",content:`🐞 **Debug**: ${e}`,isComplete:!0,timestamp:new Date};m(e=>({...e,messages:[...e.messages.filter(e=>"thinking"!==e.type),t]}))}else if("CancelMessage"===r?.messageType){const e={id:h(),type:"system",content:r.content||"✋ Task paused",isComplete:!0,timestamp:new Date};m(t=>({...t,messages:[...t.messages.filter(e=>"thinking"!==e.type),e],isProcessing:!1}))}else if("TaskResult"===r?.messageType){const e={id:h(),type:"system",content:r.content||"",isComplete:!0,timestamp:new Date};m(t=>({...t,messages:[...t.messages.filter(e=>"thinking"!==e.type),e],isProcessing:!1}))}else"ThinkingMessage"===r?.messageType&&m(e=>{const t=[...e.messages],n=r.data?.category,a=t.filter(e=>"thinking"!==e.type||!!n&&e.category!==n),o={id:h(),type:"thinking",content:`💭 ${r.content}`,isComplete:!0,timestamp:new Date,category:n};return{...e,messages:[...a,o]}})};return u(ao.AGENT_STREAM_UPDATE,e),()=>{g.current.timer&&(clearTimeout(g.current.timer),b()),d(ao.AGENT_STREAM_UPDATE,e)}},[u,d]),(0,o.useEffect)(()=>{const e=e=>{l({status:e.status||(e.error?"failed":"completed"),message:e.message,error:e.error,result:e.result}),("completed"===e.status||"failed"===e.status||e.cancelled)&&m(t=>{const n=[...t.messages];return e.cancelled?n.push({id:h(),type:"system",content:e.message||"✋ Task paused. To continue this task, just type your next request OR use 🔄 to start a new task!",isComplete:!0,timestamp:new Date}):e.error&&!e.cancelled&&n.push({id:h(),type:"error",content:e.error,isComplete:!0,timestamp:new Date}),{...t,messages:n,isProcessing:!1}}),r({source:"SidePanelPage",message:`Task ${e.status||"completed"}: ${e.message||"No message"}`,level:e.error&&!e.cancelled?"error":"info",timestamp:(new Date).toISOString()})};return u(ao.WORKFLOW_STATUS,e),()=>d(ao.WORKFLOW_STATUS,e)},[u,d,l,r,t]),(0,o.useEffect)(()=>{const e=e=>{window.close()};return u(ao.CLOSE_PANEL,e),()=>d(ao.CLOSE_PANEL,e)},[u,d]);const{updateIntentPredictions:y}=Za();(0,o.useEffect)(()=>{const e=e=>{e.error,y({tabId:e.tabId,url:e.url,intents:e.intents,confidence:e.confidence,timestamp:e.timestamp,error:e.error})};return u(ao.INTENT_PREDICTION_UPDATED,e),()=>d(ao.INTENT_PREDICTION_UPDATED,e)},[u,d,y]);const[v,_]=(0,o.useState)(null);(0,o.useEffect)(()=>{const e=e=>{_(e.intent)};return u(ao.INTENT_BUBBLE_CLICKED,e),()=>d(ao.INTENT_BUBBLE_CLICKED,e)},[u,d]);const w=()=>{m(e=>({...e,isProcessing:!1}));const e=`cancel-task-${Date.now()}`;c(ao.CANCEL_TASK,{reason:"User requested cancellation from sidepanel",source:"sidepanel"},e)?r({source:"SidePanelPage",message:"Cancellation request sent to background script",level:"info",timestamp:(new Date).toISOString()}):(r({source:"SidePanelPage",message:"Failed to send cancellation request",level:"warning",timestamp:(new Date).toISOString()}),m(e=>({...e,messages:[...e.messages,{id:h(),type:"error",content:"Failed to cancel task. The task may still be running.",isComplete:!0,timestamp:new Date}]}))),l({status:"cancelled",message:"Task cancellation requested"}),r({source:"SidePanelPage",message:"Task cancellation completed",level:"info",timestamp:(new Date).toISOString()})},k=()=>{m(e=>({...e,isVisible:!0,hasUnreadNotifications:!1}))};return o.createElement("div",{className:"w-full h-full",onFocus:k,onClick:k},o.createElement(Mo,{onNewTask:async(e,t)=>{if(!e.trim()||!s)return;let o=e;try{n(e),a();const i=[];if(t&&t.length>0)try{const n=(await chrome.tabs.query({})).filter(e=>t.includes(e.id));if(1===n.length){const t=n[0];o=`${e}\n\n📍 *Operating on: ${t.title||"Untitled"}*`}else n.length>1&&(o=`${e}\n\n📑 *Operating on ${n.length} selected tabs:*\n`,n.forEach((e,t)=>{o+=`${t+1}. ${e.title||"Untitled"}\n`}))}catch(n){o=`${e}\n\n📑 *Operating on ${t.length} selected tab${t.length>1?"s":""}*`}i.push({id:h(),type:"user",content:o,isComplete:!0,timestamp:new Date}),m(e=>{e.messages.length;return{...e,isProcessing:!0,messages:[...e.messages,...i],currentSegmentId:0,lastActivity:new Date}}),r({source:"SidePanelPage",message:`Processing task: "${e}"${t?` with ${t.length} selected tabs`:""}`,level:"info",timestamp:(new Date).toISOString()});const l=`sidepanel-task-${Date.now()}`;if(!c(ao.EXECUTE_QUERY,{query:e,source:"sidepanel",tabIds:t},l))throw new Error("Failed to send message to background script")}catch(e){const t=e instanceof Error?e.message:String(e),n={id:h(),type:"user",content:o,isComplete:!0,timestamp:new Date},a={id:h(),type:"error",content:`Error: ${t}`,isComplete:!0,timestamp:new Date};m(e=>({...e,isProcessing:!1,messages:[...e.messages,n,a]})),r({source:"SidePanelPage",message:`Error starting task: ${t}`,level:"error",timestamp:(new Date).toISOString()}),l({status:"failed",error:t})}},onCancelTask:w,onReset:()=>{p.isProcessing&&w(),m(e=>({...e,messages:[],isProcessing:!1,currentSegmentId:0})),l(null),n("");const e=`reset-conversation-${Date.now()}`,t=c(ao.RESET_CONVERSATION,{source:"sidepanel"},e);r(t?{source:"SidePanelPage",message:"Reset request sent to background script - clearing conversation history",level:"info",timestamp:(new Date).toISOString()}:{source:"SidePanelPage",message:"Failed to send reset request",level:"warning",timestamp:(new Date).toISOString()}),r({source:"SidePanelPage",message:"Conversation reset completed",level:"info",timestamp:(new Date).toISOString()})},onClose:()=>{m(e=>({...e,isVisible:!1})),e?.()},isConnected:s,isProcessing:p.isProcessing,messages:p.messages,externalIntent:v,onExternalIntentHandled:()=>_(null),className:"h-full"}))},null)))})();