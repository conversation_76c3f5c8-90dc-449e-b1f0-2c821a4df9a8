/* XPM */
static char *product_logo_32[] = {
/* columns rows colors chars-per-pixel */
"32 32 217 2 ",
"   c None",
".  c #EB5818",
"X  c #EB5A1B",
"o  c #EC5918",
"O  c #EC5919",
"+  c #EE5A18",
"@  c #EE5A19",
"#  c #EE5B19",
"$  c #EA5A1C",
"%  c #EF5D1E",
"&  c #EE5E1E",
"*  c #EE5E1F",
"=  c #EF5F1F",
"-  c #F15C1A",
";  c #F15D1B",
":  c #F25D1B",
">  c #F25E1B",
",  c #F35E1B",
"<  c #F25F1B",
"1  c #F35F1B",
"2  c #F45F1B",
"3  c #F15D1C",
"4  c #F05E1D",
"5  c #F15F1D",
"6  c #F25F1D",
"7  c #F05F1F",
"8  c #F45F1C",
"9  c #F4601B",
"0  c #F2621F",
"q  c #F5601C",
"w  c #F5611C",
"e  c #F8621E",
"r  c #F8631E",
"t  c #F9631E",
"y  c #F8641E",
"u  c #FA641F",
"i  c #FA651F",
"p  c #FB651F",
"a  c #EC5F21",
"s  c #F05F20",
"d  c #ED6223",
"f  c #EF672A",
"g  c #F06223",
"h  c #F36222",
"j  c #F06526",
"k  c #F06627",
"l  c #F56625",
"z  c #FB6621",
"x  c #FB6722",
"c  c #FB6723",
"v  c #FB6823",
"b  c #FB6924",
"n  c #FB6A27",
"m  c #F3692B",
"M  c #F1692C",
"N  c #F06A2E",
"B  c #F76C2D",
"V  c #FB6C29",
"C  c #FB6C2A",
"Z  c #FB6D2A",
"A  c #FB6E2C",
"S  c #EF6C33",
"D  c #EF6D34",
"F  c #F06C32",
"G  c #F36E32",
"H  c #F26F35",
"J  c #F46F34",
"K  c #EE7139",
"L  c #ED733D",
"P  c #EF723C",
"I  c #FB7030",
"U  c #FB7130",
"Y  c #FB7436",
"T  c #FB7536",
"R  c #FB7537",
"E  c #F1763E",
"W  c #F1773E",
"Q  c #F1763F",
"!  c #F4753C",
"~  c #F87539",
"^  c #F17842",
"/  c #F47940",
"(  c #F27A44",
")  c #F27C46",
"_  c #F27C48",
"`  c #F17D4A",
"'  c #F37F4A",
"]  c #F88047",
"[  c #FC8147",
"{  c #F2804E",
"}  c #F5824E",
"|  c #F8824C",
" . c #F9824C",
".. c #FC844D",
"X. c #FC854E",
"o. c #FC854F",
"O. c #FC864E",
"+. c #FC874F",
"@. c #EE8250",
"#. c #F08250",
"$. c #F48351",
"%. c #F78751",
"&. c #F38655",
"*. c #F68654",
"=. c #F1895B",
"-. c #F38C5E",
";. c #F78D5D",
":. c #FC8D59",
">. c #FC8F5C",
",. c #FC905E",
"<. c #F58F61",
"1. c #F78F60",
"2. c #F78F61",
"3. c #F59062",
"4. c #F39166",
"5. c #F59064",
"6. c #F59266",
"7. c #F79467",
"8. c #F69468",
"9. c #F5966B",
"0. c #FD996B",
"q. c #FC9A6C",
"w. c #F59B72",
"e. c #F79F77",
"r. c #FC9D71",
"t. c #FCA074",
"y. c #FDA176",
"u. c #FDA277",
"i. c #FDA279",
"p. c #FDA379",
"a. c #FDA67F",
"s. c #F4A480",
"d. c #F7A682",
"f. c #FAA881",
"g. c #FBA882",
"h. c #F8A884",
"j. c #F8A986",
"k. c #FAAB87",
"l. c #FDAA84",
"z. c #F2AA89",
"x. c #FDB08D",
"c. c #ECB197",
"v. c #F8B495",
"b. c #F9B596",
"n. c #FAB596",
"m. c #FDB796",
"M. c #F7B69A",
"N. c #FDBA9A",
"B. c #FDBA9B",
"V. c #F8BA9E",
"C. c #FDBB9D",
"Z. c #F9BCA0",
"A. c #FABDA1",
"S. c #F9BEA2",
"D. c #F9C0A6",
"F. c #FDC2A6",
"G. c #F8C3AB",
"H. c #FAC3AA",
"J. c #FDC4AA",
"K. c #F9C8B2",
"L. c #FBCDB8",
"P. c #FACDBA",
"I. c #FED0BB",
"U. c #FCD1BD",
"Y. c #FBD4C2",
"T. c #FED5C2",
"R. c #FBD5C4",
"E. c #FAD6C7",
"W. c #FED7C4",
"Q. c #FED7C5",
"!. c #FCD7C6",
"~. c #FED8C6",
"^. c #FEDAC9",
"/. c #FBDCCE",
"(. c #E7DEDA",
"). c #FBDDD0",
"_. c #FBDED0",
"`. c #FCDED0",
"'. c #FEE3D7",
"]. c #FEE4D7",
"[. c #FDE4D9",
"{. c #FDE5DA",
"}. c #FEE7DD",
"|. c #FEE8DD",
" X c #FEE9DF",
".X c #E5E2E0",
"XX c gray90",
"oX c #E7E7E7",
"OX c #EAEAEA",
"+X c #EFEFEF",
"@X c #FEEBE2",
"#X c #FEECE3",
"$X c #FDECE4",
"%X c #FDEDE6",
"&X c #FFEEE6",
"*X c #F7EEEA",
"=X c #FDEFE9",
"-X c #FEF0EA",
";X c #FEF2EC",
":X c #FFF3ED",
">X c #FEF3EE",
",X c gray96",
"<X c #FEF4F0",
"1X c #FFF6F2",
"2X c #FEF6F3",
"3X c #FEF7F5",
"4X c #FFF9F6",
"5X c #FFF9F7",
"6X c gray98",
"7X c #FFFBF9",
"8X c #FCFAFA",
"9X c #FFFDFB",
"0X c gray99",
"qX c #FFFDFD",
"wX c #FFFEFC",
"eX c #FFFEFD",
"rX c white",
/* pixels */
"                                                                ",
"                                                                ",
"        p.o.U p p p p p p p p p p p p p p p p p z U >.          ",
"      >.p p p p p p p p p p p p p p p p p p p p p p p z u.      ",
"    q.p p p p p p p p p p p p p p p p p p p p p p p p p z       ",
"    C p p p p p p p p p p z z p p p p p p p p p p p p p p o.    ",
"    p p p p p p p p p v m.1X4XC.C G.T.u.p p p p p p p p p v     ",
"  u.p p p p p p p p U T.rXrXrXrX#X^.rXrXC.p p p p p p p p p     ",
"  p.p p p p p p ,.T.rXrXrXrXrXrXrXrXrXrXrX[.f.v p p p p p p     ",
"  p.p p p p p o.#XrXrXrXrXrXrXrXrXrXrXrXrXrXrXD.p p p p p p     ",
"  p.p p p p p [ ].rXrXK.rXrXrXrXrXrXrXrXrXrXrXD p p p p p p     ",
"  p.p p p p U :XrXrXM.=.rXrXrXrXrXrXrXrXrXrXrX^.8 p p p p p     ",
"  p.p p p p C.rXrXrXL G.rXrXrXrXrX1XrXrXrXL.L.rXf p p p p p     ",
"  p.p p p p ^.rXrXrXL C.rXrXrXrXe.$ :XrXrXD H rXM 8 p p p p     ",
"  p.p p p p ].rXrXrX4.` rXrXrXrXT.8.rXrXrX[.[.rXE $ p p p p     ",
"  p.p p p C rXrXrXrX[.$ _.rXrXrXrXrXrXrXrXrXrXrX1XS.f.U p p     ",
"  p.p p p x.rXrXrXrXrX=.{ rXrXrXrXrXrXrXrXrXrXI.h $ h f.p p     ",
"  p.p p o.rXrXrXrXrXrX:Xa #XrXrXrXrXrXrXrXrXrX].$ $ 8 S.p p     ",
"  p.p p I.rXrXrXrXrXrXrX&.d.rXrXrXrXT.rXrXrXrXrXT.8 G.m.8 p     ",
"  p.p p #XrXrXrXrXrXrXrXm.&.rXrXrXrX<.^ I.rXrX4X&.E rX=.8 p     ",
"  p.p p ~.rXrXrXrXrXrXrXD.` rXrXrXrXrX8.$ M H s H H <.8 8 8     ",
"  p.p p r.#X=XrXrXrXrXrX4.4.rXrX4XT.rXrX1XL.G. XrX<.8 8 8 8     ",
"  p.p p p ^ L :XrXrXrXS.$ _.rXrXrX&.z.rXrXrXrX1X4.8 8 8 8 8     ",
"  p.p p p 8 $ d 4.f.` $ m.rXrXrXrX*X@.h E { ^ h 8 8 8 8 8 8     ",
"  p.p p p p $ $ $ E e. XrXrXrXrXrXrXOX(.c.8 8 8 8 8 8 8 8 8     ",
"  p.p p p p 9 $ $ #XrXrXrXrXrXrXrXrXrXXXXX8 8 8 8 8 8 8 8 8     ",
"    p p p p p $ f.rXrXrXrXrXrXrXrXrXrXOXXX9 8 8 8 8 8 8 8 h     ",
"    o.p p p p f.rXrXrXrXrXrXrXrXrXrXrXrXXXM 8 8 8 8 8 8 9 [     ",
"      C p v G.rXrXrXrXrXrXrXrXrXrXrXrXrX,X^ 8 8 8 8 9 9 M       ",
"        q. XrXrXrXrXrXrXrXrXrXrXrXrXrXrXrXd.8 8 8 9 9 ~         ",
"                                          _.o.o.o.&.            ",
"                                                                "
};
