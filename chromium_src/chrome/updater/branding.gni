# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chrome_build.gni")

if (is_chrome_branded) {
  import("//chrome/updater/internal/branding_google.gni")
} else {
  browser_name = "BrowserOS"
  browser_product_name = "BrowserOS"
  crash_product_name = "BrowserOSUpdater"
  crash_upload_url = "https://clients2.google.com/cr/staging_report"
  help_center_url = "http://support.google.com/installer/"
  app_logo_url = "https://dl.google.com/update2/installers/icons/"
  keystone_app_name = "BrowserOSSoftwareUpdate"
  keystone_bundle_identifier = "com.browseros.Keystone"
  mac_browser_bundle_identifier = "com.browseros.BrowserOS"
  mac_updater_bundle_identifier = "com.browseros.BrowserOSUpdater"
  privileged_helper_bundle_name = "BrowserOSUpdaterPrivilegedHelper"
  privileged_helper_name = "com.browseros.BrowserOS.UpdaterPrivilegedHelper"
  updater_company_full_name = "BrowserOS"
  updater_company_short_name = "BrowserOS"
  updater_company_short_name_lowercase = "browseros"
  updater_company_short_name_uppercase = "BROWSEROS"
  updater_copyright =
      "Copyright 2020 BrowserOS. All rights reserved."
  updater_product_full_name = "BrowserOSUpdater"
  updater_product_full_name_dashed_lowercase = "browseros-updater"
  updater_product_full_display_name = "BrowserOS Updater"
  updater_metainstaller_name = "BrowserOS Installer"
  mac_team_identifier = "PLACEHOLDER"
  updater_appid = "{6e8ffa8f-e7e2-4000-9884-589283c27015}"
  qualification_appid = "{43f3a046-04b3-4443-a770-d67dae90e440}"
  legacy_service_name_prefix = "cupdate"
  prefs_access_mutex = "{A6B9ECD5-772A-4D3F-BFEB-CF9340534A3E}"
  setup_mutex_prefix = "{25569F82-3B67-4185-8127-88E4CF803680}"
  grdfile_name = "browseros_strings"
  extra_args_is_chrome_branded = "IS_CHROME_BRANDED=False"

  UpdaterLegacyLibGUID = "4C61BB05-94D1-4BAB-B69C-C34195AF92CA"
  GoogleUpdate3WebUserClassGUID = "75828ED1-7BE8-45D0-8950-AA85CBF74510"
  GoogleUpdate3WebSystemClassGUID = "283209B7-C761-41CA-BE8D-B5321CD78FD6"
  GoogleUpdate3WebServiceClassGUID = "B52C8B56-9541-4B78-9B2F-665366B78A9C"
  PolicyStatusUserClassGUID = "4DAC24AB-B340-4B7E-AD01-1504A7F59EEA"
  PolicyStatusSystemClassGUID = "83FE19AC-72A6-4A72-B136-************"
  ProcessLauncherClassGUID = "811A664F-703E-407C-A323-E6E31D1EFFA0"

  IAppVersionWebGUID = "3057E1F8-2498-4C19-99B5-F7F207DA4DC7"
  IAppVersionWebUserGUID = "F4874A57-8C88-4B1D-AAB8-4B06CF98FB29"
  IAppVersionWebSystemGUID = "4AF986BF-DD14-45C2-8228-2541622699CD"

  ICurrentStateGUID = "BE5D3E90-A66C-4A0A-9B7B-1A6B9BF3971E"
  ICurrentStateUserGUID = "AE097E2A-7F4B-4248-8698-D6E515E9C638"
  ICurrentStateSystemGUID = "E0425C1F-4263-4BA5-9328-423470344FC0"

  IGoogleUpdate3WebGUID = "027234BD-61BB-4F5C-9386-7FE804171C8C"
  IGoogleUpdate3WebUserGUID = "E2C00808-83C7-4CE4-8075-7E185A90AE04"
  IGoogleUpdate3WebSystemGUID = "B7DA4837-09FF-4355-BFE1-30598E40F41A"

  IAppBundleWebGUID = "D734C877-21F4-496E-B857-3E5B2E72E4CC"
  IAppBundleWebUserGUID = "EA08425E-054B-4564-B0A0-8BAAB76F38E9"
  IAppBundleWebSystemGUID = "D45CFCE3-5297-4D0B-925E-1924A32A7452"

  IAppWebGUID = "2C6218B9-088D-4D25-A4F8-************"
  IAppWebUserGUID = "2C320333-21CF-4FC8-BF8F-4287DB3795B7"
  IAppWebSystemGUID = "8CA0CD21-6864-4BD8-BF0F-2D69AF3A0FE8"

  IAppCommandWebGUID = "87DBF75E-F590-4802-93FD-F8D07800E2E9"
  IAppCommandWebUserGUID = "F8D4B43D-7D22-47FE-B6AD-BAC5ED8A1659"
  IAppCommandWebSystemGUID = "DF735BF0-0265-4D65-BABA-E244D428EFC5"

  IPolicyStatusGUID = "7D908375-C9D0-44C5-BB98-206F3C24A74C"
  IPolicyStatusUserGUID = "19309C09-C93C-45DA-8C02-9A8B8198A5E0"
  IPolicyStatusSystemGUID = "F8197F77-DE9C-4BB0-9F63-459A001154C6"

  IPolicyStatus2GUID = "9D31EA63-2E06-4D41-98C7-CB1F307DB597"
  IPolicyStatus2UserGUID = "1163C9E6-6391-46E2-96AB-2BCB8907D669"
  IPolicyStatus2SystemGUID = "78A85A33-F541-4A52-A1F2-0BCBA1F1A4F0"

  IPolicyStatus3GUID = "5C674FC1-80E3-48D2-987B-79D9D286065B"
  IPolicyStatus3UserGUID = "847BB70C-47D0-46C2-8C97-9000A1F9AD99"
  IPolicyStatus3SystemGUID = "6F7996CA-5531-4299-BD7F-3C7090520090"

  IPolicyStatus4GUID = "4F08E832-C4AF-4D77-840F-8884083E8324"
  IPolicyStatus4UserGUID = "3C0435E1-B19F-4B7A-8481-D7EBAEBFAF47"
  IPolicyStatus4SystemGUID = "DD9F76CA-FCFB-4F8E-B65E-F7C0703AAFBD"

  IPolicyStatusValueGUID = "47C8886A-A4B5-4F6C-865A-41A207074DFA"
  IPolicyStatusValueUserGUID = "13D172EB-2BFB-4D72-AF10-7D33337B7FC6"
  IPolicyStatusValueSystemGUID = "CFF9AE0F-C5C5-4D53-8394-7602758A52BA"

  IProcessLauncherGUID = "EED70106-3604-4385-866E-6D540E99CA1A"
  IProcessLauncherSystemGUID = "437174A3-B9A5-4835-B896-7DE8C1FE2638"

  IProcessLauncher2GUID = "BAEE6326-C925-4FA4-AFE9-5FA69902B021"
  IProcessLauncher2SystemGUID = "E1C53EEF-2BAB-4420-BDD7-256B91C49D0C"

  if (is_mac) {
    legacy_google_update_appid = keystone_bundle_identifier
  } else {
    legacy_google_update_appid = "{8B2B92A3-1BA2-4154-A89C-DA74C9C505E4}"
  }
}

# Chrome and Chromium share the same endpoints for now.
update_check_url = "https://update.googleapis.com/service/update2/json"
device_management_server_url = "https://m.google.com/devicemanagement/data/api"
