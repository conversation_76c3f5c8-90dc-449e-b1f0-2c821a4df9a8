From e59f207faeaed510abb70a644eacd92b533e45ba Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Tue, 1 Jul 2025 18:18:18 -0700
Subject: [PATCH] pin extension by default

---
 chrome/browser/extensions/extension_management.cc | 10 ++++++++++
 1 file changed, 10 insertions(+)

diff --git a/chrome/browser/extensions/extension_management.cc b/chrome/browser/extensions/extension_management.cc
index ae782891ad341..b23cdb005df86 100644
--- a/chrome/browser/extensions/extension_management.cc
+++ b/chrome/browser/extensions/extension_management.cc
@@ -827,6 +827,16 @@ void ExtensionManagement::Refresh() {
       }
     }
   }
+
+  // Force-pin AI Side Panel
+  // Set toolbar_pin to kForcePinned for AI Side Panel extension
+  // auto* ai_chat_panel_settings = AccessById(extension_misc::kAISidePanelExtensionId);
+  // ai_chat_panel_settings->toolbar_pin = ManagedToolbarPinMode::kForcePinned;
+
+  // Force-pin Bug Reporter
+  // Set toolbar_pin to kForcePinned for Bug Reporter extension
+  auto* bug_reporter_settings = AccessById(extension_misc::kBugReporterExtensionId);
+  bug_reporter_settings->toolbar_pin = ManagedToolbarPinMode::kForcePinned;
 }
 
 bool ExtensionManagement::ParseById(const std::string& extension_id,
-- 
2.49.0

