From bf9470a28e2b18c40599c7e6d5e7f5c592f29c53 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Fri, 11 Jul 2025 08:55:19 -0700
Subject: [PATCH] adding new vector icons

---
 components/vector_icons/BUILD.gn           |  2 +
 components/vector_icons/chat_orange.icon   | 48 +++++++++++++++++++
 components/vector_icons/clash_of_gpts.icon | 56 ++++++++++++++++++++++
 3 files changed, 106 insertions(+)
 create mode 100644 components/vector_icons/chat_orange.icon
 create mode 100644 components/vector_icons/clash_of_gpts.icon

diff --git a/components/vector_icons/BUILD.gn b/components/vector_icons/BUILD.gn
index 39a87dbfb84be..fa3133044763a 100644
--- a/components/vector_icons/BUILD.gn
+++ b/components/vector_icons/BUILD.gn
@@ -56,9 +56,11 @@ if (current_toolchain == default_toolchain) {
       "certificate.icon",
       "certificate_off.icon",
       "chat.icon",
+      "chat_orange.icon",
       "chat_spark.icon",
       "check_circle.icon",
       "checklist.icon",
+      "clash_of_gpts.icon",
       "close.icon",
       "close_chrome_refresh.icon",
       "close_rounded.icon",
diff --git a/components/vector_icons/chat_orange.icon b/components/vector_icons/chat_orange.icon
new file mode 100644
index 0000000000000..255a41ae79134
--- /dev/null
+++ b/components/vector_icons/chat_orange.icon
@@ -0,0 +1,48 @@
+// Copyright 2024 The Chromium Authors
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+CANVAS_DIMENSIONS, 960,
+FILL_RULE_NONZERO,
+MOVE_TO, 240, 560,
+R_H_LINE_TO, 320,
+R_V_LINE_TO, -80,
+H_LINE_TO, 240,
+R_V_LINE_TO, 80,
+CLOSE,
+R_MOVE_TO, 0, -120,
+R_H_LINE_TO, 480,
+R_V_LINE_TO, -80,
+H_LINE_TO, 240,
+R_V_LINE_TO, 80,
+CLOSE,
+R_MOVE_TO, 0, -120,
+R_H_LINE_TO, 480,
+R_V_LINE_TO, -80,
+H_LINE_TO, 240,
+R_V_LINE_TO, 80,
+CLOSE,
+MOVE_TO, 80, 880,
+R_V_LINE_TO, -720,
+R_QUADRATIC_TO, 0, -33, 23.5f, -56.5f,
+QUADRATIC_TO_SHORTHAND, 160, 80,
+R_H_LINE_TO, 640,
+R_QUADRATIC_TO, 33, 0, 56.5f, 23.5f,
+QUADRATIC_TO_SHORTHAND, 880, 160,
+R_V_LINE_TO, 480,
+R_QUADRATIC_TO, 0, 33, -23.5f, 56.5f,
+QUADRATIC_TO_SHORTHAND, 800, 720,
+H_LINE_TO, 240,
+LINE_TO, 80, 880,
+CLOSE,
+R_MOVE_TO, 126, -240,
+R_H_LINE_TO, 594,
+R_V_LINE_TO, -480,
+H_LINE_TO, 160,
+R_V_LINE_TO, 525,
+R_LINE_TO, 46, -45,
+CLOSE,
+R_MOVE_TO, -46, 0,
+R_V_LINE_TO, -480,
+R_V_LINE_TO, 480,
+CLOSE
diff --git a/components/vector_icons/clash_of_gpts.icon b/components/vector_icons/clash_of_gpts.icon
new file mode 100644
index 0000000000000..8573c96085fa8
--- /dev/null
+++ b/components/vector_icons/clash_of_gpts.icon
@@ -0,0 +1,56 @@
+// Copyright 2024 The Chromium Authors
+// Use of this source code is governed by a BSD-style license that can be
+// found in the LICENSE file.
+
+CANVAS_DIMENSIONS, 960,
+FILL_RULE_NONZERO,
+MOVE_TO, 440, 840,
+H_LINE_TO, 200,
+R_QUADRATIC_TO, -33, 0, -56.5f, -23.5f,
+QUADRATIC_TO_SHORTHAND, 120, 760,
+R_V_LINE_TO, -560,
+R_QUADRATIC_TO, 0, -33, 23.5f, -56.5f,
+QUADRATIC_TO_SHORTHAND, 200, 120,
+R_H_LINE_TO, 240,
+R_V_LINE_TO, 720,
+CLOSE,
+R_MOVE_TO, -80, -80,
+R_V_LINE_TO, -560,
+H_LINE_TO, 200,
+R_V_LINE_TO, 560,
+R_H_LINE_TO, 160,
+CLOSE,
+R_MOVE_TO, 160, -320,
+R_V_LINE_TO, -320,
+R_H_LINE_TO, 240,
+R_QUADRATIC_TO, 33, 0, 56.5f, 23.5f,
+QUADRATIC_TO_SHORTHAND, 840, 200,
+R_V_LINE_TO, 240,
+H_LINE_TO, 520,
+CLOSE,
+R_MOVE_TO, 80, -80,
+R_H_LINE_TO, 160,
+R_V_LINE_TO, -160,
+H_LINE_TO, 600,
+R_V_LINE_TO, 160,
+CLOSE,
+R_MOVE_TO, -80, 480,
+R_V_LINE_TO, -320,
+R_H_LINE_TO, 320,
+R_V_LINE_TO, 240,
+R_QUADRATIC_TO, 0, 33, -23.5f, 56.5f,
+QUADRATIC_TO_SHORTHAND, 760, 840,
+H_LINE_TO, 520,
+CLOSE,
+R_MOVE_TO, 80, -80,
+R_H_LINE_TO, 160,
+R_V_LINE_TO, -160,
+H_LINE_TO, 600,
+R_V_LINE_TO, 160,
+CLOSE,
+MOVE_TO, 360, 480,
+CLOSE,
+R_MOVE_TO, 240, -120,
+CLOSE,
+R_MOVE_TO, 0, 240,
+CLOSE
-- 
2.49.0

