From 63fe007641d93e054e7120d61a69804ffb9c0bb0 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Thu, 12 Jun 2025 19:46:08 -0700
Subject: [PATCH] ai chat extension

---
 chrome/browser/browser_resources.grd               |  1 +
 .../component_extensions_allowlist/allowlist.cc    |  2 ++
 chrome/browser/extensions/component_loader.cc      |  3 +++
 chrome/browser/resources/BUILD.gn                  |  1 +
 .../resources/component_extension_resources.grd    | 14 +++++++++++++-
 chrome/common/extensions/extension_constants.h     |  3 +++
 6 files changed, 23 insertions(+), 1 deletion(-)

diff --git a/chrome/browser/browser_resources.grd b/chrome/browser/browser_resources.grd
index 533d97a0d7e02..80f296feed297 100644
--- a/chrome/browser/browser_resources.grd
+++ b/chrome/browser/browser_resources.grd
@@ -238,6 +238,7 @@
         <include name="IDR_CERT_MANAGER_DIALOG_HTML" file="resources\certificate_manager\certificate_manager_dialog.html" type="BINDATA" />
         <include name="IDR_CERT_MANAGER_DIALOG_V2_HTML" file="resources\certificate_manager\certificate_manager_dialog_v2.html" type="BINDATA" />
       </if>
+      <include name="IDR_AI_SIDE_PANEL_MANIFEST" file="resources\ai_side_panel\manifest.json" type="BINDATA" />
     </includes>
   </release>
 </grit>
diff --git a/chrome/browser/extensions/component_extensions_allowlist/allowlist.cc b/chrome/browser/extensions/component_extensions_allowlist/allowlist.cc
index b818225467c01..b2e9219008a54 100644
--- a/chrome/browser/extensions/component_extensions_allowlist/allowlist.cc
+++ b/chrome/browser/extensions/component_extensions_allowlist/allowlist.cc
@@ -31,6 +31,7 @@ bool IsComponentExtensionAllowlisted(const std::string& extension_id) {
   constexpr auto kAllowed = base::MakeFixedFlatSet<std::string_view>({
       extension_misc::kInAppPaymentsSupportAppId,
       extension_misc::kPdfExtensionId,
+      extension_misc::kAISidePanelExtensionId,
 #if BUILDFLAG(IS_CHROMEOS)
       extension_misc::kAssessmentAssistantExtensionId,
       extension_misc::kAccessibilityCommonExtensionId,
@@ -89,6 +90,7 @@ bool IsComponentExtensionAllowlisted(int manifest_resource_id) {
     case IDR_TTS_ENGINE_MANIFEST:
 #endif  // BUILDFLAG(IS_WIN) || BUILDFLAG(IS_LINUX) || BUILDFLAG(IS_MAC)
     case IDR_WEBSTORE_MANIFEST:
+    case IDR_AI_SIDE_PANEL_MANIFEST:  // AI Side Panel Extension
 
 #if BUILDFLAG(IS_CHROMEOS)
     // Separate ChromeOS list, as it is quite large.
diff --git a/chrome/browser/extensions/component_loader.cc b/chrome/browser/extensions/component_loader.cc
index 97868e0d32ffc..12ba5765f1cd3 100644
--- a/chrome/browser/extensions/component_loader.cc
+++ b/chrome/browser/extensions/component_loader.cc
@@ -543,6 +543,9 @@ void ComponentLoader::AddDefaultComponentExtensionsWithBackgroundPages(
        command_line->HasSwitch(
            ::switches::kDisableComponentExtensionsWithBackgroundPages));
 
+  Add(IDR_AI_SIDE_PANEL_MANIFEST,
+      base::FilePath(FILE_PATH_LITERAL("ai_side_panel")));
+
 #if BUILDFLAG(ENABLE_HANGOUT_SERVICES_EXTENSION)
   const bool enable_hangout_services_extension_for_testing =
       command_line->HasSwitch(::switches::kTestType) &&
diff --git a/chrome/browser/resources/BUILD.gn b/chrome/browser/resources/BUILD.gn
index ecc70941cd1f4..b479c135ceffe 100644
--- a/chrome/browser/resources/BUILD.gn
+++ b/chrome/browser/resources/BUILD.gn
@@ -19,6 +19,7 @@ group("resources") {
   public_deps = [
     "saved_tab_groups_unsupported:resources",
     "segmentation_internals:resources",
+    "ai_side_panel:build",
   ]
 
   if (!is_android) {
diff --git a/chrome/browser/resources/component_extension_resources.grd b/chrome/browser/resources/component_extension_resources.grd
index 2e355c8ce7c2f..d0d2bf499ee62 100644
--- a/chrome/browser/resources/component_extension_resources.grd
+++ b/chrome/browser/resources/component_extension_resources.grd
@@ -12,7 +12,19 @@
   </outputs>
   <release seq="1">
     <includes>
-      <include name="IDR_NETWORK_SPEECH_SYNTHESIS_JS" file="network_speech_synthesis/tts_extension.js" type="BINDATA" />
+    <!-- AI Side Panel Extension -->
+    <include name="IDR_AI_SIDE_PANEL_MANIFEST" file="ai_side_panel/manifest.json" type="BINDATA" />
+    <include name="IDR_AI_SIDE_PANEL_BACKGROUND_JS" file="ai_side_panel/background.js" type="BINDATA" />
+    <include name="IDR_AI_SIDE_PANEL_CONTENT_JS" file="ai_side_panel/content.js" type="BINDATA" />
+    <include name="IDR_AI_SIDE_PANEL_SIDEPANEL_HTML" file="ai_side_panel/sidepanel.html" type="BINDATA" />
+    <include name="IDR_AI_SIDE_PANEL_SIDEPANEL_JS" file="ai_side_panel/sidepanel.js" type="BINDATA" />
+    <include name="IDR_AI_SIDE_PANEL_READABILITY_JS" file="ai_side_panel/Readability.js" type="BINDATA" />
+    <include name="IDR_AI_SIDE_PANEL_BUILDDOMTREE_JS" file="ai_side_panel/buildDomTree.js" type="BINDATA" />
+    <include name="IDR_AI_SIDE_PANEL_ICON16" file="ai_side_panel/assets/icon16.png" type="BINDATA" />
+    <include name="IDR_AI_SIDE_PANEL_ICON48" file="ai_side_panel/assets/icon48.png" type="BINDATA" />
+    <include name="IDR_AI_SIDE_PANEL_ICON128" file="ai_side_panel/assets/icon128.png" type="BINDATA" />
+
+     <include name="IDR_NETWORK_SPEECH_SYNTHESIS_JS" file="network_speech_synthesis/tts_extension.js" type="BINDATA" />
 
       <include name="IDR_NETWORK_SPEECH_SYNTHESIS_MV3_AUDIO_HTML" file="network_speech_synthesis/mv3/audio.html" type="BINDATA" />
       <include name="IDR_NETWORK_SPEECH_SYNTHESIS_MV3_AUDIO_JS" file="network_speech_synthesis/mv3/audio.js" type="BINDATA" />
diff --git a/chrome/common/extensions/extension_constants.h b/chrome/common/extensions/extension_constants.h
index 7a46135d8fa99..b73ab6259a19d 100644
--- a/chrome/common/extensions/extension_constants.h
+++ b/chrome/common/extensions/extension_constants.h
@@ -27,6 +27,9 @@ inline constexpr char kLaunchSourceAppListInfoDialog[] =
 }  // namespace extension_urls
 
 namespace extension_misc {
+// The extension id of the AI Side Panel extension.
+inline constexpr char kAISidePanelExtensionId[] =
+    "opocihnfjcgcjecjhjjgifkbkgeeoonh";
 
 // The extension id of the Calendar application.
 inline constexpr char kCalendarAppId[] = "ejjicmeblgpmajnghnpcppodonldlgfn";
-- 
2.49.0

